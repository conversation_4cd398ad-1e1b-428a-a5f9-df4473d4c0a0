apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nginx-ingress-host
  namespace: ape-crm-dev
  annotations:
    kubernetes.io/ingress.class: 'nginx'
spec:
  rules:
    - host: crm-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-crm-admin-dev
                port:
                  number: 80
    - host: crm-website-dev.apetechs.co
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: ape-crm-website-dev
                port:
                  number: 80