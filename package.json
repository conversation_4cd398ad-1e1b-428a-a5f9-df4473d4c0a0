{"name": "admin-web", "version": "0.1.0", "private": true, "scripts": {"start": "craco start --watch", "build": "craco build", "test": "craco test", "dev": "REACT_APP_ENV=dev yarn start", "prod": "REACT_APP_ENV=prod yarn start", "sandbox": "REACT_APP_ENV=sandbox yarn start", "build:dev": "REACT_APP_ENV=dev yarn build", "build:prod": "REACT_APP_ENV=prod yarn build", "build:sandbox": "REACT_APP_ENV=sandbox yarn build", "st": "yarn st-router &&  yarn st-common", "st-router": "yarn add react-router-dom history && yarn add @types/history @types/react-router-dom -D", "st-common": "yarn add axios uuid moment  && yarn add @types/uuid gulp gulp-rename -D", "st-add": "yarn add react-intl @ant-design/icons react-drag-listview react-beautiful-dnd react-transition-group screenfull react-custom-scrollbars"}, "eslintConfig": {"extends": ["react-app"], "rules": {"no-extend-native": 0, "array-callback-return": 0, "@typescript-eslint/no-unused-vars": 0, "no-useless-escape": 0, "react-hooks/exhaustive-deps": 1, "jsx-a11y/anchor-is-valid": 0}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@ant-design/compatible": "^5.1.1", "@ant-design/icons": "^5.0.1", "@fontsource/dejavu-sans": "^5.2.5", "@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@tanstack/react-query": "^5.80.5", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "@types/jest": "^27.0.1", "@types/node": "^16.7.13", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "antd": "^5.3.2", "axios": "0.21.1", "chart.js": "^4.4.9", "chartjs-plugin-datalabels": "^2.2.0", "dayjs": "^1.11.7", "extensionsjs": "^1.1.6", "file-saver": "^2.0.5", "history": "^5.3.0", "html2pdf.js": "^0.10.3", "i18next": "^25.2.1", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "moment": "^2.29.4", "pdfmake": "^0.2.20", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.3.0", "react-custom-scrollbars": "^4.2.1", "react-dom": "^18.2.0", "react-drag-listview": "^2.0.0", "react-i18next": "^15.5.3", "react-icons": "^5.5.0", "react-intl": "^6.3.2", "react-json-view": "^1.21.3", "react-qr-reader": "^3.0.0-beta-1", "react-qr-scanner": "^1.0.0-alpha.11", "react-quill": "^2.0.0", "react-router-dom": "^6.9.0", "react-scripts": "5.0.1", "react-transition-group": "^4.4.5", "recharts": "^2.5.0", "screenfull": "^6.0.2", "typescript": "^4.4.2", "util": "^0.12.5", "uuid": "^9.0.0", "web-vitals": "^2.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@craco/craco": "^7.1.0", "@tanstack/eslint-plugin-query": "^5.78.0", "@types/history": "^5.0.0", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^9.0.1", "craco-less": "^2.0.0", "gulp": "^4.0.2", "gulp-rename": "^2.0.0"}}