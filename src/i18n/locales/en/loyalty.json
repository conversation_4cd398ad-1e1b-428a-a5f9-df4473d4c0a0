{"gift_screen": {"title": "Loyalty Program Management", "create": "Create Loyalty Program", "edit": "Edit Loyalty Program", "detail": "Loyalty Program Details", "list": "Loyalty Program List", "filter": "Filter Loyalty Programs", "search": "Search Loyalty Programs", "columns": {"stt": "No.", "targetAudience": "Target Audience", "programName": "Program Name", "memberLevel": "Member Level", "conversionType": "Conversion Type", "giftValue": "Gift Value", "unit": "Unit", "time": "Time", "startDate": "From Date", "endDate": "To Date", "status": "Status", "action": "Action"}}, "loyalty_screen": {"title": "Loyalty Member Management", "create": "Create Loyalty Member", "edit": "<PERSON> Member", "detail": "Loyalty Member Details", "list": "Loyalty Member List", "filter": "Filter Loyalty Members", "search": "Search Loyalty Members", "columns": {"stt": "No.", "customerCode": "Customer Code", "customerName": "Customer Name", "phoneNumber": "Phone Number", "programCode": "Program Code", "programName": "Program Name", "totalAccumulatedMoney": "Total Accumulated Sales", "exchangedMoney": "Exchanged Sales", "remainingMoney": "Remaining Sales", "status": "Status", "action": "Action"}}, "history_screen": {"title": "Gift Exchange History", "create": "Create Gift Exchange", "edit": "Edit Gift Exchange", "detail": "Gift Exchange Details", "list": "Gift Exchange List", "filter": "Filter Gift Exchanges", "search": "Search Gift Exchanges", "columns": {"stt": "No.", "rewardCode": "Reward Code", "exchangeDate": "Exchange Date", "customerCode": "Customer Code", "customerName": "Customer Name", "phoneNumber": "Phone Number", "programCode": "Program Code", "programName": "Program Name", "memberRank": "Member Rank", "totalAccumulatedMoney": "Total Accumulated Sales", "atp": "ATP", "remainingAtp": "Remaining/ATP", "conversionType": "Conversion Type", "giftValue": "Gift Value", "unit": "Unit", "usedAmount": "Used Amount"}}}