{"customer": {"title": "Customer Management", "create": "Create Customer", "edit": "Edit Customer", "detail": "Customer Details", "list": "Customer List", "filter": "Filter Customers", "search": "Search Customers", "columns": {"stt": "No.", "code": "Code", "name": "Name", "phone": "Phone number", "address": "Address", "customerType": "Customer type", "salesRep": "Sales representative", "department": "Department", "ranking": "Customer ranking", "source": "Customer source", "industry": "Industry", "region": "Region", "createdBy": "Created by", "createdAt": "Created at", "action": "Action"}, "customer_create": {"title": "Create New Customer", "description": "Add new customer to the system", "create": "Create Customer", "cancel": "Cancel", "sections": {"basic_info": "Basic Information", "personal_info": "Personal Information", "address_info": "Address Information", "additional_info": "Additional Information"}, "fields": {"market": "Market", "isTopInvestor": "Top Investor", "customerRank": "Customer Ranking", "activityRating": "Activity Rating", "isProjectInvestor": "Project Investor", "isProjectDesigner": "Project Designer", "isProjectContractor": "Project Contractor", "customerSource": "Customer Source", "branch": "Branch", "fullName": "Full Name", "shortName": "Short Name", "website": "Website", "dateOfBirth": "Date of Birth", "gender": "Gender", "age": "Age", "phone": "Phone Number", "email": "Email", "industry": "Industry", "nationality": "Nationality", "addressType": "Address Type", "region": "Region", "city": "City/Province", "district": "District", "ward": "Ward/Commune", "address": "Address", "area": "Area", "visitDate": "Visit Date", "salesRep": "Sales Representative", "mainProductGroup": "Main Product Group", "transactionYear": "Transaction Year", "signboardCount": "Signboard Count", "productTrustLevel": "Product Trust Level", "interactionLevel": "Interaction Level", "investmentSegment": "Investment Segment", "productAwareness": "Product Awareness", "shoppingHabits": "Shopping Habits", "note": "Note", "images": "Images"}, "placeholders": {"selectRank": "Select ranking", "activityRating": "Activity rating", "selectSource": "Select customer source", "selectBranch": "Select branch", "fullName": "Full name", "shortName": "Short name", "website": "Website", "selectGender": "Select gender", "phone": "Phone number", "email": "Email", "selectIndustry": "Select industry", "selectNationality": "Select nationality", "selectAddressType": "Select address type", "selectRegion": "Select region", "selectCity": "Select city/province", "selectDistrict": "Select district", "selectWard": "Select ward/commune", "address": "Detailed address", "area": "Area", "selectSalesRep": "Select sales representative", "selectProductGroup": "Select product group", "signboardCount": "Quantity", "selectTrustLevel": "Select level", "selectInteractionLevel": "Select level", "selectInvestmentSegment": "Select investment segment", "selectAwareness": "Select awareness source", "note": "Enter note"}, "options": {"market": {"domestic": "Domestic", "international": "International"}, "customerRank": {"A": "Rank A", "B": "Rank B", "C": "Rank C", "D": "Rank D"}, "customerSource": {"website": "Website", "facebook": "Facebook", "google": "Google", "referral": "Referral", "other": "Other"}, "branch": {"hcm": "Ho Chi Minh City", "hn": "<PERSON><PERSON>", "dn": "<PERSON>"}, "gender": {"male": "Male", "female": "Female", "other": "Other"}, "industry": {"retail": "Retail", "wholesale": "Wholesale", "service": "Service", "other": "Other"}, "nationality": {"vn": "Vietnam", "us": "United States", "uk": "United Kingdom", "other": "Other"}, "addressType": {"home": "Home", "office": "Office", "other": "Other"}, "region": {"north": "North", "central": "Central", "south": "South"}, "city": {"hcm": "Ho Chi Minh City", "hn": "<PERSON><PERSON>", "dn": "<PERSON>"}, "district": {"q1": "District 1", "q2": "District 2", "q3": "District 3"}, "ward": {"p1": "Ward 1", "p2": "Ward 2", "p3": "Ward 3"}, "salesRep": {"nv1": "<PERSON><PERSON><PERSON>", "nv2": "Tran Thi B", "nv3": "Le <PERSON> C"}, "productGroup": {"group1": "Group 1", "group2": "Group 2", "group3": "Group 3"}, "trustLevel": {"high": "High", "medium": "Medium", "low": "Low"}, "interactionLevel": {"high": "High", "medium": "Medium", "low": "Low"}, "investmentSegment": {"high": "Premium", "medium": "Mid-range", "low": "Standard"}, "productAwareness": {"website": "Website", "social": "Social Media", "friend": "Friend", "other": "Other"}, "shoppingHabits": {"promotion": "Only buy during promotion campaigns", "noInterest": "Not interested"}}, "checkboxes": {"isTopInvestor": "Is top investor", "isProjectInvestor": "Is project investor", "isProjectDesigner": "Is design unit", "isProjectContractor": "Is main contractor"}, "upload": {"button": "Upload", "imageOnly": "Only image files are allowed!", "sizeLimit": "Image must be smaller than 5MB!"}, "validation": {"required": "Please enter {field}", "selectRequired": "Please select {field}", "emailInvalid": "Invalid email", "createSuccess": "Customer created successfully", "createError": "Failed to create customer"}}, "customer_edit": {"title": "Edit Customer", "description": "Update customer information", "update": "Update Customer", "cancel": "Cancel", "sections": {"basic_info": "Basic Information", "personal_info": "Personal Information", "address_info": "Address Information", "additional_info": "Additional Information"}, "fields": {"market": "Market", "customerRank": "Customer Ranking", "customerSource": "Customer Source", "branch": "Branch", "fullName": "Full Name", "shortName": "Short Name", "website": "Website", "phone": "Phone Number", "email": "Email", "industry": "Industry", "address": "Address", "visitDate": "Visit Date", "salesRep": "Sales Representative", "note": "Note"}, "placeholders": {"selectRank": "Select ranking", "selectSource": "Select customer source", "selectBranch": "Select branch", "fullName": "Full name", "shortName": "Short name", "website": "Website", "phone": "Phone number", "email": "Email", "selectIndustry": "Select industry", "address": "Detailed address", "selectSalesRep": "Select sales representative", "note": "Enter note"}, "options": {"market": {"domestic": "Domestic", "international": "International"}, "customerRank": {"A": "Rank A", "B": "Rank B", "C": "Rank C", "D": "Rank D"}, "customerSource": {"website": "Website", "facebook": "Facebook", "google": "Google", "referral": "Referral", "other": "Other"}, "branch": {"hcm": "Ho Chi Minh City", "hn": "<PERSON><PERSON>", "dn": "<PERSON>"}, "industry": {"retail": "Retail", "wholesale": "Wholesale", "service": "Service", "other": "Other"}, "salesRep": {"nv1": "<PERSON><PERSON><PERSON>", "nv2": "Tran Thi B", "nv3": "Le <PERSON> C"}}, "validation": {"required": "Please enter {field}", "selectRequired": "Please select {field}", "emailInvalid": "Invalid email", "updateSuccess": "Customer updated successfully", "updateError": "Failed to update customer"}}, "customer_detail": {"title": "Customer Details", "description": "Customer Details", "information_tab": {"title": "Customer Details", "sections": {"basic_info": "Basic Information", "company_list": "Company List", "contact_info": "Contact Information", "business_info": "Business Information"}, "fields": {"customerCode": "Customer Code", "sapCode": "SAP Code", "shortName": "Short Name", "fullName": "Full Name", "taxCode": "Tax Code", "customerSource": "Customer Source", "visitDate": "Visit Date", "market": "Market", "customerType": "Customer Type", "customerGroup": "Customer Group", "industry": "Industry", "customerRanking": "Customer Ranking", "note": "Note", "companyCode": "Company Code", "salesRep": "Sales Representative", "phone": "Phone Number", "email": "Email", "website": "Website", "address": "Address", "region": "Region", "department": "Department", "createdBy": "Created By", "createdAt": "Created At", "updatedAt": "Updated At", "status": "Status"}, "placeholders": {"noData": "No data available", "selectStatus": "Select status"}, "options": {"status": {"active": "Active", "inactive": "Inactive", "pending": "Pending"}}}, "contact_tab": {"title": "Contact Information", "columns": {"stt": "No.", "code": "Code", "name": "Name", "position": "Position", "phoneNumber": "Phone number", "email": "Email", "isKeyDecision": "Key decision", "createdBy": "Created by", "createdAt": "Created at"}}, "complaint_tab": {"title": "Complaint Information", "columns": {"stt": "No.", "code": "Code", "description": "Description", "type": "Type", "status": "Status", "supervisor": "Supervisor", "assignedTo": "Assigned to", "startDate": "Start date", "endDate": "End date", "address": "Complaint address"}}, "address_tab": {"title": "Customer Addresses", "columns": {"stt": "No.", "addressType": "Address type", "market": "Market", "area": "Area", "address": "Address", "areaSize": "Area size", "note": "Note", "isMain": "Main address", "createdBy": "Created by", "createdAt": "Created at"}}, "manager_tab": {"title": "Customer Management", "columns": {"stt": "No.", "employeeCode": "Employee code", "salesPerson": "Sales person", "department": "Department", "company": "Company", "createdBy": "Created by", "createdAt": "Created at"}}, "document_tab": {"title": "Customer Documents", "columns": {"stt": "No.", "fileType": "File type", "fileName": "File name", "createdBy": "Created by", "createdAt": "Created at"}}, "revenue_tab": {"title": "Customer Revenue", "tabs": {"averageRevenue": "Average revenue", "cumulativeRevenue": "Cumulative revenue", "growthRevenue": "Revenue growth"}, "columns": {"time": "Time", "revenue": "Revenue", "averageRevenue": "Average revenue", "growth": "Growth"}, "form": {"company": "Company", "time": "Time", "revenueFrom": "Revenue from", "revenueTo": "Revenue to", "viewRevenue": "View revenue"}, "placeholders": {"selectCompany": "Select company", "enterRevenueFrom": "Enter revenue from", "enterRevenueTo": "Enter revenue to"}}, "history_tab": {"title": "Customer Business", "columns": {"stt": "No.", "time": "Time", "operationType": "Operation type", "operationName": "Operation name", "operator": "Operator", "operatorRole": "Operator role", "targetObject": "Target object", "oldValue": "Old value", "newValue": "New value", "ipAddress": "IP address", "deviceInfo": "Device info", "status": "Status", "note": "Note"}}}}, "customer_contact": {"title": "Customer Contact Management", "description": "Add contact to the system", "create": "Create Contact", "edit": "Edit Contact", "detail": "Contact Details", "list": "Contact List", "filter": "Filter Contacts", "search": "Search Contacts", "columns": {"stt": "No.", "contactCode": "Contact Code", "name": "Name", "customerName": "Customer Name", "phone": "Phone number", "email": "Email", "branch": "Branch", "note": "Note", "createdBy": "Created by", "responsible": "Responsible", "createdAt": "Created at", "isSpecialCare": "Special care", "status": "Status", "action": "Action"}, "cancel": "Cancel", "validation": {"required": "Please enter {field}"}, "placeholders": {"enterContactCode": "Enter contact code", "enterName": "Enter name", "enterCustomerName": "Enter customer name", "enterPhone": "Enter phone number", "enterEmail": "Enter email", "enterBranch": "Enter branch", "enterStatus": "Enter status", "enterNote": "Enter note"}}, "customer_not_identify": {"title": "Unidentified Customer Management", "create": "Create Unidentified Customer", "edit": "Edit Unidentified Customer", "detail": "Unidentified Customer Details", "list": "Unidentified Customer List", "filter": "Filter Unidentified Customers", "search": "Search Unidentified Customers", "columns": {"stt": "No.", "source": "Source", "id": "ID", "companyCode": "Company Code", "companyName": "Company Name", "region": "Region", "address": "Address", "city": "City/Province", "district": "District", "ward": "Ward/Commune", "name": "Name", "email": "Email", "phone": "Phone number", "position": "Position", "firstAccessTime": "First Access Time", "accessCount30Days": "Access Count (30 Days)", "action": "Action"}}, "customer_evaluation": {"title": "Customer Evaluation Management", "create": "Create Customer Evaluation", "edit": "Edit Customer Evaluation", "detail": "Customer Evaluation Details", "list": "Customer Evaluation List", "filter": "Filter Customer Evaluations", "search": "Search Customer Evaluations", "columns": {"stt": "No.", "code": "Segment Code", "name": "Segment Name", "customerCount": "Customer Count", "status": "Status", "createdBy": "Created by", "createdAt": "Created at", "action": "Action"}}, "contact_evaluation": {"title": "Contact Evaluation Management", "create": "Create Contact Evaluation", "edit": "Edit Contact Evaluation", "detail": "Contact Evaluation Details", "list": "Contact Evaluation List", "filter": "Filter Contact Evaluations", "search": "Search Contact Evaluations", "columns": {"stt": "No.", "code": "Segment Code", "name": "Segment Name", "customerCount": "Customer Count", "status": "Status", "companyName": "Company", "createdBy": "Created by", "createdAt": "Created at", "action": "Action"}}}