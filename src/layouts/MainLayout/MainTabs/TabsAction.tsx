import type { FC } from 'react'
import { useState } from 'react'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { Button } from 'antd'
import { useLayoutConfig } from '~/stores/layoutConfig'

import CreateProductModal from '~/views/main/Product/components/CreateProductModal'
import CreateComplaintModal from '~/views/main/complaint-management/components/CreateComplaintModal'
import CreateSurveyFeedbackModal from '~/views/main/survey-feedback/components/CreateSurveyModal'
import CreateGiftModal from '~/views/main/loyalty-program/components-gift/CreateGiftModal'
import CreateCustomerModal from '~/views/main/Customer/list-customer/component/CreateCustomerModal'
import CreateCustomerContactModal from '~/views/main/Customer/list-contact/component/CreateCustomerContactModal'
import CreateEvaluationCriteriaModal from '~/views/main/Customer/list-customer-evaluation/component/CreateEvaluationCriteriaModal'
import CreateContactEvaluationCriteriaModal from '~/views/main/Customer/list-contact-evaluation/component/CreateContactEvaluationCriteriaModal'
import { ContractCreateComponent } from '~/views/main/sales-management/sub-screen/contract/components/ContractCreateComponent'
import CreateContractModal from '~/views/main/Settings/Contract/components/CreateContractModal'
import { QuotationTemplateCreate } from '~/views/main/Settings/Quotation/components/QuotationTemplateCreate'
import { TargetCreateComponent } from '~/views/main/Marketing-campaign/sub-screen/target/components/TargetCreateComponent'
import { ContentCreateComponent } from '~/views/main/Marketing-campaign/sub-screen/content/components/ContentCreateComponent'
import { CampaignCreateComponent } from '~/views/main/Marketing-campaign/sub-screen/campaign/components/CampaignCreateComponent'
import { InvoiceCreateComponent } from '~/views/main/sales-management/sub-screen/invoice/components/InvoiceCreateComponent'
import { InvoiceTemplateCreateComponent } from '~/views/main/Settings/Invoice/components/InvoiceTemplateCreate'
import CreateDepartmentModal from '~/views/main/Settings/Department/components/CreateDepartmentModal'
import CreateEmployeeModal from '~/views/main/Settings/Employee/components/CreateEmployeeModal'
import CreateCustomerCriteriaModal from '~/views/main/Settings/CustomerCriteria/components/CreateCustomerCriteriaModal'
import CreateKpiCategoryModal from '~/views/main/KPI/KPICategory/components/CreateKpiCategoryModal'
import CreateKpiGroupModal from '~/views/main/KPI/KPIGroup/components/CreateKpiGroupModal'
import CreateCustomerSupportModal from '~/views/main/CustomerContact/components/CreateCustomerSupportModal'
import CreateSupportMissionsModal from '~/views/main/Missions/components/CreateSupportMissionsModal'
import CreateQuotationModal from '~/views/main/sales-management/sub-screen/quotation/components/QuotationCreateComponent'
import { InvoiceEditComponent } from '~/views/main/sales-management/sub-screen/invoice/components/InvoiceEditComponent'

// Map tab titles to modal types
const MODAL_MAP = {
  'list-product': 'product',
  'customer-support/complaint-management': 'complaint',
  'survey-feedback/survey-feedback': 'survey-feedback',
  'loyalty-program/gift-box': 'gift-box',
  'customer-management/list-customer': 'list-customer',
  'customer-management/list-contact': 'list-customer-contact',
  'customer-management/list-customer-evaluation': 'list-customer-evaluation',
  'customer-management/list-contact-evaluation': 'list-contact-evaluation',
  'sales-management/quotations': 'quotations',
  'sales-management/contracts': 'contracts',
  'sales-management/invoices': 'invoices',
  'customer-support/customer-contact': 'customer-contact',
  'customer-support/missions': 'missions',
  'settings/customer-criteria': 'customer-criteria',
  'settings/quotation-template': 'quotation-template',
  'settings/contract': 'contract',
  'settings/invoice': 'invoice-template',
  'settings/department': 'department',
  'settings/employee': 'employee',
  'marketing-campaign/target-group': 'target',
  'marketing-campaign/content': 'content',
  'marketing-campaign/campaign': 'campaign',
  'kpi/kpi-category': 'kpi-category',
  'kpi/kpi-group': 'kpi-group'
} as const

type ModalType = (typeof MODAL_MAP)[keyof typeof MODAL_MAP]

const TabsAction: FC = () => {
  const { tabs, selectedKey, removeAllTab } = useLayoutConfig()
  const [openModal, setOpenModal] = useState<ModalType | null>(null)

  const currentTab = tabs.find((tab) => tab.key === selectedKey)
  const modalType = currentTab?.rootPath ? MODAL_MAP[currentTab.rootPath] : null

  const handlePress = () => {
    if (modalType) {
      setOpenModal(modalType)
    } else {
      console.log('No modal defined for this tab:', currentTab?.title)
    }
  }

  const handleDelete = () => {
    removeAllTab()
  }

  const handleCloseModal = () => {
    setOpenModal(null)
  }

  const handleSuccess = () => {
    // console.log(`${currentTab?.title} created successfully!`)
    // Có thể refresh data hoặc thực hiện actions khác
  }

  // ẩn button create nếu là loyalty, report
  const hideButtonCreate = currentTab?.key.includes('loyalty') || currentTab?.key.includes('report')

  return (
    <>
      <div style={{ display: 'flex', gap: 8 }}>
        <Button
          onClick={handleDelete}
          htmlType='submit'
          icon={<DeleteOutlined />}
          style={{ display: 'block' }}
        />

        {!hideButtonCreate && (
          <Button
            onClick={handlePress}
            type='primary'
            htmlType='submit'
            icon={<PlusOutlined />}
            style={{ display: modalType ? 'block' : 'none' }}>
            {`Tạo ${currentTab?.title || ''}`}
          </Button>
        )}
      </div>
      <CreateProductModal
        open={openModal === 'product'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CreateComplaintModal
        open={openModal === 'complaint'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CreateSurveyFeedbackModal
        open={openModal === 'survey-feedback'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CreateGiftModal
        open={openModal === 'gift-box'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CreateCustomerModal
        open={openModal === 'list-customer'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CreateCustomerContactModal
        open={openModal === 'list-customer-contact'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CreateEvaluationCriteriaModal
        open={openModal === 'list-customer-evaluation'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CreateContactEvaluationCriteriaModal
        open={openModal === 'list-contact-evaluation'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CreateQuotationModal
        open={openModal === 'quotations'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      {/* <CreateQuotationModal
        open={openModal === 'quotations'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
       */}

      <CreateSupportMissionsModal
        open={openModal === 'missions'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />
      <CreateCustomerSupportModal
        open={openModal === 'customer-contact'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />
      <CreateCustomerCriteriaModal
        open={openModal === 'customer-criteria'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CreateDepartmentModal
        open={openModal === 'department'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CreateEmployeeModal
        open={openModal === 'employee'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />
      <CreateContractModal
        open={openModal === 'contract'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <InvoiceCreateComponent
        open={openModal === 'invoices'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <InvoiceEditComponent
        open={openModal === 'invoices'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <ContractCreateComponent
        open={openModal === 'contracts'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <QuotationTemplateCreate
        open={openModal === 'quotation-template'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <TargetCreateComponent
        open={openModal === 'target'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <ContentCreateComponent
        open={openModal === 'content'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CampaignCreateComponent
        open={openModal === 'campaign'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <InvoiceTemplateCreateComponent
        open={openModal === 'invoice-template'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CreateKpiCategoryModal
        open={openModal === 'kpi-category'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />

      <CreateKpiGroupModal
        open={openModal === 'kpi-group'}
        onClose={handleCloseModal}
        onSuccess={handleSuccess}
      />
    </>
  )
}

export default TabsAction
