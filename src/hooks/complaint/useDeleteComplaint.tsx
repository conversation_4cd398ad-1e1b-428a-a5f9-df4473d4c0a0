import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toastService } from '~/services/@common'
import { rootApiService } from '~/services/@common'
import { endpoints_customer } from '~/services/endpoints'

interface useDeleteCustomerParams {
  id: string
}

export const useDeleteComplaint = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: (body: useDeleteCustomerParams) =>
      rootApiService.post(endpoints_customer.delete, body),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [endpoints_customer.list, { pageIndex: 1, pageSize: 10 }]
      })
      toastService.success('Xóa khách hàng thành công')
    },
    onError: (error) => {
      toastService.error('Xóa khách hàng thất bại')
    }
  })
}
