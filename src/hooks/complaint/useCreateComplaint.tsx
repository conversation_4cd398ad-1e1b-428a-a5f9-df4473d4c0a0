import { useMutation, useQueryClient } from '@tanstack/react-query'
import { ICreateCustomerReq } from '~/dto/customer.dto'
import { rootApiService, toastService } from '~/services/@common'
import { endpoints_customer } from '~/services/endpoints'

export const useCreateComplaint = () => {
  const queryClient = useQueryClient()
  const { mutateAsync: createCustomer, isPending } = useMutation({
    mutationFn: (body: ICreateCustomerReq) =>
      rootApiService.post(endpoints_customer.create, body),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [endpoints_customer.list, { pageIndex: 1, pageSize: 10 }]
      })
      toastService.success('Tạo khách hàng thành công')
    },
    onError: (error) => {
      toastService.error('Tạo khách hàng thất bại')
    }
  })

  return { createCustomer, isPending }
}
