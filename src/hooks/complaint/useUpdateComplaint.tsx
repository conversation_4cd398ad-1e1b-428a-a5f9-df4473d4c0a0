import { useMutation, useQueryClient } from '@tanstack/react-query'
import { IUpdateCustomerReq } from '~/dto/customer.dto'
import { rootApiService, toastService } from '~/services/@common'
import { endpoints_customer } from '~/services/endpoints'

export const useUpdateComplaint = () => {
  const queryClient = useQueryClient()
  const { mutateAsync: updateCustomer, isPending } = useMutation({
    mutationFn: (body: IUpdateCustomerReq) =>
      rootApiService.post(endpoints_customer.update, body),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [endpoints_customer.list, { pageIndex: 1, pageSize: 10 }]
      })
      toastService.success('Cập nhật khách hàng thành công')
    },
    onError: (error) => {
      toastService.error('Cập nhật khách hàng thất bại')
    }
  })

  return { updateCustomer, isPending }
}
