import { useQuery } from "@tanstack/react-query";
import { rootApiService } from "~/services/@common";
import { IProductDetail } from "~/dto/product.dto";
import { endpoints_product } from "~/services/endpoints";

export const useDetailProduct = (id: string) => {
  const { data, isLoading, refetch } = useQuery<IProductDetail>({
    queryKey: [endpoints_product.detail, id],
    queryFn: () => rootApiService.post(endpoints_product.detail, { id }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // 5 minutes
  });

  return {
    data,
    isLoading,
    refetch,
  };
};
