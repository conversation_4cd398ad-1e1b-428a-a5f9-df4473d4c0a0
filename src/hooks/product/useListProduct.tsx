import { useInfiniteQuery } from "@tanstack/react-query";
import { IProduct, IProductResponse } from "~/dto/product.dto";
import { rootApiService } from "~/services/@common";
import { endpoints_product } from "~/services/endpoints";
import { PageResponse } from "~/@ui/GridControl/models";

interface UseListProductParams {
  productName: string;
  version: string;
  status: string;
  pageIndex: number;
  pageSize: number;
}

export const useListProduct = (params: UseListProductParams) => {
  // const { data, isLoading, refetch } = useInfiniteQuery<
  //   IProductResponse,
  //   Error
  // >({
  //   queryKey: [
  //     endpoints_product.list,
  //     {
  //       ...params,
  //       pageSize: params.pageSize,
  //     },
  //   ],
  //   queryFn: ({ pageParam = 1 }) =>
  //     rootApiService.post<PageResponse<IProduct>>(endpoints_product.list, {
  //       ...params,
  //       pageSize: params.pageSize,
  //     }),
  //   getNextPageParam: (lastPage, allPages) =>
  //     lastPage.data.length > 0 ? allPages.length + 1 : undefined,
  //   initialPageParam: 1,
  // });

  // const formatData = data?.pages.flatMap((page) => page.data) ?? [];
  // const total = data?.pages[0]?.total ?? 0;
  return {
    data: [],
    isLoading: false,
    refetch: () => {},
    total: 0,
  };
};
