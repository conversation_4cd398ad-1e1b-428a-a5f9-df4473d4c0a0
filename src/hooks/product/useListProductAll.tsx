import { useQuery } from "@tanstack/react-query";
import { rootApiService } from "~/services/@common";
import { endpoints_product } from "~/services/endpoints";

export const useListProductAll = () => {
  const { data, isLoading, refetch } = useQuery({
    queryKey: [endpoints_product.listAll],
    queryFn: () => rootApiService.get(endpoints_product.listAll),
  });

  return {
    data,
    isLoading,
    refetch,
  };
};
