import { useMutation, useQueryClient } from "@tanstack/react-query";
import { rootApiService, toastService } from "~/services/@common";
import { endpoints_product } from "~/services/endpoints";

interface useDeleteProductParams {
  id: string;
}

export const useDeleteProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (body: useDeleteProductParams) =>
      rootApiService.post(endpoints_product.delete, body),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [endpoints_product.list, { pageIndex: 1, pageSize: 10 }],
      });
      toastService.success("Xóa sản phẩm thành công");
    },
    onError: (error) => {
      toastService.error("Xóa sản phẩm thất bại");
    },
  });
};
