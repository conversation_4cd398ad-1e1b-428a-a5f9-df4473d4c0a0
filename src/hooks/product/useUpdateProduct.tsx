import { useMutation, useQueryClient } from "@tanstack/react-query";
import { rootApiService, toastService } from "~/services/@common";
import { endpoints_product } from "~/services/endpoints";

export interface UseUpdateProductParams {
  id: string;
  name?: string;
  title?: string;
  description?: string;
  defaultMaxUsers: number;
  note: string;
  images: string[];
}

export const useUpdateProduct = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (body: UseUpdateProductParams) =>
      rootApiService.post(endpoints_product.update, body),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [endpoints_product.list, { pageIndex: 1, pageSize: 10 }],
      });
      toastService.success("Cập nhật product thành công");
    },
    onError: (error) => {
      toastService.error("Cập nhật product thất bại");
    },
  });
};
