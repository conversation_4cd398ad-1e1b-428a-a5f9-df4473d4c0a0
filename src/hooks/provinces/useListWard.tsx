import { useQuery } from "@tanstack/react-query";
import { PageResponse } from "~/@ui/GridControl/models";
import { IWard } from "~/dto/provinces.dto";
import { rootApiService } from "~/services/@common";
import { endpoints_provinces } from "~/services/endpoints";

interface UseListWardsParams {
  code?: number;
}

export const useListWards = ({ code }: UseListWardsParams) => {
  const { data, isLoading, refetch } = useQuery<PageResponse<IWard>>({
    queryKey: [endpoints_provinces.listWard, { code }],
    queryFn: () =>
      rootApiService.post<PageResponse<IWard>>(endpoints_provinces.listWard, {
        code,
      }),
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!code,
  });

  const wards: any = data || [];

  return {
    data: wards,
    isLoading,
    refetch,
  };
};
