import { useQuery } from "@tanstack/react-query";
import { rootApiService } from "~/services/@common";
import { endpoints_provinces } from "~/services/endpoints";

interface UseListFullAddressParams {
  provinceCode: number;
  districtCode: number;
  wardCode: number;
  address?: string;
}

export const useListFullAddress = ({
  provinceCode,
  districtCode,
  wardCode,
  address,
}: UseListFullAddressParams) => {
  const { data, isLoading, refetch } = useQuery<string>({
    queryKey: [
      endpoints_provinces.fullAddress,
      {
        provinceCode,
        districtCode,
        wardCode,
        address,
      },
    ],
    queryFn: () =>
      rootApiService.post<string>(endpoints_provinces.fullAddress, {
        provinceCode: provinceCode.toString(),
        districtCode: districtCode.toString(),
        wardCode: wardCode.toString(),
        address,
      }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const fullAddress = data || "";

  return {
    data: fullAddress,
    isLoading,
    refetch,
  };
};
