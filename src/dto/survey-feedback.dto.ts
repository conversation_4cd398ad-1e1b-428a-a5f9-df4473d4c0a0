export interface ISurveyFeedbackResponse {
  data: ISurveyFeedback[]
  total: number
}

export interface ISurveyFeedback {
  id: string
  surveyName: string
  createdAt: string
  createdBy: string
  status: string
  statusName?: string
  images?: string[]
  title?: string
  description?: string
  validDate?: number
  sections?: ISection[]
  endSection?: IEndSection
}

export interface ISection {
  title: string
  description: string
  questions: IQuestion[]
}

export interface IQuestion {
  question: string
  image?: string
  answer?: string
  type: string
}

export interface IEndSection {
  description: string
}

export interface IFilterSurveyFeedback {
  surveyName: string
  createdBy: string
  status: string
  pageIndex: number
  pageSize: number
}

export enum ESurveyFeedbackStatus {
  DRAFT = 'DRAFT',
  PUBLISHED = 'PUBLISHED',
  CLOSED = 'CLOSED'
}

export const SurveyFeedbackStatusConfig = {
  [ESurveyFeedbackStatus.DRAFT]: {
    color: 'default',
    label: 'Nháp'
  },
  [ESurveyFeedbackStatus.PUBLISHED]: {
    color: 'success',
    label: 'Đã xuất bản'
  },
  [ESurveyFeedbackStatus.CLOSED]: {
    color: 'error',
    label: 'Đã đóng'
  }
}
