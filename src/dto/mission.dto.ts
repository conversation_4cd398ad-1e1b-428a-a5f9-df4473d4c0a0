export interface IMission {
  id: string
  activityId: string
  title: string
  description: string
  type: string
  status: string
  creator: string
  createdAt: string
  customerCode: string
  customerName: string
  email: string
  phone: string
}

export interface ICustomerVisit {
  id: string
  activityId: string
  title: string
  description: string
  type: string
  status: string
  creator: string
  createdAt: string
  customerCode: string
  customerName: string
  email: string
  phone: string
}

export interface IActivitySegment {
  month: string
  count: number
  segment: string
}
