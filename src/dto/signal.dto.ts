import { PageRequest } from "~/@ui/GridControl/models";
import { NSAccount, NSSignal } from "~/common/enums";

export class CreateSignalReq {
  actionType: NSSignal.EActionType;
  type: string;
  baseToken: string;
  quoteToken: string;
  baseTokenIcon?: string;
  quoteTokenIcon?: string;
  entryPrice: number;
  recordedPrice?: number;
  startDate: Date;
  imageUrl?: string;
  accountType?: NSAccount.EType;
  note?: string;
  noteVi?: string;
}

export class CreateNotificationReq {
  title: string;
  desc: string;
  iconUrl: string;
}

export class ListLicenseReq extends PageRequest {
  key?: string;
  customerName?: string;
  productName?: string;
  status?: string;
  issuedAtFrom?: string;
  issuedAtTo?: string;
  expiredAtFrom?: string;
  expiredAtTo?: string;
}

export interface ISignal {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  actionType: string;
  type: string;
  baseToken: string;
  quoteToken: string;
  baseTokenIcon: string;
  quoteTokenIcon: string;
  entryPrice: string;
  recordedPrice: string;
  startDate: string;
  imageUrl?: string;
}

export interface IReport {
  id: string;
  title: string;
  fileUrl: string;
  content: string;
}

export class CreateReportReq {
  title: string;
  content: string;
  fileUrl: string;
  accountType?: NSAccount.EType;
}

export class ListReportReq extends PageRequest {}
