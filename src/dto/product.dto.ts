import { PageRequest } from "./page.dto";
import { NSProduct } from "~/common/enums/NSProduct";

export class ListProductReq extends PageRequest {}

export interface IProductResponse {
  data: IProduct[];
  total: number;
}

export interface IProduct {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  groupId: any;
  name: string;
  title: string;
  description: string;
  defaultMaxUsers: string;
  vat: string;
  note: string;
  status: string;
  type: string;
  customerUsed: number;
  media: Media[];
}

export interface Media {
  id: string;
  createdDate?: string;
  updatedDate?: string;
  createdBy?: any;
  updatedBy?: any;
  version?: number;
  refId?: string;
  refTable?: string;
  type?: string;
  imageUrl: string;
}

export interface CreateProductReq {
  name: string;
  title?: string;
  description?: string;
  defaultMaxUsers: number;
  note?: string;
}

export interface IProductDetail {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  groupId: any;
  name: string;
  title: string;
  description: string;
  defaultMaxUsers: string;
  isRecommended: boolean;
  vat: string;
  note: string;
  status: string;
  type: string;
  media: IMedia[];
  customerUsed: number;
}

export interface IMedia {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  refId: string;
  refTable: string;
  type: string;
  imageUrl: string;
}
