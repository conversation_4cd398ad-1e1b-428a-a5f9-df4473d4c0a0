export interface IGiftResponse {
  data: IGift[]
  total: number
}

export interface IGift {
  id: string
  targetAudience: string
  programName: string
  memberLevel: string
  conversionType: string
  giftValue: number
  unit: string
  startDate: string
  endDate: string
  status: string
  description?: string
  createdAt: string
  updatedAt: string
  images?: string[]
}

export enum ELoyaltyStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DRAFT = 'DRAFT',
  EXPIRED = 'EXPIRED'
}

export const LoyaltyStatusConfig = {
  [ELoyaltyStatus.ACTIVE]: {
    color: 'success',
    label: 'Hoạt động'
  },
  [ELoyaltyStatus.INACTIVE]: {
    color: 'error',
    label: 'Không hoạt động'
  },
  [ELoyaltyStatus.DRAFT]: {
    color: 'warning',
    label: 'Bản nháp'
  },
  [ELoyaltyStatus.EXPIRED]: {
    color: 'default',
    label: 'Hết hạn'
  }
}
