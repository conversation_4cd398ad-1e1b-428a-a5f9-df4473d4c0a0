export interface ISettingContract {
  id: string
  code: string
  name: string
  terms: { code: string; name: string; content: string }[]
  baseOn: string
  description: string
  status: string
  createdAt: string
  updatedAt: string
}

//filter
export interface ISettingContractFilter {
  id: string
  name: string
  pageIndex: number
  pageSize: number
}

export interface ISettingContractResponse {
  data: ISettingContract[]
  total: number
}

export interface ICreateSettingContractReq {
  name: string
  description: string
}
