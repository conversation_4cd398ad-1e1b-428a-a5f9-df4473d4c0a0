

export interface IMission {
  id: number;
  title: string;
  description: string;
  type: string;
  status: string;
  displayLocation: string;
  customerCode: string;
  sapCode: string;
  customerName: string;
  address: string;
  supervisor: string;
  assignedEmployee: string;
  assignType: string;
  level: string;
  startDate: string;
  dueDate: string;
  endDate: string;
  checkInDate: string;
  checkOutTime: string;
}

//filter mission
export interface IMissionFilter {
  id: number;
  title: string;
  description: string;
  type: string;
  status: string;
  displayLocation: string;
  customerCode: string;
  sapCode: string;
  customerName: string;
  address: string;
  supervisor: string;
  assignedEmployee: string;
  level: string;
  startDate: string;
  dueDate: string;
  endDate: string;
  checkInDate: string;
  pageSize: number;
  pageIndex: number;
}