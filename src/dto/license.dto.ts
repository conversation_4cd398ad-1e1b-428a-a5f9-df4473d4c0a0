export interface ILicenseResponse {
  data: ILicense[];
  total: number;
}

export interface ILicense {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  key: string;
  issuedAt: string;
  expiredAt: string;
  status: string;
  notes: any;
  maxUsers: number;
  currentUsers: number;
  customerId: string;
  productId: string;
  licenseId: string;
  customer: Customer;
  product: Product;
}

export interface Customer {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  name: string;
  phone: string;
  address: string;
  provinceCode: any;
  districtCode: any;
  wardCode: any;
  email: string;
  password: any;
  avatar: string;
  taxCode: any;
  note: any;
  role: string;
  status: string;
  referrerId: any;
}

export interface Product {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  groupId: any;
  name: string;
  title: string;
  description: string;
  defaultMaxUsers: string;
  isRecommended: boolean;
  vat: string;
  note: string;
  status: string;
  type: string;
}

export interface CreateLicenseReq {
  key: string;
  customerId: string;
  productId: string;
  issuedAt: Date;
  expiredAt: Date;
  maxUsers: number;
}

export interface ILicenseDetail {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  key: string;
  issuedAt: string;
  expiredAt: string;
  status: string;
  notes: any;
  maxUsers: string;
  currentUsers: string;
  customerId: string;
  productId: string;
  histories: any[];
  product: ProductDetail;
  customer: CustomerDetail;
  statusName: string;
}

export interface ProductDetail {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  groupId: any;
  name: string;
  title: string;
  description: string;
  defaultMaxUsers: string;
  isRecommended: boolean;
  vat: string;
  note: string;
  status: string;
  type: string;
}

export interface CustomerDetail {
  id: string;
  createdDate: string;
  updatedDate: string;
  createdBy: any;
  updatedBy: any;
  version: number;
  name: string;
  phone: string;
  address: any;
  provinceCode: string;
  districtCode: string;
  wardCode: string;
  email: string;
  password: string;
  avatar: any;
  taxCode: any;
  note: any;
  role: string;
  status: string;
  referrerId: any;
}
