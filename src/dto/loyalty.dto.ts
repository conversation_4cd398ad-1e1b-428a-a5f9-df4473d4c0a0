export interface ILoyaltyResponse {
  data: ILoyalty[]
  total: number
}

export interface ILoyalty {
  id: string
  customerCode: string
  customerName: string
  phoneNumber: string
  programCode: string
  programName: string
  totalAccumulatedMoney: number
  exchangedMoney: number
  remainingMoney: number
  status: string
  createdAt: string
  updatedAt: string
}

export enum ELoyaltyStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  DRAFT = 'DRAFT',
  EXPIRED = 'EXPIRED'
}

export const LoyaltyStatusConfig = {
  [ELoyaltyStatus.ACTIVE]: {
    color: 'success',
    label: 'Hoạt động'
  },
  [ELoyaltyStatus.INACTIVE]: {
    color: 'error',
    label: 'Không hoạt động'
  },
  [ELoyaltyStatus.DRAFT]: {
    color: 'warning',
    label: 'Bản nháp'
  },
  [ELoyaltyStatus.EXPIRED]: {
    color: 'default',
    label: 'Hết hạn'
  }
}

export interface IFilterLoyalty {
  customerCode: string
  customerName: string
  phoneNumber: string
  programCode: string
  programName: string
  status: string
  pageIndex: number
  pageSize: number
}
