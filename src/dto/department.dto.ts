export interface IDepartment {
  id: string
  name: string
  description: string
  status: string
  createdAt: string
  updatedAt: string
}

export interface IDepartmentResponse {
  data: IDepartment[]
  total: number
}

export interface IDepartmentFilter {
  id: string
  name: string
  description: string
  status: string
  createdAt: string
  updatedAt: string 
  pageIndex: number
  pageSize: number
}
