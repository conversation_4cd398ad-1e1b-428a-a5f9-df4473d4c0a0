export interface ICustomersResponse {
  data: ICustomer[]
  total: number
}

export interface ICustomer {
  id: string
  code: string
  name: string
  shortName: string
  phone: string
  email: string
  address: string
  customerType: string
  salesRep: string
  department: string
  ranking: string
  source: string
  industry: string
  region: string
  createdBy: string
  createdAt: string
  updatedAt: string
  website: string
  taxCode: string
  market: 'domestic' | 'international'
  customerGroup: string
  visitDate: string
  note: string
  sapCode: string
}

export interface Province {
  id: string
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: any
  isDeleted: boolean
  code: string
  name: string
  regionId: string
  area: any
}

export interface District {
  id: string
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: any
  isDeleted: boolean
  code: string
  name: string
  cityId: string
}

export interface Ward {
  id: string
  createdAt: string
  createdBy: string
  updatedAt: string
  updatedBy: any
  isDeleted: boolean
  code: string
  name: string
  districtId: string
}

export interface ICreateCustomerReq {
  // Thông tin cơ bản
  market: 'domestic' | 'international'
  isTopInvestor: boolean
  customerRank: 'A' | 'B' | 'C' | 'D'
  activityRating: string
  isProjectInvestor: boolean
  isProjectDesigner: boolean
  isProjectContractor: boolean
  customerSource: 'website' | 'facebook' | 'google' | 'referral' | 'other'
  branch: string

  // Thông tin cá nhân
  fullName: string
  shortName: string
  dateOfBirth: string
  gender: 'male' | 'female' | 'other'
  age: number
  phone: string
  email: string
  industry: 'retail' | 'wholesale' | 'service' | 'other'
  nationality: string

  // Thông tin địa chỉ
  addressType: 'home' | 'office' | 'other'
  region: 'north' | 'central' | 'south'
  city: string
  district: string
  ward: string
  address: string
  area: number

  // Thông tin bổ sung
  visitDate: string
  salesRep: string
  mainProductGroup: string[]
  transactionYear: string
  signboardCount: number
  productTrustLevel: 'high' | 'medium' | 'low'
  interactionLevel: 'high' | 'medium' | 'low'
  investmentSegment: 'high' | 'medium' | 'low'
  productAwareness: 'website' | 'social' | 'friend' | 'other'
  shoppingHabits: ('promotion' | 'noInterest')[]
  note?: string
  images?: string[]
}

export interface IUpdateCustomerReq extends Partial<ICreateCustomerReq> {
  id: string
}

export interface IDeleteCustomerReq {
  id: string
}

export interface IGetCustomerReq {
  id: string
}

export interface IGetCustomersReq {
  page?: number
  limit?: number
  search?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
  filter?: {
    customerType?: string
    ranking?: string
    source?: string
    industry?: string
    region?: string
    salesRep?: string
    department?: string
    market?: 'domestic' | 'international'
    isTopInvestor?: boolean
    isProjectInvestor?: boolean
    isProjectDesigner?: boolean
    isProjectContractor?: boolean
    branch?: string
    gender?: 'male' | 'female' | 'other'
    nationality?: string
    addressType?: 'home' | 'office' | 'other'
    city?: string
    district?: string
    ward?: string
    productTrustLevel?: 'high' | 'medium' | 'low'
    interactionLevel?: 'high' | 'medium' | 'low'
    investmentSegment?: 'high' | 'medium' | 'low'
    productAwareness?: 'website' | 'social' | 'friend' | 'other'
    shoppingHabits?: ('promotion' | 'noInterest')[]
  }
}
