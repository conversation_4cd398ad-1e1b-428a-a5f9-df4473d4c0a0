import { PrimaryBaseEntity } from './@common/base.dto'

export interface IComplaintResponse extends PrimaryBaseEntity {
  data: IComplaint[]
  total: number
}

export interface IComplaint extends PrimaryBaseEntity {
  // Thông tin cơ bản
  title: string
  description: string
  type: EComplaintType
  status: EComplaintStatus
  priority: EComplaintPriority

  // Thông tin địa chỉ
  complaintAddress: string
  address: string
  provinceCode?: string
  districtCode?: string
  wardCode?: string

  // Thông tin khách hàng
  customerCode: string
  sapCode: string
  customer: string
  customerId: string

  // Thông tin nhân viên
  supervisor: string
  supervisorId: string
  assignedStaff: string
  assignedStaffId: string

  // Thông tin thời gian
  startDate: string
  dueDate: string
  endDate: string
  checkinTime: string
  checkoutTime: string

  // Thông tin khác
  notificationStatus: ENotificationStatus
  media: IMedia[]
  notes: string
  solution: string
  rating: number
  feedback: string
}

export interface IMedia {
  id: string
  url: string
  type: string
  name: string
  size: number
}

export interface IFilterComplaint {
  title?: string
  type?: EComplaintType
  status?: EComplaintStatus
  customerCode?: string
  sapCode?: string
  supervisorId?: string
  assignedStaffId?: string
  priority?: EComplaintPriority
  startDate?: string
  endDate?: string
  pageIndex: number
  pageSize: number
}

export interface IFilterCustomerContact {
  name?: string
  phone?: string
  email?: string
  pageIndex: number
  pageSize: number
}

export interface ICreateComplaintReq {
  title: string
  description: string
  type: EComplaintType
  priority: EComplaintPriority
  complaintAddress: string
  address: string
  provinceCode?: string
  districtCode?: string
  wardCode?: string
  customerId: string
  supervisorId: string
  assignedStaffId: string
  startDate: string
  dueDate: string
  media?: File[]
}

export interface IUpdateComplaintReq {
  id: string
  title?: string
  description?: string
  type?: EComplaintType
  status?: EComplaintStatus
  priority?: EComplaintPriority
  complaintAddress?: string
  address?: string
  provinceCode?: string
  districtCode?: string
  wardCode?: string
  customerId?: string
  supervisorId?: string
  assignedStaffId?: string
  startDate?: string
  dueDate?: string
  endDate?: string
  checkinTime?: string
  checkoutTime?: string
  notificationStatus?: ENotificationStatus
  media?: File[]
  notes?: string
  solution?: string
}

export enum EComplaintType {
  PRODUCT = 'PRODUCT',
  SERVICE = 'SERVICE',
  TECHNICAL = 'TECHNICAL',
  BILLING = 'BILLING',
  OTHER = 'OTHER'
}

export enum EComplaintStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REJECTED = 'REJECTED'
}

export enum EComplaintPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

export enum ENotificationStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  FAILED = 'FAILED'
}

export const ComplaintTypeLabels: Record<EComplaintType, string> = {
  [EComplaintType.PRODUCT]: 'Sản phẩm',
  [EComplaintType.SERVICE]: 'Dịch vụ',
  [EComplaintType.TECHNICAL]: 'Kỹ thuật',
  [EComplaintType.BILLING]: 'Thanh toán',
  [EComplaintType.OTHER]: 'Khác'
}

export const ComplaintStatusLabels: Record<EComplaintStatus, string> = {
  [EComplaintStatus.PENDING]: 'Đang chờ',
  [EComplaintStatus.PROCESSING]: 'Đang xử lý',
  [EComplaintStatus.COMPLETED]: 'Hoàn thành',
  [EComplaintStatus.CANCELLED]: 'Đã hủy',
  [EComplaintStatus.REJECTED]: 'Từ chối'
}

export const ComplaintPriorityLabels: Record<EComplaintPriority, string> = {
  [EComplaintPriority.LOW]: 'Thấp',
  [EComplaintPriority.MEDIUM]: 'Trung bình',
  [EComplaintPriority.HIGH]: 'Cao',
  [EComplaintPriority.URGENT]: 'Khẩn cấp'
}

export const NotificationStatusLabels: Record<ENotificationStatus, string> = {
  [ENotificationStatus.PENDING]: 'Chờ gửi',
  [ENotificationStatus.SENT]: 'Đã gửi',
  [ENotificationStatus.FAILED]: 'Gửi thất bại'
}

export const ComplaintStatusColors: Record<EComplaintStatus, string> = {
  [EComplaintStatus.PENDING]: 'blue',
  [EComplaintStatus.PROCESSING]: 'orange',
  [EComplaintStatus.COMPLETED]: 'green',
  [EComplaintStatus.CANCELLED]: 'red',
  [EComplaintStatus.REJECTED]: 'red'
}

export const ComplaintPriorityColors: Record<EComplaintPriority, string> = {
  [EComplaintPriority.LOW]: 'blue',
  [EComplaintPriority.MEDIUM]: 'orange',
  [EComplaintPriority.HIGH]: 'red',
  [EComplaintPriority.URGENT]: 'red'
}
