export interface IReportKpiSaleEmployee {
  key: string
  name: string
  newCustomersTarget: number
  newCustomersActual: number
  newCustomersRate: string
  callsTarget: number
  callsActual: number
  callsRate: string
  quotesTarget: number
  quotesActual: number
  quotesRate: string
  ordersTarget: number
  ordersActual: number
  ordersRate: string
  revenueTarget: number
  revenueActual: number
  revenueRate: string
  closingRateTarget: string
  closingRateActual: string
  closingRateRate: string
  crmUpdateTarget: string
  crmUpdateActual: string
  crmUpdateRate: string
  totalKpi: string
}

export interface IReportKpiSaleEmployeeResponse {
  data: IReportKpiSaleEmployee[]
  total: number
}

//filter

export interface IFilterReportKpiSaleEmployee {
  name?: string
  month?: string
  pageIndex?: number
  pageSize?: number
}
