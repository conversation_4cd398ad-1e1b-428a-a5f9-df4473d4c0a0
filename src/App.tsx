import { FC, Suspense, useEffect } from 'react'
import { ConfigProvider, Spin, theme } from 'antd'
import AppRouter from './views/AppRouter'
import ActivityIndicator from './components/ActivityIndicator'
import { useThemeStore } from './stores/themeStore'
import { useAuthStore } from './stores/authStore'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import i18n from './i18n'
import { I18nextProvider } from 'react-i18next'

interface IProps {}
const App: FC<IProps> = (props: IProps) => {
  const { changeTheme } = useThemeStore()
  const { authenticate, isFirstLoading } = useAuthStore()
  useEffect(() => {
    authenticate()
  }, [authenticate, changeTheme])

  const { themConfig } = useThemeStore()
  const queryClient = new QueryClient()
  return (
    <I18nextProvider i18n={i18n}>
      <ConfigProvider
        theme={{
          ...themConfig
        }}>
        <QueryClientProvider client={queryClient}>
          <Suspense fallback={null}>
            {/* <Spin
              spinning={isFirstLoading}
              wrapperClassName='app-loading-wrapper'
              tip={'Đang tải dữ liệu...'}>
              {isFirstLoading ? <ActivityIndicator /> : }
            </Spin> */}
            {<AppRouter />}
          </Suspense>
        </QueryClientProvider>
      </ConfigProvider>
    </I18nextProvider>
  )
}
export default App
