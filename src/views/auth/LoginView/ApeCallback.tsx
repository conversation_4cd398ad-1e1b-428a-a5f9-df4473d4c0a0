import { useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAuthStore } from '~/stores/authStore'
import { KeyLocalStore } from '~/common/constants'
import { endpoint_auth } from '~/services/endpoints'
import { useQueryClient } from '@tanstack/react-query'

const ApeCallback = () => {
  const navigate = useNavigate()
  const { login } = useAuthStore()
  const { authenticate } = useAuthStore()
  const queryClient = useQueryClient()
  useEffect(() => {
    const params = new URLSearchParams(window.location.search)
    const accessToken = params.get('accessToken')
    if (accessToken) {
      localStorage.setItem(KeyLocalStore.accessToken, accessToken)
      queryClient.refetchQueries({
        queryKey: [endpoint_auth.me]
      })
      authenticate()
    } else {
      console.error('Access token not found!')
    }
  }, [navigate, login, queryClient, authenticate])

  return <p><PERSON><PERSON> xử lý đăng nhập...</p>
}

export default ApeCallback
