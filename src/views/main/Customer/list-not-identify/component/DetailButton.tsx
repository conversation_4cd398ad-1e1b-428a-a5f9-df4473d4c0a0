import {
  EyeOutlined,
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  BankOutlined,
  InfoCircleOutlined,
  StarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons'
import { Card, Descriptions, Tag, Typography, Row, Col, Space } from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import BaseModal from '~/components/BaseModal'
import { formatDateCustom } from '~/common/helper/helper'
import { ICustomerContact } from '~/dto/customer-contact.dto'
import { useModal } from '~/hooks/useModal'

const { Title } = Typography

interface DetailButtonProps {
  data: ICustomerContact
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal()

  if (!data) return null

  const {
    contactCode,
    name,
    customerName,
    phone,
    email,
    branch,
    note,
    createdBy,
    createdAt,
    isSpecialCare,
    status
  } = data

  const modalContent = (
    <div>
      {/* Contact Overview Card */}
      <Card
        title={
          <Space>
            <UserOutlined style={{ color: '#1890ff' }} />
            <BaseText>Thông tin liên hệ</BaseText>
          </Space>
        }
        style={{ marginBottom: '16px' }}
        size='small'>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={16}>
            <div>
              <Space align='start'>
                <Title level={3} style={{ margin: 0 }}>
                  {name || 'N/A'}
                </Title>
                {isSpecialCare && (
                  <Tag color='gold' icon={<StarOutlined />}>
                    Chăm sóc đặc biệt
                  </Tag>
                )}
              </Space>
              <BaseText
                color='textSecondary'
                size='lg'
                style={{ marginTop: '8px', display: 'block' }}>
                {customerName}
              </BaseText>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <Space direction='vertical' size='middle' style={{ width: '100%' }}>
              <div>
                <BaseText color='textSecondary'>Mã liên hệ:</BaseText>
                <br />
                <Tag
                  color='blue'
                  style={{ fontSize: '14px', padding: '4px 12px' }}>
                  {contactCode}
                </Tag>
              </div>
              <div>
                <BaseText color='textSecondary'>Trạng thái:</BaseText>
                <br />
                <Tag
                  color={status === 'Hoạt động' ? 'green' : 'red'}
                  icon={
                    status === 'Hoạt động' ? (
                      <CheckCircleOutlined />
                    ) : (
                      <CloseCircleOutlined />
                    )
                  }
                  style={{ fontSize: '14px', padding: '4px 12px' }}>
                  {status}
                </Tag>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Contact Details */}
      <Card
        title={
          <Space>
            <InfoCircleOutlined style={{ color: '#13c2c2' }} />
            <BaseText>Chi tiết liên hệ</BaseText>
          </Space>
        }
        style={{ marginBottom: '16px' }}
        size='small'>
        <Row gutter={[24, 16]}>
          <Col xs={24} sm={12}>
            <Space>
              <PhoneOutlined style={{ color: '#52c41a' }} />
              <BaseText weight='bold'>Số điện thoại:</BaseText>
              <BaseText>{phone}</BaseText>
            </Space>
          </Col>
          <Col xs={24} sm={12}>
            <Space>
              <MailOutlined style={{ color: '#722ed1' }} />
              <BaseText weight='bold'>Email:</BaseText>
              <BaseText>{email}</BaseText>
            </Space>
          </Col>
          <Col xs={24} sm={12}>
            <Space>
              <BankOutlined style={{ color: '#fa8c16' }} />
              <BaseText weight='bold'>Chi nhánh:</BaseText>
              <BaseText>{branch}</BaseText>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Notes */}
      {note && (
        <Card
          title={
            <Space>
              <InfoCircleOutlined style={{ color: '#eb2f96' }} />
              <BaseText>Ghi chú</BaseText>
            </Space>
          }
          style={{ marginBottom: '16px' }}
          size='small'>
          <BaseText>{note}</BaseText>
        </Card>
      )}

      {/* System Information */}
      <Card
        title={
          <Space>
            <InfoCircleOutlined style={{ color: '#13c2c2' }} />
            <BaseText>Thông tin hệ thống</BaseText>
          </Space>
        }
        size='small'>
        <Row gutter={[24, 16]}>
          <Col xs={24} sm={12}>
            <div>
              <BaseText color='textSecondary'>Người tạo</BaseText>
              <br />
              <BaseText weight='bold'>{createdBy}</BaseText>
            </div>
          </Col>
          <Col xs={24} sm={12}>
            <div>
              <BaseText color='textSecondary'>Ngày tạo</BaseText>
              <br />
              <BaseText weight='bold'>
                {formatDateCustom(createdAt, 'DD/MM/YYYY')}
              </BaseText>
            </div>
          </Col>
        </Row>
      </Card>
    </div>
  )

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />
      <BaseModal
        open={open}
        title='Chi tiết liên hệ'
        description='Thông tin chi tiết liên hệ khách hàng'
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  )
}

export default DetailButton
