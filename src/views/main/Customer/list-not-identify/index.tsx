import { useState, FC } from 'react'
import { Col, Row } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useTranslation } from 'react-i18next'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import DetailButton from './component/DetailButton'
import { useListCustomerNotIdentify } from '~/hooks/customer-not-identify/useListCustomerNotIdentify'

interface INotIdentifyCustomer {
  id: string
  source: string
  companyCode: string
  companyName: string
  region: string
  address: string
  city: string
  district: string
  ward: string
  name: string
  email: string
  phone: string
  position: string
  firstAccessTime: string
  accessCount30Days: number
}

interface INotIdentifyCustomerFilter {
  pageIndex: number
  pageSize: number
  search?: string
}

type IProps = {}

const ListNotIdentifyView: FC<IProps> = () => {
  const { t } = useTranslation()
  const [filter, setFilter] = useState<INotIdentifyCustomerFilter>({
    pageIndex: 1,
    pageSize: 10
  })

  // TODO: Implement useListNotIdentifyCustomer hook
  const { data, isLoading, total } = useListCustomerNotIdentify()

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const columns: ColumnsType<INotIdentifyCustomer> = [
    {
      title: t('customer:customer_not_identify.columns.stt'),
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: t('customer:customer_not_identify.columns.source'),
      dataIndex: 'source',
      key: 'source',
      width: 120,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.id'),
      dataIndex: 'id',
      key: 'id',
      width: 100,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.companyCode'),
      dataIndex: 'companyCode',
      key: 'companyCode',
      width: 120,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.companyName'),
      dataIndex: 'companyName',
      key: 'companyName',
      width: 200,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.region'),
      dataIndex: 'region',
      key: 'region',
      width: 120,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.address'),
      dataIndex: 'address',
      key: 'address',
      width: 200,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.city'),
      dataIndex: 'city',
      key: 'city',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.district'),
      dataIndex: 'district',
      key: 'district',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.ward'),
      dataIndex: 'ward',
      key: 'ward',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.name'),
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.email'),
      dataIndex: 'email',
      key: 'email',
      width: 180,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.phone'),
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.position'),
      dataIndex: 'position',
      key: 'position',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer_not_identify.columns.firstAccessTime'),
      dataIndex: 'firstAccessTime',
      key: 'firstAccessTime',
      width: 150,
      align: 'center',
      render: (value: string) =>
        value ? new Date(value).toLocaleString('vi-VN') : '-'
    },
    {
      title: t('customer:customer_not_identify.columns.accessCount30Days'),
      dataIndex: 'accessCount30Days',
      key: 'accessCount30Days',
      width: 180,
      align: 'center',
      render: (value: number) => value || 0
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data?.data || []}
            total={total || 0}
            isLoading={isLoading}
            onPageChange={handlePageChange}
            scroll={{ x: 3000 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

export default ListNotIdentifyView
