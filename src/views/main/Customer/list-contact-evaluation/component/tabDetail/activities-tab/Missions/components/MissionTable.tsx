import React, { useState } from 'react'
import { Tag, Space, Button } from 'antd'
import { EyeOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'
import BaseTable from '../../../../../../../../../components/BaseTable'
import { IMission } from '../../../../../../../../../dto/mission.dto'

interface MissionTableProps {
  data: IMission[]
  loading?: boolean
}

const MissionTable: React.FC<MissionTableProps> = ({
  data,
  loading = false
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Hoàn thành':
        return 'success'
      case 'Đang thực hiện':
        return 'processing'
      case 'Chờ xử lý':
        return 'warning'
      default:
        return 'default'
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'Tư vấn':
        return 'blue'
      case 'Khảo sát':
        return 'green'
      case 'Đào tạo':
        return 'orange'
      case 'Bảo trì':
        return 'purple'
      case 'Triển khai':
        return 'cyan'
      case 'Hỗ trợ':
        return 'magenta'
      case 'Đánh giá':
        return 'volcano'
      case 'Cập nhật':
        return 'geekblue'
      default:
        return 'default'
    }
  }

  const columns = [
    {
      title: 'STT',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      render: (_: any, __: any, index: number) => index + 1
    },
    {
      title: 'ID hoạt động',
      dataIndex: 'activityId',
      key: 'activityId',
      width: 120
    },
    {
      title: 'Tiêu đề',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      ellipsis: true
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 250,
      ellipsis: true
    },
    {
      title: 'Loại',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      render: (type: string) => <Tag color={getTypeColor(type)}>{type}</Tag>
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>{status}</Tag>
      )
    },
    {
      title: 'Người tạo',
      dataIndex: 'creator',
      key: 'creator',
      width: 120
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120
    },
    {
      title: 'Mã khách hàng',
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 120
    },
    {
      title: 'Tên khách hàng',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 150,
      ellipsis: true
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 180,
      ellipsis: true
    },
    {
      title: 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 130
    }
  ]

  return (
    <div
      style={{
        marginTop: '16px',
        padding: '16px',
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid rgba(0,0,0,0.05)'
      }}>
      <h3 style={{ marginBottom: '16px', color: '#1890ff' }}>
        Danh sách Nhiệm vụ
      </h3>
      <BaseTable
        columns={columns}
        data={data}
        total={data.length}
        isLoading={loading}
        scroll={{ x: 1500 }}
        rowKey='id'
      />
    </div>
  )
}

export default MissionTable
