import { EyeOutlined } from '@ant-design/icons'
import BaseButton from '~/components/BaseButton'
import BaseModal from '~/components/BaseModal'
import { useModal } from '~/hooks/useModal'
import { ICustomerEvaluation } from '~/dto/customer-evaluation.dto'
import { TabsCustomerEvaluationDetail } from './tabs-customer-evaluation-detail'

interface DetailButtonProps {
  data: ICustomerEvaluation
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal()

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />
      <BaseModal
        open={open}
        title='Chi tiết tiêu chí đánh giá khách hàng'
        description='Thông tin chi tiết về tiêu chí đánh giá và phân loại khách hàng'
        onClose={closeModal}
        childrenBody={<TabsCustomerEvaluationDetail {...data} />}
      />
    </>
  )
}

export default DetailButton
