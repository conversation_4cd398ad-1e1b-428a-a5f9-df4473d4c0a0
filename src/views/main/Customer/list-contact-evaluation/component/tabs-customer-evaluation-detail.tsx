import {
  InfoCircleOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  WarningOutlined
} from '@ant-design/icons'
import { Space, Tabs, Typography } from 'antd'
import {
  GeneralInformation,
  CustomerInformation,
  ActivitiesInformation,
  RevenueInformation
} from './tabDetail'
import { ICustomerEvaluation } from '~/dto/customer-evaluation.dto'

const { Title } = Typography

export const TabsCustomerEvaluationDetail = (data: ICustomerEvaluation) => {
  return (
    <div>
      <Tabs
        defaultActiveKey='1'
        items={[
          {
            key: '1',
            label: (
              <Space>
                <InfoCircleOutlined />
                <span>Thông tin chung</span>
              </Space>
            ),
            children: <GeneralInformation data={data} />
          },
          {
            key: '2',
            label: (
              <Space>
                <PhoneOutlined />
                <span>khách hàng</span>
              </Space>
            ),
            children: <CustomerInformation />
          },
          {
            key: '3',
            label: (
              <Space>
                <WarningOutlined />
                <span>Hoạt động</span>
              </Space>
            ),
            children: <ActivitiesInformation />
          },
          {
            key: '4',
            label: (
              <Space>
                <EnvironmentOutlined />
                <span>Do<PERSON>h số</span>
              </Space>
            ),
            children: <RevenueInformation />
          }
        ]}
      />
    </div>
  )
}
