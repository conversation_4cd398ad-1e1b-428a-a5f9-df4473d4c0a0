import { useState, FC } from 'react'
import { Col, Row } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useTranslation } from 'react-i18next'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { ICustomer } from '~/dto/customer.dto'
import { BaseButton } from '~/components'
import { CloseOutlined, CheckOutlined, DeleteOutlined } from '@ant-design/icons'
import { BaseCheckbox } from '~/components'
import { DetailButton, EditButton } from './component'
import { useContactEvaluation } from '~/hooks/contact-evaluation/useContactEvaluation'
import { IContactEvaluation } from '~/dto/contact-evaluation.dto'

interface IFilterContactEvaluation {
  pageIndex: number
  pageSize: number
}

type IProps = {}

const ListContactEvaluationView: FC<IProps> = () => {
  const { t } = useTranslation()
  const [filter, setFilter] = useState<IFilterContactEvaluation>({
    pageIndex: 1,
    pageSize: 10
  })

  const { data, isLoading, total } = useContactEvaluation()

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const handleDelete = (record: ICustomer) => {
    console.log(record)
  }

  const columns: ColumnsType<IContactEvaluation> = [
    {
      title: t('customer:contact_evaluation.columns.stt'),
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: t('customer:contact_evaluation.columns.code'),
      dataIndex: 'code',
      key: 'code',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:contact_evaluation.columns.name'),
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center'
    },
    {
      title: t('customer:contact_evaluation.columns.customerCount'),
      dataIndex: 'customerCount',
      key: 'customerCount',
      width: 180,
      align: 'center'
    },
    {
      title: t('customer:contact_evaluation.columns.status'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: (value: boolean) => {
        return value ? (
          <CheckOutlined style={{ color: 'green' }} />
        ) : (
          <CloseOutlined style={{ color: 'red' }} />
        )
      }
    },
    {
      title: t('customer:contact_evaluation.columns.companyName'),
      dataIndex: 'companyName',
      key: 'companyName',
      width: 200,
      align: 'center'
    },
    {
      title: t('customer:contact_evaluation.columns.createdBy'),
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:contact_evaluation.columns.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      align: 'center',
      render: (value: string) =>
        value ? new Date(value).toLocaleDateString('vi-VN') : '-'
    },
    {
      title: t('customer:contact_evaluation.columns.action'),
      key: 'action',
      width: 150,
      fixed: 'right',
      align: 'center',
      render: (value: any, record: any, index: number) => {
        return (
          <>
            <EditButton data={record} />
            <DetailButton data={record} />
            <BaseButton
              danger
              type='primary'
              shape='circle'
              icon={<DeleteOutlined />}
              tooltip='Delete'
              onClick={() => handleDelete(record)}
            />
          </>
        )
      }
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data?.data || []}
            total={total || 0}
            isLoading={isLoading}
            onPageChange={handlePageChange}
            scroll={{ x: 1000 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

export default ListContactEvaluationView
