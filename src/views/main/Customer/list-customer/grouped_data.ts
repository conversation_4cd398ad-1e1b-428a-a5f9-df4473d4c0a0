export class CustomerData {
  undefinedCustomers = [
    {
      id: '11',
      code: 'KH011',
      name: '<PERSON>ông ty TNHH Alpha',
      shortName: 'Alpha',
      phone: '028 1000 501439',
      email: '<EMAIL>',
      address: '890 <PERSON><PERSON><PERSON><PERSON>, Quận 1, TP.HCM',
      customerType: '<PERSON><PERSON><PERSON> nghiệp',
      salesRep: '',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'CNTT',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.alpha.com',
      taxCode: '7067491829',
      market: 'domestic',
      customerGroup: 'Chưa phân nhóm',
      visitDate: '2024-03-20T00:00:00',
      note: 'Kh<PERSON>ch hàng chưa định danh',
      sapCode: 'SAP011'
    },
    {
      id: '12',
      code: 'KH012',
      name: '<PERSON><PERSON>ng ty TNHH Beta',
      shortName: 'Beta',
      phone: '028 1001 603507',
      email: '<EMAIL>',
      address: '590 <PERSON><PERSON><PERSON><PERSON>, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: '',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Bán lẻ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.beta.com',
      taxCode: '4795921259',
      market: 'domestic',
      customerGroup: 'Chưa phân nhóm',
      visitDate: '2024-03-21T00:00:00',
      note: 'Khách hàng chưa định danh',
      sapCode: 'SAP012'
    },
    {
      id: '13',
      code: 'KH013',
      name: 'Công ty TNHH Gamma',
      shortName: 'Gamma',
      phone: '028 1002 911843',
      email: '<EMAIL>',
      address: '489 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: '',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Logistics',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.gamma.com',
      taxCode: '6498327055',
      market: 'domestic',
      customerGroup: 'Chưa phân nhóm',
      visitDate: '2024-03-22T00:00:00',
      note: 'Khách hàng chưa định danh',
      sapCode: 'SAP013'
    },
    {
      id: '14',
      code: 'KH014',
      name: 'Công ty TNHH Delta',
      shortName: 'Delta',
      phone: '028 1003 171072',
      email: '<EMAIL>',
      address: '835 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: '',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Thương mại',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.delta.com',
      taxCode: '8418654164',
      market: 'domestic',
      customerGroup: 'Chưa phân nhóm',
      visitDate: '2024-03-23T00:00:00',
      note: 'Khách hàng chưa định danh',
      sapCode: 'SAP014'
    },
    {
      id: '15',
      code: 'KH015',
      name: 'Công ty TNHH Epsilon',
      shortName: 'Epsilon',
      phone: '028 1004 206404',
      email: '<EMAIL>',
      address: '138 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: '',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Sản xuất',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.epsilon.com',
      taxCode: '4989034734',
      market: 'domestic',
      customerGroup: 'Chưa phân nhóm',
      visitDate: '2024-03-24T00:00:00',
      note: 'Khách hàng chưa định danh',
      sapCode: 'SAP015'
    },
    {
      id: '16',
      code: 'KH016',
      name: 'Công ty TNHH Zeta',
      shortName: 'Zeta',
      phone: '028 1005 550195',
      email: '<EMAIL>',
      address: '814 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: '',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Dịch vụ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.zeta.com',
      taxCode: '4465971473',
      market: 'domestic',
      customerGroup: 'Chưa phân nhóm',
      visitDate: '2024-03-25T00:00:00',
      note: 'Khách hàng chưa định danh',
      sapCode: 'SAP016'
    },
    {
      id: '17',
      code: 'KH017',
      name: 'Công ty TNHH Eta',
      shortName: 'Eta',
      phone: '028 1006 646783',
      email: '<EMAIL>',
      address: '326 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: '',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Tài chính',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.eta.com',
      taxCode: '2399085135',
      market: 'domestic',
      customerGroup: 'Chưa phân nhóm',
      visitDate: '2024-03-26T00:00:00',
      note: 'Khách hàng chưa định danh',
      sapCode: 'SAP017'
    },
    {
      id: '18',
      code: 'KH018',
      name: 'Công ty TNHH Theta',
      shortName: 'Theta',
      phone: '028 1007 626252',
      email: '<EMAIL>',
      address: '262 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: '',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Y tế',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.theta.com',
      taxCode: '3714171379',
      market: 'domestic',
      customerGroup: 'Chưa phân nhóm',
      visitDate: '2024-03-27T00:00:00',
      note: 'Khách hàng chưa định danh',
      sapCode: 'SAP018'
    },
    {
      id: '19',
      code: 'KH019',
      name: 'Công ty TNHH Iota',
      shortName: 'Iota',
      phone: '028 1008 739558',
      email: '<EMAIL>',
      address: '613 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: '',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Giáo dục',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.iota.com',
      taxCode: '5029697812',
      market: 'domestic',
      customerGroup: 'Chưa phân nhóm',
      visitDate: '2024-03-28T00:00:00',
      note: 'Khách hàng chưa định danh',
      sapCode: 'SAP019'
    },
    {
      id: '20',
      code: 'KH020',
      name: 'Công ty TNHH Kappa',
      shortName: 'Kappa',
      phone: '028 1009 537032',
      email: '<EMAIL>',
      address: '146 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: '',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Viễn thông',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.kappa.com',
      taxCode: '5554076232',
      market: 'domestic',
      customerGroup: 'Chưa phân nhóm',
      visitDate: '2024-03-29T00:00:00',
      note: 'Khách hàng chưa định danh',
      sapCode: 'SAP020'
    }
  ]

  potentialCustomers = [
    {
      id: '21',
      code: 'KH021',
      name: 'Công ty TNHH Alpha',
      shortName: 'Alpha',
      phone: '028 1000 565073',
      email: '<EMAIL>',
      address: '508 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Nguyễn Văn A',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'Website',
      industry: 'CNTT',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.alpha.com',
      taxCode: '5317039976',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-20T00:00:00',
      note: 'Tiềm năng',
      sapCode: 'SAP021'
    },
    {
      id: '22',
      code: 'KH022',
      name: 'Công ty TNHH Beta',
      shortName: 'Beta',
      phone: '028 1001 473249',
      email: '<EMAIL>',
      address: '210 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Nguyễn Văn A',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'Website',
      industry: 'Bán lẻ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.beta.com',
      taxCode: '6012653013',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-21T00:00:00',
      note: 'Tiềm năng',
      sapCode: 'SAP022'
    },
    {
      id: '23',
      code: 'KH023',
      name: 'Công ty TNHH Gamma',
      shortName: 'Gamma',
      phone: '028 1002 698494',
      email: '<EMAIL>',
      address: '484 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Nguyễn Văn A',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'Website',
      industry: 'Logistics',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.gamma.com',
      taxCode: '6707369091',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-22T00:00:00',
      note: 'Tiềm năng',
      sapCode: 'SAP023'
    },
    {
      id: '24',
      code: 'KH024',
      name: 'Công ty TNHH Delta',
      shortName: 'Delta',
      phone: '028 1003 490025',
      email: '<EMAIL>',
      address: '146 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Nguyễn Văn A',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'Website',
      industry: 'Thương mại',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.delta.com',
      taxCode: '2879864025',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-23T00:00:00',
      note: 'Tiềm năng',
      sapCode: 'SAP024'
    },
    {
      id: '25',
      code: 'KH025',
      name: 'Công ty TNHH Epsilon',
      shortName: 'Epsilon',
      phone: '028 1004 495953',
      email: '<EMAIL>',
      address: '269 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Nguyễn Văn A',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'Website',
      industry: 'Sản xuất',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.epsilon.com',
      taxCode: '1389323322',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-24T00:00:00',
      note: 'Tiềm năng',
      sapCode: 'SAP025'
    },
    {
      id: '26',
      code: 'KH026',
      name: 'Công ty TNHH Zeta',
      shortName: 'Zeta',
      phone: '028 1005 862197',
      email: '<EMAIL>',
      address: '618 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Nguyễn Văn A',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'Website',
      industry: 'Dịch vụ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.zeta.com',
      taxCode: '3954409981',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-25T00:00:00',
      note: 'Tiềm năng',
      sapCode: 'SAP026'
    },
    {
      id: '27',
      code: 'KH027',
      name: 'Công ty TNHH Eta',
      shortName: 'Eta',
      phone: '028 1006 817646',
      email: '<EMAIL>',
      address: '510 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Nguyễn Văn A',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'Website',
      industry: 'Tài chính',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.eta.com',
      taxCode: '4733234945',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-26T00:00:00',
      note: 'Tiềm năng',
      sapCode: 'SAP027'
    },
    {
      id: '28',
      code: 'KH028',
      name: 'Công ty TNHH Theta',
      shortName: 'Theta',
      phone: '028 1007 279112',
      email: '<EMAIL>',
      address: '553 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Nguyễn Văn A',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'Website',
      industry: 'Y tế',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.theta.com',
      taxCode: '3486942500',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-27T00:00:00',
      note: 'Tiềm năng',
      sapCode: 'SAP028'
    },
    {
      id: '29',
      code: 'KH029',
      name: 'Công ty TNHH Iota',
      shortName: 'Iota',
      phone: '028 1008 878588',
      email: '<EMAIL>',
      address: '666 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Nguyễn Văn A',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'Website',
      industry: 'Giáo dục',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.iota.com',
      taxCode: '2054338324',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-28T00:00:00',
      note: 'Tiềm năng',
      sapCode: 'SAP029'
    },
    {
      id: '30',
      code: 'KH030',
      name: 'Công ty TNHH Kappa',
      shortName: 'Kappa',
      phone: '028 1009 945384',
      email: '<EMAIL>',
      address: '89 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Nguyễn Văn A',
      department: 'Phòng Kinh doanh',
      ranking: 'B',
      source: 'Website',
      industry: 'Viễn thông',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.kappa.com',
      taxCode: '6240079245',
      market: 'domestic',
      customerGroup: 'Nhóm B',
      visitDate: '2024-03-29T00:00:00',
      note: 'Tiềm năng',
      sapCode: 'SAP030'
    }
  ]

  qualifiedCustomers = [
    {
      id: '31',
      code: 'KH031',
      name: 'Công ty TNHH Alpha',
      shortName: 'Alpha',
      phone: '028 1000 120852',
      email: '<EMAIL>',
      address: '24 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Trần Thị B',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'CNTT',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.alpha.com',
      taxCode: '8603489268',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-20T00:00:00',
      note: 'Đủ điều kiện bán hàng',
      sapCode: 'SAP031'
    },
    {
      id: '32',
      code: 'KH032',
      name: 'Công ty TNHH Beta',
      shortName: 'Beta',
      phone: '028 1001 176754',
      email: '<EMAIL>',
      address: '153 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Trần Thị B',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Bán lẻ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.beta.com',
      taxCode: '4914017155',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-21T00:00:00',
      note: 'Đủ điều kiện bán hàng',
      sapCode: 'SAP032'
    },
    {
      id: '33',
      code: 'KH033',
      name: 'Công ty TNHH Gamma',
      shortName: 'Gamma',
      phone: '028 1002 533059',
      email: '<EMAIL>',
      address: '349 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Trần Thị B',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Logistics',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.gamma.com',
      taxCode: '7409368365',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-22T00:00:00',
      note: 'Đủ điều kiện bán hàng',
      sapCode: 'SAP033'
    },
    {
      id: '34',
      code: 'KH034',
      name: 'Công ty TNHH Delta',
      shortName: 'Delta',
      phone: '028 1003 713550',
      email: '<EMAIL>',
      address: '463 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Trần Thị B',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Thương mại',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.delta.com',
      taxCode: '1735556192',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-23T00:00:00',
      note: 'Đủ điều kiện bán hàng',
      sapCode: 'SAP034'
    },
    {
      id: '35',
      code: 'KH035',
      name: 'Công ty TNHH Epsilon',
      shortName: 'Epsilon',
      phone: '028 1004 377752',
      email: '<EMAIL>',
      address: '56 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Trần Thị B',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Sản xuất',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.epsilon.com',
      taxCode: '7891543680',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-24T00:00:00',
      note: 'Đủ điều kiện bán hàng',
      sapCode: 'SAP035'
    },
    {
      id: '36',
      code: 'KH036',
      name: 'Công ty TNHH Zeta',
      shortName: 'Zeta',
      phone: '028 1005 260287',
      email: '<EMAIL>',
      address: '538 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Trần Thị B',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Dịch vụ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.zeta.com',
      taxCode: '4067126865',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-25T00:00:00',
      note: 'Đủ điều kiện bán hàng',
      sapCode: 'SAP036'
    },
    {
      id: '37',
      code: 'KH037',
      name: 'Công ty TNHH Eta',
      shortName: 'Eta',
      phone: '028 1006 203672',
      email: '<EMAIL>',
      address: '566 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Trần Thị B',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Tài chính',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.eta.com',
      taxCode: '5574300865',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-26T00:00:00',
      note: 'Đủ điều kiện bán hàng',
      sapCode: 'SAP037'
    },
    {
      id: '38',
      code: 'KH038',
      name: 'Công ty TNHH Theta',
      shortName: 'Theta',
      phone: '028 1007 946892',
      email: '<EMAIL>',
      address: '307 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Trần Thị B',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Y tế',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.theta.com',
      taxCode: '2155740771',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-27T00:00:00',
      note: 'Đủ điều kiện bán hàng',
      sapCode: 'SAP038'
    },
    {
      id: '39',
      code: 'KH039',
      name: 'Công ty TNHH Iota',
      shortName: 'Iota',
      phone: '028 1008 863776',
      email: '<EMAIL>',
      address: '810 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Trần Thị B',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Giáo dục',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.iota.com',
      taxCode: '5448519004',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-28T00:00:00',
      note: 'Đủ điều kiện bán hàng',
      sapCode: 'SAP039'
    },
    {
      id: '40',
      code: 'KH040',
      name: 'Công ty TNHH Kappa',
      shortName: 'Kappa',
      phone: '028 1009 654496',
      email: '<EMAIL>',
      address: '616 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Trần Thị B',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Viễn thông',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.kappa.com',
      taxCode: '7167842550',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-29T00:00:00',
      note: 'Đủ điều kiện bán hàng',
      sapCode: 'SAP040'
    }
  ]

  inProgress = [
    {
      id: '41',
      code: 'KH041',
      name: 'Công ty TNHH Alpha',
      shortName: 'Alpha',
      phone: '028 1000 919611',
      email: '<EMAIL>',
      address: '607 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Lê Văn C',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'CNTT',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.alpha.com',
      taxCode: '1065743223',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-20T00:00:00',
      note: 'Cơ hội',
      sapCode: 'SAP041'
    },
    {
      id: '42',
      code: 'KH042',
      name: 'Công ty TNHH Beta',
      shortName: 'Beta',
      phone: '028 1001 884244',
      email: '<EMAIL>',
      address: '740 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Lê Văn C',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Bán lẻ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.beta.com',
      taxCode: '8799415804',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-21T00:00:00',
      note: 'Cơ hội',
      sapCode: 'SAP042'
    },
    {
      id: '43',
      code: 'KH043',
      name: 'Công ty TNHH Gamma',
      shortName: 'Gamma',
      phone: '028 1002 875412',
      email: '<EMAIL>',
      address: '803 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Lê Văn C',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Logistics',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.gamma.com',
      taxCode: '2377639266',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-22T00:00:00',
      note: 'Cơ hội',
      sapCode: 'SAP043'
    },
    {
      id: '44',
      code: 'KH044',
      name: 'Công ty TNHH Delta',
      shortName: 'Delta',
      phone: '028 1003 869120',
      email: '<EMAIL>',
      address: '322 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Lê Văn C',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Thương mại',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.delta.com',
      taxCode: '6904128525',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-23T00:00:00',
      note: 'Cơ hội',
      sapCode: 'SAP044'
    },
    {
      id: '45',
      code: 'KH045',
      name: 'Công ty TNHH Epsilon',
      shortName: 'Epsilon',
      phone: '028 1004 116025',
      email: '<EMAIL>',
      address: '541 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Lê Văn C',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Sản xuất',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.epsilon.com',
      taxCode: '6923312924',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-24T00:00:00',
      note: 'Cơ hội',
      sapCode: 'SAP045'
    },
    {
      id: '46',
      code: 'KH046',
      name: 'Công ty TNHH Zeta',
      shortName: 'Zeta',
      phone: '028 1005 291064',
      email: '<EMAIL>',
      address: '304 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Lê Văn C',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Dịch vụ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.zeta.com',
      taxCode: '1643181153',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-25T00:00:00',
      note: 'Cơ hội',
      sapCode: 'SAP046'
    },
    {
      id: '47',
      code: 'KH047',
      name: 'Công ty TNHH Eta',
      shortName: 'Eta',
      phone: '028 1006 599351',
      email: '<EMAIL>',
      address: '356 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Lê Văn C',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Tài chính',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.eta.com',
      taxCode: '1424146721',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-26T00:00:00',
      note: 'Cơ hội',
      sapCode: 'SAP047'
    },
    {
      id: '48',
      code: 'KH048',
      name: 'Công ty TNHH Theta',
      shortName: 'Theta',
      phone: '028 1007 231385',
      email: '<EMAIL>',
      address: '124 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Lê Văn C',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Y tế',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.theta.com',
      taxCode: '1649362071',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-27T00:00:00',
      note: 'Cơ hội',
      sapCode: 'SAP048'
    },
    {
      id: '49',
      code: 'KH049',
      name: 'Công ty TNHH Iota',
      shortName: 'Iota',
      phone: '028 1008 703093',
      email: '<EMAIL>',
      address: '130 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Lê Văn C',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Giáo dục',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.iota.com',
      taxCode: '3359367812',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-28T00:00:00',
      note: 'Cơ hội',
      sapCode: 'SAP049'
    },
    {
      id: '50',
      code: 'KH050',
      name: 'Công ty TNHH Kappa',
      shortName: 'Kappa',
      phone: '028 1009 705303',
      email: '<EMAIL>',
      address: '647 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Lê Văn C',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Viễn thông',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.kappa.com',
      taxCode: '9386911999',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-29T00:00:00',
      note: 'Cơ hội',
      sapCode: 'SAP050'
    }
  ]

  officialCustomers = [
    {
      id: '51',
      code: 'KH051',
      name: 'Công ty TNHH Alpha',
      shortName: 'Alpha',
      phone: '028 1000 211237',
      email: '<EMAIL>',
      address: '788 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Hoàng Văn D',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'CNTT',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.alpha.com',
      taxCode: '1208544025',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-20T00:00:00',
      note: 'Khách hàng chính thức',
      sapCode: 'SAP051'
    },
    {
      id: '52',
      code: 'KH052',
      name: 'Công ty TNHH Beta',
      shortName: 'Beta',
      phone: '028 1001 875980',
      email: '<EMAIL>',
      address: '509 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Hoàng Văn D',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Bán lẻ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.beta.com',
      taxCode: '5100170715',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-21T00:00:00',
      note: 'Khách hàng chính thức',
      sapCode: 'SAP052'
    },
    {
      id: '53',
      code: 'KH053',
      name: 'Công ty TNHH Gamma',
      shortName: 'Gamma',
      phone: '028 1002 683995',
      email: '<EMAIL>',
      address: '860 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Hoàng Văn D',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Logistics',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.gamma.com',
      taxCode: '5559163247',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-22T00:00:00',
      note: 'Khách hàng chính thức',
      sapCode: 'SAP053'
    },
    {
      id: '54',
      code: 'KH054',
      name: 'Công ty TNHH Delta',
      shortName: 'Delta',
      phone: '028 1003 744969',
      email: '<EMAIL>',
      address: '29 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Hoàng Văn D',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Thương mại',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.delta.com',
      taxCode: '9908161800',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-23T00:00:00',
      note: 'Khách hàng chính thức',
      sapCode: 'SAP054'
    },
    {
      id: '55',
      code: 'KH055',
      name: 'Công ty TNHH Epsilon',
      shortName: 'Epsilon',
      phone: '028 1004 345569',
      email: '<EMAIL>',
      address: '875 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Hoàng Văn D',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Sản xuất',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.epsilon.com',
      taxCode: '3794441909',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-24T00:00:00',
      note: 'Khách hàng chính thức',
      sapCode: 'SAP055'
    },
    {
      id: '56',
      code: 'KH056',
      name: 'Công ty TNHH Zeta',
      shortName: 'Zeta',
      phone: '028 1005 215536',
      email: '<EMAIL>',
      address: '694 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Hoàng Văn D',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Dịch vụ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.zeta.com',
      taxCode: '7407144032',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-25T00:00:00',
      note: 'Khách hàng chính thức',
      sapCode: 'SAP056'
    },
    {
      id: '57',
      code: 'KH057',
      name: 'Công ty TNHH Eta',
      shortName: 'Eta',
      phone: '028 1006 370108',
      email: '<EMAIL>',
      address: '925 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Hoàng Văn D',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Tài chính',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.eta.com',
      taxCode: '5954278492',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-26T00:00:00',
      note: 'Khách hàng chính thức',
      sapCode: 'SAP057'
    },
    {
      id: '58',
      code: 'KH058',
      name: 'Công ty TNHH Theta',
      shortName: 'Theta',
      phone: '028 1007 837006',
      email: '<EMAIL>',
      address: '762 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Hoàng Văn D',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Y tế',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.theta.com',
      taxCode: '9557397299',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-27T00:00:00',
      note: 'Khách hàng chính thức',
      sapCode: 'SAP058'
    },
    {
      id: '59',
      code: 'KH059',
      name: 'Công ty TNHH Iota',
      shortName: 'Iota',
      phone: '028 1008 633489',
      email: '<EMAIL>',
      address: '335 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Hoàng Văn D',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Giáo dục',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.iota.com',
      taxCode: '3567818171',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-28T00:00:00',
      note: 'Khách hàng chính thức',
      sapCode: 'SAP059'
    },
    {
      id: '60',
      code: 'KH060',
      name: 'Công ty TNHH Kappa',
      shortName: 'Kappa',
      phone: '028 1009 810089',
      email: '<EMAIL>',
      address: '143 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Hoàng Văn D',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Viễn thông',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.kappa.com',
      taxCode: '8133587627',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-29T00:00:00',
      note: 'Khách hàng chính thức',
      sapCode: 'SAP060'
    }
  ]

  careAndDevelop = [
    {
      id: '61',
      code: 'KH061',
      name: 'Công ty TNHH Alpha',
      shortName: 'Alpha',
      phone: '028 1000 547197',
      email: '<EMAIL>',
      address: '44 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Phạm Thị E',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'CNTT',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.alpha.com',
      taxCode: '2939395711',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-20T00:00:00',
      note: 'Chăm sóc & phát triển',
      sapCode: 'SAP061'
    },
    {
      id: '62',
      code: 'KH062',
      name: 'Công ty TNHH Beta',
      shortName: 'Beta',
      phone: '028 1001 100591',
      email: '<EMAIL>',
      address: '790 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Phạm Thị E',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Bán lẻ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.beta.com',
      taxCode: '9286458331',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-21T00:00:00',
      note: 'Chăm sóc & phát triển',
      sapCode: 'SAP062'
    },
    {
      id: '63',
      code: 'KH063',
      name: 'Công ty TNHH Gamma',
      shortName: 'Gamma',
      phone: '028 1002 323938',
      email: '<EMAIL>',
      address: '298 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Phạm Thị E',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Logistics',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.gamma.com',
      taxCode: '7548823428',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-22T00:00:00',
      note: 'Chăm sóc & phát triển',
      sapCode: 'SAP063'
    },
    {
      id: '64',
      code: 'KH064',
      name: 'Công ty TNHH Delta',
      shortName: 'Delta',
      phone: '028 1003 789248',
      email: '<EMAIL>',
      address: '748 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Phạm Thị E',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Thương mại',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.delta.com',
      taxCode: '4888460188',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-23T00:00:00',
      note: 'Chăm sóc & phát triển',
      sapCode: 'SAP064'
    },
    {
      id: '65',
      code: 'KH065',
      name: 'Công ty TNHH Epsilon',
      shortName: 'Epsilon',
      phone: '028 1004 496800',
      email: '<EMAIL>',
      address: '801 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Phạm Thị E',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Sản xuất',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.epsilon.com',
      taxCode: '2709254120',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-24T00:00:00',
      note: 'Chăm sóc & phát triển',
      sapCode: 'SAP065'
    },
    {
      id: '66',
      code: 'KH066',
      name: 'Công ty TNHH Zeta',
      shortName: 'Zeta',
      phone: '028 1005 856085',
      email: '<EMAIL>',
      address: '30 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Phạm Thị E',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Dịch vụ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.zeta.com',
      taxCode: '7948052933',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-25T00:00:00',
      note: 'Chăm sóc & phát triển',
      sapCode: 'SAP066'
    },
    {
      id: '67',
      code: 'KH067',
      name: 'Công ty TNHH Eta',
      shortName: 'Eta',
      phone: '028 1006 651446',
      email: '<EMAIL>',
      address: '507 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Phạm Thị E',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Tài chính',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.eta.com',
      taxCode: '3092027554',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-26T00:00:00',
      note: 'Chăm sóc & phát triển',
      sapCode: 'SAP067'
    },
    {
      id: '68',
      code: 'KH068',
      name: 'Công ty TNHH Theta',
      shortName: 'Theta',
      phone: '028 1007 683350',
      email: '<EMAIL>',
      address: '405 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Phạm Thị E',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Y tế',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.theta.com',
      taxCode: '5329226759',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-27T00:00:00',
      note: 'Chăm sóc & phát triển',
      sapCode: 'SAP068'
    },
    {
      id: '69',
      code: 'KH069',
      name: 'Công ty TNHH Iota',
      shortName: 'Iota',
      phone: '028 1008 827996',
      email: '<EMAIL>',
      address: '764 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Phạm Thị E',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Giáo dục',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.iota.com',
      taxCode: '7806668178',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-28T00:00:00',
      note: 'Chăm sóc & phát triển',
      sapCode: 'SAP069'
    },
    {
      id: '70',
      code: 'KH070',
      name: 'Công ty TNHH Kappa',
      shortName: 'Kappa',
      phone: '028 1009 263553',
      email: '<EMAIL>',
      address: '975 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Phạm Thị E',
      department: 'Phòng Kinh doanh',
      ranking: 'A',
      source: 'Website',
      industry: 'Viễn thông',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.kappa.com',
      taxCode: '9058440464',
      market: 'domestic',
      customerGroup: 'Nhóm A',
      visitDate: '2024-03-29T00:00:00',
      note: 'Chăm sóc & phát triển',
      sapCode: 'SAP070'
    }
  ]

  failed = [
    {
      id: '71',
      code: 'KH071',
      name: 'Công ty TNHH Alpha',
      shortName: 'Alpha',
      phone: '028 1000 606011',
      email: '<EMAIL>',
      address: '138 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Vũ Văn F',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'CNTT',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.alpha.com',
      taxCode: '1265282304',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-20T00:00:00',
      note: 'Không thành công',
      sapCode: 'SAP071'
    },
    {
      id: '72',
      code: 'KH072',
      name: 'Công ty TNHH Beta',
      shortName: 'Beta',
      phone: '028 1001 688819',
      email: '<EMAIL>',
      address: '66 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Vũ Văn F',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Bán lẻ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.beta.com',
      taxCode: '1944631894',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-21T00:00:00',
      note: 'Không thành công',
      sapCode: 'SAP072'
    },
    {
      id: '73',
      code: 'KH073',
      name: 'Công ty TNHH Gamma',
      shortName: 'Gamma',
      phone: '028 1002 181094',
      email: '<EMAIL>',
      address: '619 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Vũ Văn F',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Logistics',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.gamma.com',
      taxCode: '9632330155',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-22T00:00:00',
      note: 'Không thành công',
      sapCode: 'SAP073'
    },
    {
      id: '74',
      code: 'KH074',
      name: 'Công ty TNHH Delta',
      shortName: 'Delta',
      phone: '028 1003 186055',
      email: '<EMAIL>',
      address: '841 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Vũ Văn F',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Thương mại',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.delta.com',
      taxCode: '4248025518',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-23T00:00:00',
      note: 'Không thành công',
      sapCode: 'SAP074'
    },
    {
      id: '75',
      code: 'KH075',
      name: 'Công ty TNHH Epsilon',
      shortName: 'Epsilon',
      phone: '028 1004 400254',
      email: '<EMAIL>',
      address: '139 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Vũ Văn F',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Sản xuất',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.epsilon.com',
      taxCode: '2863802719',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-24T00:00:00',
      note: 'Không thành công',
      sapCode: 'SAP075'
    },
    {
      id: '76',
      code: 'KH076',
      name: 'Công ty TNHH Zeta',
      shortName: 'Zeta',
      phone: '028 1005 241740',
      email: '<EMAIL>',
      address: '210 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Vũ Văn F',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Dịch vụ',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.zeta.com',
      taxCode: '7563593141',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-25T00:00:00',
      note: 'Không thành công',
      sapCode: 'SAP076'
    },
    {
      id: '77',
      code: 'KH077',
      name: 'Công ty TNHH Eta',
      shortName: 'Eta',
      phone: '028 1006 220287',
      email: '<EMAIL>',
      address: '33 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Vũ Văn F',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Tài chính',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.eta.com',
      taxCode: '3200893838',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-26T00:00:00',
      note: 'Không thành công',
      sapCode: 'SAP077'
    },
    {
      id: '78',
      code: 'KH078',
      name: 'Công ty TNHH Theta',
      shortName: 'Theta',
      phone: '028 1007 404987',
      email: '<EMAIL>',
      address: '967 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Vũ Văn F',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Y tế',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.theta.com',
      taxCode: '4425189279',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-27T00:00:00',
      note: 'Không thành công',
      sapCode: 'SAP078'
    },
    {
      id: '79',
      code: 'KH079',
      name: 'Công ty TNHH Iota',
      shortName: 'Iota',
      phone: '028 1008 564088',
      email: '<EMAIL>',
      address: '37 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Vũ Văn F',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Giáo dục',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.iota.com',
      taxCode: '9562515346',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-28T00:00:00',
      note: 'Không thành công',
      sapCode: 'SAP079'
    },
    {
      id: '80',
      code: 'KH080',
      name: 'Công ty TNHH Kappa',
      shortName: 'Kappa',
      phone: '028 1009 443344',
      email: '<EMAIL>',
      address: '199 Nguyễn Huệ, Quận 1, TP.HCM',
      customerType: 'Doanh nghiệp',
      salesRep: 'Vũ Văn F',
      department: 'Phòng Kinh doanh',
      ranking: 'C',
      source: 'Website',
      industry: 'Viễn thông',
      region: 'Miền Nam',
      createdBy: 'Admin',
      createdAt: '2024-03-20T00:00:00',
      updatedAt: '2024-03-20T00:00:00',
      website: 'www.kappa.com',
      taxCode: '7282763180',
      market: 'domestic',
      customerGroup: 'Nhóm C',
      visitDate: '2024-03-29T00:00:00',
      note: 'Không thành công',
      sapCode: 'SAP080'
    }
  ]

  allCustomers = [
    ...this.undefinedCustomers,
    ...this.potentialCustomers,
    ...this.qualifiedCustomers,
    ...this.inProgress,
    ...this.officialCustomers,
    ...this.careAndDevelop,
    ...this.failed
  ]
}
