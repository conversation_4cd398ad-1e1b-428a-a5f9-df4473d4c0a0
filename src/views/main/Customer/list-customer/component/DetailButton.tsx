import { EyeOutlined } from '@ant-design/icons'
import BaseButton from '~/components/BaseButton'
import BaseModal from '~/components/BaseModal'
import { ICustomer } from '~/dto/customer.dto'
import { useModal } from '~/hooks/useModal'
import { TabsCustomerDetail } from './Tabs-customer-detail'
import { useTranslation } from 'react-i18next'

interface DetailButtonProps {
  data: ICustomer
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { t } = useTranslation()
  const { open, openModal, closeModal } = useModal()

  if (!data) return null

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />
      <BaseModal
        open={open}
        title={t('customer:customer.customer_detail.title')}
        description={t('customer:customer.customer_detail.description')}
        onClose={closeModal}
        childrenBody={<TabsCustomerDetail data={data} />}
      />
    </>
  )
}

export default DetailButton
