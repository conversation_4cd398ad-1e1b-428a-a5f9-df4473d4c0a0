import { FC } from 'react'
import { Card, Col, Row, Tag, Space, Table } from 'antd'
import { Descriptions } from 'antd'
import { useTranslation } from 'react-i18next'
import {
  AppstoreOutlined,
  TagOutlined,
  CalendarOutlined,
  StarOutlined,
  FileTextOutlined,
  BankOutlined
} from '@ant-design/icons'
import { GlobalOutlined } from '@ant-design/icons'
import { TeamOutlined } from '@ant-design/icons'
import { ShopOutlined } from '@ant-design/icons'
import BaseText from '~/components/BaseText'
import { BarcodeOutlined } from '@ant-design/icons'
import { UserOutlined } from '@ant-design/icons'
import { ICustomer } from '~/dto/customer.dto'
import { formatDateCustom } from '~/common/helper/helper'

type IProps = {
  data: ICustomer
}

export const InformationTab: FC<IProps> = (props: IProps) => {
  const { t } = useTranslation()
  const { data } = props
  const {
    name,
    phone,
    address,
    customerType,
    salesRep,
    department,
    ranking,
    source,
    industry,
    region,
    createdAt,
    updatedAt,
    code,
    shortName,
    taxCode,
    market,
    customerGroup,
    visitDate,
    note,
    website,
    sapCode
  } = data

  const tableData = [
    {
      key: '1',
      companyCode: 'COMP001',
      companyBranch_Code: 'COMP001',
      companyBranch_Name: 'Chi nhánh 1',
      customerType: 'Doanh nghiệp',
      customerGroup: 'Nhóm A',
      salesRep: 'Nguyễn Văn A'
    },
    {
      key: '2',
      companyCode: 'COMP002',
      companyBranch_Code: 'COMP002',
      companyBranch_Name: 'Chi nhánh 2',
      customerType: 'Cá nhân',
      customerGroup: 'Nhóm B',
      salesRep: 'Trần Thị B'
    }
  ]

  const columns = [
    {
      title: t('customer:customer.customer_detail.information_tab.fields.companyBranch_Code') || 'Mã chi nhánh',
      dataIndex: 'companyBranch_Code',
      key: 'companyBranch_Code'
    },
    {
      title: t('customer:customer.customer_detail.information_tab.fields.companyBranch_Name'),
      dataIndex: 'companyBranch_Name',
      key: 'companyBranch_Name'
    },
    // {
    //   title: t('customer:customer.customer_detail.information_tab.fields.customerGroup'),
    //   dataIndex: 'customerGroup',
    //   key: 'customerGroup'
    // },
    {
      title: t('customer:customer.customer_detail.information_tab.fields.salesRep'),
      dataIndex: 'salesRep',
      key: 'salesRep'
    }
  ]


  const convertMarket = (market: string) => {
    if (market === 'domestic') {
      return 'Nội địa'
    } else if (market === 'international') {
      return 'Xuất khẩu'
    } else {
      return 'Cả hai'
    }
  }

  return (
    <div>
      <Card
        title={
          <Space>
            <AppstoreOutlined style={{ color: '#1890ff' }} />
            <BaseText>{t('customer:customer.customer_detail.information_tab.title')}</BaseText>
          </Space>
        }
        style={{ marginBottom: '16px' }}
        size='small'>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Descriptions column={1}>
              <Descriptions.Item
                label={
                  <Space>
                    <BarcodeOutlined />
                    <span>
                      {t('customer:customer.customer_detail.information_tab.fields.customerCode')}
                    </span>
                  </Space>
                }>
                {code || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <BarcodeOutlined />
                    <span>
                      {t('customer:customer.customer_detail.information_tab.fields.sapCode')}
                    </span>
                  </Space>
                }>
                {sapCode || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <UserOutlined />
                    <span>
                      {t('customer:customer.customer_detail.information_tab.fields.shortName')}
                    </span>
                  </Space>
                }>
                {shortName || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <UserOutlined />
                    <span>
                      {t('customer:customer.customer_detail.information_tab.fields.fullName')}
                    </span>
                  </Space>
                }>
                {name || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <BarcodeOutlined />
                    <span>
                      {t('customer:customer.customer_detail.information_tab.fields.taxCode')}
                    </span>
                  </Space>
                }>
                {taxCode || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <TagOutlined />
                    <span>
                      {t('customer:customer.customer_detail.information_tab.fields.customerSource')}
                    </span>
                  </Space>
                }>
                <Tag color='purple'>{source || 'N/A'}</Tag>
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <CalendarOutlined />
                    <span>
                      {t('customer:customer.customer_detail.information_tab.fields.visitDate')}
                    </span>
                  </Space>
                }>
                {formatDateCustom(visitDate, 'DD/MM/YYYY') || 'N/A'}
              </Descriptions.Item>
            </Descriptions>
          </Col>
          <Col xs={24} sm={12}>
            <Descriptions column={1}>
              <Descriptions.Item
                label={
                  <Space>
                    <GlobalOutlined />
                    <span>
                      {t('customer:customer.customer_detail.information_tab.fields.market')}
                    </span>
                  </Space>
                }>
                <Tag color='blue'>{convertMarket(market) || 'N/A'}</Tag>
              </Descriptions.Item>
              {/* <Descriptions.Item
                label={
                  <Space>
                    <TagOutlined />
                    <span>
                      {t(
                        'customer:customer.customer_detail.information_tab.fields.customerType'
                      )}
                    </span>
                  </Space>
                }>
                <Tag color='green'>{customerType || 'N/A'}</Tag>
              </Descriptions.Item> */}
              <Descriptions.Item
                label={
                  <Space>
                    <TeamOutlined />
                    <span>
                      {t('customer:customer.customer_detail.information_tab.fields.customerGroup')}
                    </span>
                  </Space>
                }>
                {customerGroup || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <ShopOutlined />
                    <span>
                      {t('customer:customer.customer_detail.information_tab.fields.industry')}
                    </span>
                  </Space>
                }>
                {industry || 'N/A'}
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <StarOutlined />
                    <span>
                      {t(
                        'customer:customer.customer_detail.information_tab.fields.customerRanking'
                      )}
                    </span>
                  </Space>
                }>
                <Tag color='gold'>{ranking || 'N/A'}</Tag>
              </Descriptions.Item>
              <Descriptions.Item
                label={
                  <Space>
                    <FileTextOutlined />
                    <span>
                      {t('customer:customer.customer_detail.information_tab.fields.note')}
                    </span>
                  </Space>
                }>
                {note || 'N/A'}
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>

      <Card
        title={
          <Space>
            <BankOutlined style={{ color: '#722ed1' }} />
            <BaseText>
              {t('customer:customer.customer_detail.information_tab.sections.company_list')}
            </BaseText>
          </Space>
        }
        size='small'>
        <Table columns={columns} dataSource={tableData} pagination={false} size='small' />
      </Card>
    </div>
  )
}
