import { FC, useState } from 'react'
import {
  Card,
  Col,
  DatePicker,
  Form,
  Input,
  Row,
  Button,
  Table,
  Tag,
  Select
} from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { SearchOutlined } from '@ant-design/icons'
import BaseTable from '~/components/BaseTable'
import { useTranslation } from 'react-i18next'

interface IAverageRevenue {
  id: string
  time: string
  revenue: number
  averageRevenue: number
}

const fakeData: IAverageRevenue[] = [
  {
    id: '1',
    time: '2024-03',
    revenue: 150000000,
    averageRevenue: 5000000
  },
  {
    id: '2',
    time: '2024-02',
    revenue: 120000000,
    averageRevenue: 4000000
  },
  {
    id: '3',
    time: '2024-01',
    revenue: 100000000,
    averageRevenue: 3500000
  }
]

const AverageRevenue: FC = () => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const columns: ColumnsType<IAverageRevenue> = [
    {
      title: t('customer:customer.customer_detail.revenue_tab.columns.time'),
      dataIndex: 'time',
      key: 'time',
      width: 150
    },
    {
      title: t('customer:customer.customer_detail.revenue_tab.columns.revenue'),
      dataIndex: 'revenue',
      key: 'revenue',
      width: 200,
      render: (value: number) => value.toLocaleString('vi-VN') + ' VNĐ'
    },
    {
      title: t(
        'customer:customer.customer_detail.revenue_tab.columns.averageRevenue'
      ),
      dataIndex: 'averageRevenue',
      key: 'averageRevenue',
      width: 200,
      render: (value: number) => value.toLocaleString('vi-VN') + ' VNĐ'
    }
  ]

  const handleSearch = async () => {
    try {
      setLoading(true)
      const values = await form.validateFields()
      console.log('Search values:', values)
      // TODO: Implement API call
    } catch (error) {
      console.error('Validation failed:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <Card className='mb-4'>
        <Form
          form={form}
          layout='vertical'
          initialValues={{
            company: undefined,
            dateRange: undefined,
            revenueFrom: undefined,
            revenueTo: undefined
          }}>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item
                name='company'
                label={t(
                  'customer:customer.customer_detail.revenue_tab.form.company'
                )}
                rules={[{ required: true, message: 'Vui lòng chọn công ty' }]}>
                <Select
                  placeholder={t(
                    'customer:customer.customer_detail.revenue_tab.placeholders.selectCompany'
                  )}
                  options={[
                    { label: 'Công ty A', value: 'A' },
                    { label: 'Công ty B', value: 'B' }
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name='dateRange'
                label={t(
                  'customer:customer.customer_detail.revenue_tab.form.time'
                )}
                rules={[
                  { required: true, message: 'Vui lòng chọn thời gian' }
                ]}>
                <DatePicker.RangePicker
                  style={{ width: '100%' }}
                  picker='month'
                  format='MM/YYYY'
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name='revenueFrom'
                label={t(
                  'customer:customer.customer_detail.revenue_tab.form.revenueFrom'
                )}>
                <Input
                  type='number'
                  placeholder={t(
                    'customer:customer.customer_detail.revenue_tab.placeholders.enterRevenueFrom'
                  )}
                  suffix='VNĐ'
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name='revenueTo'
                label={t(
                  'customer:customer.customer_detail.revenue_tab.form.revenueTo'
                )}>
                <Input
                  type='number'
                  placeholder={t(
                    'customer:customer.customer_detail.revenue_tab.placeholders.enterRevenueTo'
                  )}
                  suffix='VNĐ'
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24} className='text-right'>
              <Button
                type='primary'
                icon={<SearchOutlined />}
                onClick={handleSearch}
                loading={loading}>
                {t(
                  'customer:customer.customer_detail.revenue_tab.form.viewRevenue'
                )}
              </Button>
            </Col>
          </Row>
        </Form>
      </Card>

      <Card>
        <BaseTable
          columns={columns}
          data={fakeData}
          isLoading={loading}
          total={fakeData.length}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  )
}

export default AverageRevenue
