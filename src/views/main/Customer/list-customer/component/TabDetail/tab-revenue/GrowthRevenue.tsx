import { FC, useState } from 'react'
import { Card, Col, DatePicker, Form, Row, Button, Select } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { SearchOutlined } from '@ant-design/icons'
import BaseTable from '~/components/BaseTable'
import { useTranslation } from 'react-i18next'

interface IGrowthRevenue {
  id: string
  year: string
  revenue: number
  growthRate: number
}

const fakeData: IGrowthRevenue[] = [
  {
    id: '1',
    year: '2024',
    revenue: 2000000000,
    growthRate: 15.5
  },
  {
    id: '2',
    year: '2023',
    revenue: 1732000000,
    growthRate: 12.3
  },
  {
    id: '3',
    year: '2022',
    revenue: 1542000000,
    growthRate: 8.7
  }
]

const GrowthRevenue: FC = () => {
  const { t } = useTranslation()
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const columns: ColumnsType<IGrowthRevenue> = [
    {
      title: t('customer:customer.customer_detail.revenue_tab.columns.time'),
      dataIndex: 'year',
      key: 'year',
      width: 100
    },
    {
      title: t('customer:customer.customer_detail.revenue_tab.columns.revenue'),
      dataIndex: 'revenue',
      key: 'revenue',
      width: 200,
      render: (value: number) => value.toLocaleString('vi-VN') + ' VNĐ'
    },
    {
      title: t('customer:customer.customer_detail.revenue_tab.columns.growth'),
      dataIndex: 'growthRate',
      key: 'growthRate',
      width: 150,
      render: (value: number) => (
        <span style={{ color: value >= 0 ? '#52c41a' : '#f5222d' }}>
          {value >= 0 ? '+' : ''}
          {value}%
        </span>
      )
    }
  ]

  const handleSearch = async () => {
    try {
      setLoading(true)
      const values = await form.validateFields()
      console.log('Search values:', values)
      // TODO: Implement API call
    } catch (error) {
      console.error('Validation failed:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div>
      <Card className='mb-4'>
        <Form
          form={form}
          layout='vertical'
          initialValues={{
            company: undefined,
            period: undefined,
            dateRange: undefined
          }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name='company'
                label={t(
                  'customer:customer.customer_detail.revenue_tab.form.company'
                )}
                rules={[{ required: true, message: 'Vui lòng chọn công ty' }]}>
                <Select
                  placeholder={t(
                    'customer:customer.customer_detail.revenue_tab.placeholders.selectCompany'
                  )}
                  options={[
                    { label: 'Công ty A', value: 'A' },
                    { label: 'Công ty B', value: 'B' }
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name='period'
                label={t(
                  'customer:customer.customer_detail.revenue_tab.form.time'
                )}
                rules={[{ required: true, message: 'Vui lòng chọn kỳ' }]}>
                <Select
                  placeholder={t(
                    'customer:customer.customer_detail.revenue_tab.placeholders.selectPeriod'
                  )}
                  options={[
                    { label: 'Quý 1', value: 'Q1' },
                    { label: 'Quý 2', value: 'Q2' },
                    { label: 'Quý 3', value: 'Q3' },
                    { label: 'Quý 4', value: 'Q4' },
                    { label: 'Cả năm', value: 'YEAR' }
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name='dateRange'
                label={t(
                  'customer:customer.customer_detail.revenue_tab.form.time'
                )}>
                <DatePicker.RangePicker
                  style={{ width: '100%' }}
                  format='DD/MM/YYYY'
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24} className='text-right'>
              <Button
                type='primary'
                icon={<SearchOutlined />}
                onClick={handleSearch}
                loading={loading}>
                {t(
                  'customer:customer.customer_detail.revenue_tab.form.viewRevenue'
                )}
              </Button>
            </Col>
          </Row>
        </Form>
      </Card>

      <Card>
        <BaseTable
          columns={columns}
          data={fakeData}
          isLoading={loading}
          total={fakeData.length}
          scroll={{ x: 800 }}
        />
      </Card>
    </div>
  )
}

export default GrowthRevenue
