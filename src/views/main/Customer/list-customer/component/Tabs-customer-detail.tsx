import {
  InfoCircleOutlined,
  TeamOutlined,
  PhoneOutlined,
  EnvironmentOutlined,
  FileOutlined,
  Bar<PERSON><PERSON>Outlined,
  HistoryOutlined,
  WarningOutlined
} from '@ant-design/icons'
import { Space, Tabs } from 'antd'
import { ICustomer } from '~/dto/customer.dto'
import {
  DocumentTab,
  HistoryTab,
  ManagerTab,
  ComplaintTab,
  ContactTab,
  AddressTab,
  RevenueTab,
  InformationTab
} from './TabDetail'
import { useTranslation } from 'react-i18next'

export const TabsCustomerDetail = ({ data }: { data: ICustomer }) => {
  const { t } = useTranslation()
  return (
    <div>
      <Tabs
        defaultActiveKey='1'
        items={[
          {
            key: '1',
            label: (
              <Space>
                <InfoCircleOutlined />
                <span>
                  {t('customer:customer.customer_detail.information_tab.title')}
                </span>
              </Space>
            ),
            children: <InformationTab data={data} />
          },
          {
            key: '2',
            label: (
              <Space>
                <PhoneOutlined />
                <span>
                  {t('customer:customer.customer_detail.contact_tab.title')}
                </span>
              </Space>
            ),
            children: <ContactTab />
          },
          {
            key: '3',
            label: (
              <Space>
                <WarningOutlined />
                <span>
                  {t('customer:customer.customer_detail.complaint_tab.title')}
                </span>
              </Space>
            ),
            children: <ComplaintTab />
          },
          {
            key: '4',
            label: (
              <Space>
                <EnvironmentOutlined />
                <span>
                  {t('customer:customer.customer_detail.address_tab.title')}
                </span>
              </Space>
            ),
            children: <AddressTab />
          },
          {
            key: '5',
            label: (
              <Space>
                <TeamOutlined />
                <span>
                  {t('customer:customer.customer_detail.manager_tab.title')}
                </span>
              </Space>
            ),
            children: <ManagerTab />
          },
          {
            key: '6',
            label: (
              <Space>
                <FileOutlined />
                <span>
                  {t('customer:customer.customer_detail.document_tab.title')}
                </span>
              </Space>
            ),
            children: <DocumentTab />
          },
          {
            key: '7',
            label: (
              <Space>
                <BarChartOutlined />
                <span>
                  {t('customer:customer.customer_detail.revenue_tab.title')}
                </span>
              </Space>
            ),
            children: <RevenueTab />
          },
          {
            key: '8',
            label: (
              <Space>
                <HistoryOutlined />
                <span>
                  {t('customer:customer.customer_detail.history_tab.title')}
                </span>
              </Space>
            ),
            children: <HistoryTab />
          }
        ]}
      />
    </div>
  )
}
