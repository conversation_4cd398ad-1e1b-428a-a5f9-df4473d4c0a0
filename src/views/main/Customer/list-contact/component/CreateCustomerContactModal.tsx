import { SaveOutlined } from '@ant-design/icons'
import { Row, Col, Form, Input, Button, Switch, Select } from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { ICustomerContact } from '~/dto/customer-contact.dto'
import { toastService } from '~/services'
import { useTranslation } from 'react-i18next'

const { Option } = Select

interface CreateCustomerContactModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateCustomerContactModal = ({
  open,
  onClose,
  onSuccess
}: CreateCustomerContactModalProps) => {
  const [form] = useForm()
  const { t } = useTranslation()
  const handleSave = async (values: Partial<ICustomerContact>) => {
    if (!values) return

    try {
      // TODO: Implement create contact API call
      console.log('Create contact with values:', values)
      onClose()
      onSuccess?.()
      form.resetFields()
    } catch (error) {
      toastService.error('T<PERSON>o liên hệ thất bại')
    }
  }

  const modalContent = (
    <Form form={form} layout='vertical' onFinish={handleSave}>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer_contact.columns.contactCode')}
            name='contactCode'
            rules={[
              {
                required: true,
                message: t('customer:customer_contact.validation.required')
              }
            ]}>
            <Input placeholder={t('customer:customer_contact.placeholders.enterContactCode')} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer_contact.columns.name')}
            name='name'
            rules={[
              {
                required: true,
                message: t('customer:customer_contact.validation.required')
              }
            ]}>
            <Input placeholder={t('customer:customer_contact.placeholders.enterName')} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer_contact.columns.customerName')}
            name='customerName'
            rules={[
              {
                required: true,
                message: t('customer:customer_contact.validation.required')
              }
            ]}>
            <Input placeholder={t('customer:customer_contact.placeholders.enterCustomerName')} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer_contact.columns.phone')}
            name='phone'
            rules={[
              {
                required: true,
                message: t('customer:customer_contact.validation.required')
              }
            ]}>
            <Input placeholder={t('customer:customer_contact.placeholders.enterPhone')} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer_contact.columns.email')}
            name='email'
            rules={[
              {
                required: true,
                message: t('customer:customer_contact.validation.required')
              },
              {
                type: 'email',
                message: t('customer:customer_contact.validation.emailInvalid')
              }
            ]}>
            <Input placeholder={t('customer:customer_contact.placeholders.enterEmail')} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer_contact.columns.branch')}
            name='branch'
            rules={[
              {
                required: true,
                message: t('customer:customer_contact.validation.required')
              }
            ]}>
            <Input placeholder={t('customer:customer_contact.placeholders.enterBranch')} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          {/* Chọn chực vụ */}
          <Form.Item
            label={t('customer:customer_contact.columns.position')}
            name='position'
            rules={[
              {
                required: true,
                message: t('customer:customer_contact.validation.required')
              }
            ]}>
            <Select placeholder={t('customer:customer_contact.placeholders.selectPosition')}>
              <Option value='1'>Giám đốc</Option>
              <Option value='2'>Phó giám đốc</Option>
              <Option value='3'>Trưởng phòng</Option>
              <Option value='4'>Phó phòng</Option>
              <Option value='5'>Nhân viên</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer_contact.columns.status')}
            name='status'
            rules={[
              {
                required: true,
                message: t('customer:customer_contact.validation.required')
              }
            ]}>
            <Input placeholder={t('customer:customer_contact.placeholders.enterStatus')} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label={t('customer:customer_contact.columns.isSpecialCare')}
            name='isSpecialCare'
            valuePropName='checked'>
            <Switch />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={24}>
          <Form.Item label={t('customer:customer_contact.columns.note')} name='note'>
            <Input.TextArea
              rows={2}
              placeholder={t('customer:customer_contact.placeholders.enterNote')}
            />
          </Form.Item>
        </Col>
      </Row>

      <div
        style={{
          textAlign: 'right',
          marginTop: 24,
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16
        }}>
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          {t('customer:customer_contact.cancel')}
        </Button>
        <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
          {t('customer:customer_contact.create')}
        </Button>
      </div>
    </Form>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title={t('customer:customer_contact.create')}
      description={t('customer:customer_contact.description')}
      childrenBody={modalContent}
    />
  )
}

export default CreateCustomerContactModal
