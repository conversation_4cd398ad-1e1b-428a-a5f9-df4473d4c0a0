import { useState, FC } from 'react'
import { Col, Row, Checkbox, Tag, Collapse, Form, Input, Button, Space, Select } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import {
  ICustomerContact,
  ICustomerContactFilter,
  ICustomerContactResponse
} from '~/dto/customer-contact.dto'
import { useListCustomerContact } from '~/hooks/customer-contact/useListCustomerContact'
import { BaseButton, BaseCheckbox } from '~/components'
import DetailButton from './component/DetailButton'
import EditButton from './component/EditButton'
import { CheckOutlined, CloseOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { useTranslation } from 'react-i18next'

type IProps = {}

const ListContactView: FC<IProps> = () => {
  const { t } = useTranslation()
  const [filter, setFilter] = useState<ICustomerContactFilter>({
    pageIndex: 1,
    pageSize: 10
  })

  const { data, isLoading, total } = useListCustomerContact()

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const columns: ColumnsType<ICustomerContactResponse> = [
    {
      title: t('customer:customer_contact.columns.stt') || 'STT',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: t('customer:customer_contact.columns.contactCode') || 'Mã liên hệ',
      dataIndex: 'contactCode',
      key: 'contactCode',
      width: 120,
      align: 'center'
    },
    {
      title: t('customer:customer_contact.columns.name') || 'Tên liên hệ',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: 'center'
    },
    {
      title: 'Chức vụ',
      dataIndex: 'position',
      key: 'position',
      width: 200,
      align: 'center'
    },
    {
      title: t('customer:customer_contact.columns.customerName') || 'Tên khách hàng',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer_contact.columns.phone') || 'Số điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
      align: 'center'
    },
    {
      title: t('customer:customer_contact.columns.email') || 'Email',
      dataIndex: 'email',
      key: 'email',
      width: 200,
      align: 'center'
    },
    {
      title: 'Ghi chú',
      dataIndex: 'note',
      key: 'note',
      width: 200,
      align: 'left'
    },
    {
      title: t('customer:customer_contact.columns.branch') || 'Chi nhánh',
      dataIndex: 'branch',
      key: 'branch',
      width: 200,
      align: 'center'
    },
    {
      title: t('customer:customer_contact.columns.createdBy') || 'Người tạo',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 120,
      align: 'center'
    },
    {
      title: t('customer:customer_contact.columns.responsible') || 'Người phụ trách',
      dataIndex: 'responsible',
      key: 'responsible',
      width: 120,
      align: 'center'
    },
    {
      title: t('customer:customer_contact.columns.createdAt') || 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 120,
      align: 'center',
      render: (value: string) => (value ? new Date(value).toLocaleDateString('vi-VN') : '-')
    },
    {
      title: t('customer:customer_contact.columns.isSpecialCare') || 'Chăm sóc đặc biệt',
      dataIndex: 'isSpecialCare',
      key: 'isSpecialCare',
      width: 120,
      align: 'center',
      render: (value: boolean) => <BaseCheckbox checked={true} sizeCheckbox='large' />
    },
    {
      title: t('customer:customer_contact.columns.status') || 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: (value: string) => {
        return <Tag color={value === 'Hoạt động' ? 'green' : 'red'}>{value}</Tag>
      }
    },
    {
      title: t('customer:customer_contact.columns.action'),
      key: 'action',
      width: 100,
      fixed: 'right',
      align: 'center',
      render: (value: any, record: any, index: number) => {
        return (
          <>
            <EditButton data={value} />
            <DetailButton data={record} />
          </>
        )
      }
    }
  ]

  return (
    <BaseView>
      <Collapse>
        <Collapse.Panel header='Tìm kiếm' key='0'>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item name='id' label='Mã liên hệ'>
                <Input placeholder='Nhập mã liên hệ' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='name' label='Tên khách hàng'>
                <Input placeholder='Nhập tên khách hàng' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='name' label='Trạng thái'>
                <Select placeholder='Chọn trạng thái'>
                  <Select.Option value='Hoạt động'>Hoạt động</Select.Option>
                  <Select.Option value='Không hoạt động'>Không hoạt động</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col>
              <Form.Item name='name' label='Chăm sóc đặc biệt'>
                <Checkbox />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24} style={{ textAlign: 'center' }}>
              <Space>
                <Button type='primary' icon={<SearchOutlined />}>
                  Tìm kiếm
                </Button>
                <Button type='default' icon={<ReloadOutlined />}>
                  Làm mới
                </Button>
              </Space>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns as any}
            data={data?.data || []}
            total={total || 0}
            isLoading={isLoading}
            onPageChange={handlePageChange}
            scroll={{ x: 2500 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}

export default ListContactView
