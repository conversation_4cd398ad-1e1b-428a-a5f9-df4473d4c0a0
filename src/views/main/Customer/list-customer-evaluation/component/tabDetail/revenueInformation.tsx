import { FC } from 'react'
import { Card, Col, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { ActivityChart } from '~/components'
import { mockActivitySegments } from '~/common/constants'

interface IAddress {
  id: string
  addressType: string
  market: string
  area: string
  address: string
  areaSize: number
  note: string
  isMain: boolean
  createdBy: string
  createdAt: string
}

type IProps = {}

export const RevenueInformation: FC<IProps> = (props: IProps) => {
  const style = {
    padding: '16px',
    boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
    borderRadius: '12px',
    border: '1px solid rgba(0,0,0,0.05)',
    backgroundColor: 'white'
  }
  return (
    <BaseView>
      <Col xs={24} lg={24} style={style}>
        <Card title='Thống kê doanh số theo nhóm' bordered={false}>
          <ActivityChart
            title='Thống kê doanh số nhóm theo tháng'
            data={mockActivitySegments}
          />
        </Card>
      </Col>
    </BaseView>
  )
}
