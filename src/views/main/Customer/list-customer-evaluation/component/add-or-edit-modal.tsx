import { SaveOutlined } from '@ant-design/icons'
import { Form, Input, Row, Col, Radio, Button, TreeSelect } from 'antd'
import form from 'antd/es/form'
import TextArea from 'antd/es/input/TextArea'

interface TreeNode {
  key: string
  title: string
  value: string
  children?: TreeNode[]
}

const treeData: TreeNode[] = [
  {
    key: '1',
    title: '1. Quy mô hoạt động',
    value: '1',
    children: [
      {
        key: '1-1',
        title: '<PERSON>à<PERSON> nghề',
        value: '1-1',
        children: [
          { key: '1-1-1', title: '<PERSON><PERSON>n xuất', value: '1-1-1' },
          { key: '1-1-2', title: 'Thương mại', value: '1-1-2' },
          { key: '1-1-3', title: 'Dịch vụ', value: '1-1-3' }
        ]
      },
      {
        key: '1-2',
        title: 'Th<PERSON> trường của khách hàng',
        value: '1-2',
        children: [
          { key: '1-2-1', title: '<PERSON><PERSON><PERSON> địa', value: '1-2-1' },
          { key: '1-2-2', title: '<PERSON><PERSON><PERSON> khẩu', value: '1-2-2' },
          { key: '1-2-3', title: '<PERSON><PERSON> hai', value: '1-2-3' }
        ]
      },
      {
        key: '1-3',
        title: 'Số lượng nhân sự công ty',
        value: '1-3',
        children: [
          { key: '1-3-1', title: 'Dưới 50', value: '1-3-1' },
          { key: '1-3-2', title: '51-150', value: '1-3-2' },
          { key: '1-3-3', title: '151-300', value: '1-3-3' },
          { key: '1-3-4', title: '301-500', value: '1-3-4' },
          { key: '1-3-5', title: '501-1000', value: '1-3-5' },
          { key: '1-3-6', title: 'Trên 1000', value: '1-3-6' }
        ]
      },
      {
        key: '1-4',
        title: 'Chủ đầu tư nước ngoài',
        value: '1-4',
        children: [
          { key: '1-4-1', title: 'Có', value: '1-4-1' },
          { key: '1-4-2', title: 'Không', value: '1-4-2' }
        ]
      }
    ]
  },
  {
    key: '2',
    title: '2. Vị trí địa lý',
    value: '2',
    children: [
      {
        key: '2-1',
        title: 'Châu lục',
        value: '2-1',
        children: [
          { key: '2-1-1', title: 'Châu Á', value: '2-1-1' },
          { key: '2-1-2', title: 'Châu Âu', value: '2-1-2' },
          { key: '2-1-3', title: 'Châu Phi', value: '2-1-3' },
          { key: '2-1-4', title: 'Châu Mỹ', value: '2-1-4' },
          { key: '2-1-5', title: 'Châu Đại Dương', value: '2-1-5' },
          { key: '2-1-6', title: 'Châu Nam cực', value: '2-1-6' },
          { key: '2-1-7', title: 'Châu Nam Mỹ', value: '2-1-7' }
        ]
      },
      {
        key: '2-2',
        title: 'Khu vực',
        value: '2-2',
        children: [
          { key: '2-2-1', title: 'Bắc', value: '2-2-1' },
          { key: '2-2-2', title: 'Trung', value: '2-2-2' },
          { key: '2-2-3', title: 'Nam', value: '2-2-3' },
          { key: '2-2-4', title: 'Quận/Huyện', value: '2-2-4' },
          { key: '2-2-5', title: 'Phường/Xã', value: '2-2-5' }
        ]
      }
    ]
  },
  {
    key: '3',
    title: '3. Tài chính khách hàng',
    value: '3',
    children: [
      {
        key: '3-1',
        title: 'Doanh số',
        value: '3-1',
        children: [{ key: '3-1-1', title: 'Trên 100 triệu', value: '3-1-1' }]
      },
      { key: '3-2', title: 'Công nợ', value: '3-2' },
      { key: '3-3', title: 'Công nợ trung bình quá hạn', value: '3-3' },
      {
        key: '3-4',
        title: 'Doanh thu trung bình trên 1 đơn hàng',
        value: '3-4'
      },
      {
        key: '3-5',
        title: 'Doanh thu luỹ kế so với cùng kỳ năm trước',
        value: '3-5'
      },
      {
        key: '3-6',
        title: 'Tăng trưởng Doanh số theo năm',
        value: '3-6',
        children: [{ key: '3-6-1', title: 'Trên 20%', value: '3-6-1' }]
      }
    ]
  },
  {
    key: '4',
    title: '4. Thói quen tiêu dùng',
    value: '4',
    children: [
      { key: '4-1', title: 'Thị hiếu khách', value: '4-1' },
      { key: '4-2', title: 'Tuổi khách hàng', value: '4-2' },
      {
        key: '4-3',
        title: 'Biết đến sản phẩm của công ty qua kênh nào?',
        value: '4-3'
      },
      { key: '4-4', title: 'Số đơn đặt hàng trong 1 năm', value: '4-4' }
    ]
  },
  {
    key: '5',
    title: '5. Thông tin cá nhân',
    value: '5',
    children: [
      {
        key: '5-1',
        title: 'Độ tuổi',
        value: '5-1',
        children: [
          { key: '5-1-1', title: '18 đến 25', value: '5-1-1' },
          { key: '5-1-2', title: '26 đến 40', value: '5-1-2' },
          { key: '5-1-3', title: '41 đến 50', value: '5-1-3' },
          { key: '5-1-4', title: 'Trên 50', value: '5-1-4' }
        ]
      },
      {
        key: '5-2',
        title: 'Giới tính',
        value: '5-2',
        children: [
          { key: '5-2-1', title: 'Nam', value: '5-2-1' },
          { key: '5-2-2', title: 'Nữ', value: '5-2-2' },
          { key: '5-2-3', title: 'Khác', value: '5-2-3' }
        ]
      },
      {
        key: '5-3',
        title: 'Quốc tịch',
        value: '5-3',
        children: [
          { key: '5-3-1', title: 'Việt Nam', value: '5-3-1' },
          { key: '5-3-2', title: 'Quốc tế', value: '5-3-2' }
        ]
      }
    ]
  }
]

export const modalContent = (isEdit: boolean, form, handleSave, closeModal) => (
  <Form form={form} layout='vertical' onFinish={handleSave}>
    {/* Quick Search */}
    <Form.Item
      label='Tìm kiếm nhanh'
      name='quickSearch'
      rules={[{ required: true, message: 'Vui lòng nhập từ khóa tìm kiếm' }]}>
      <Input placeholder='Nhập từ khóa tìm kiếm' />
    </Form.Item>

    {/* Basic Information */}
    <Row gutter={16}>
      <Col span={12}>
        <Form.Item
          label='Tên nhóm'
          name='name'
          rules={[{ required: true, message: 'Vui lòng nhập tên phân khúc' }]}>
          <Input placeholder='Nhập tên nhóm' />
        </Form.Item>
      </Col>
      <Col span={12}>
        <Form.Item
          label='Trạng thái'
          name='status'
          rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}>
          <Radio.Group>
            <Radio value='active'>Sử dụng</Radio>
            <Radio value='inactive'>Ngưng sử dụng</Radio>
          </Radio.Group>
        </Form.Item>
      </Col>
    </Row>

    {/* Action Buttons */}

    {/* Description */}
    <Form.Item
      label='Mô tả'
      name='description'
      rules={[{ required: true, message: 'Vui lòng nhập mô tả' }]}>
      <TextArea rows={3} placeholder='Nhập mô tả chi tiết về phân khúc khách hàng' />
    </Form.Item>

    {/* Evaluation Criteria */}
    <Form.Item
      label={
        <span>
          Tiêu chí đánh giá
          <span style={{ color: '#ff4d4f', marginLeft: 4 }}>*</span>
        </span>
      }
      name='criteria'
      rules={[{ required: true, message: 'Vui lòng chọn tiêu chí đánh giá' }]}
      extra='Chọn các tiêu chí phù hợp để đánh giá và phân loại khách hàng'>
      <TreeSelect
        treeData={treeData}
        placeholder='Chọn tiêu chí đánh giá (có thể chọn nhiều)'
        treeCheckable
        showCheckedStrategy={TreeSelect.SHOW_PARENT}
        multiple
        treeDefaultExpandAll
        allowClear
        showSearch
        filterTreeNode={(inputValue, treeNode) => {
          const title = treeNode.title as string
          return title?.toLowerCase().includes(inputValue.toLowerCase()) || false
        }}
        maxTagCount={5}
        maxTagPlaceholder={(omittedValues) => `+${omittedValues.length} tiêu chí khác`}
        style={{ width: '100%' }}
        dropdownStyle={{
          overflow: 'auto'
        }}
        listHeight={400}
        treeNodeFilterProp='title'
        placement='bottomLeft'
        size='large'
        notFoundContent='Không tìm thấy tiêu chí phù hợp'
        loading={false}
        virtual={false}
        treeIcon={false}
        switcherIcon={null}
      />
    </Form.Item>
    <div
      style={{
        borderTop: '1px solid #f0f0f0',
        display: 'flex',
        justifyContent: 'center'
      }}>
      <Button onClick={closeModal} style={{ marginRight: 8 }}>
        Hủy
      </Button>

      {!isEdit ? (
        <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
          Lưu
        </Button>
      ) : (
        <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
          Cập nhật
        </Button>
      )}
    </div>
  </Form>
)
