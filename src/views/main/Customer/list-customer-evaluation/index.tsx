import { useState, FC } from 'react'
import { Button, Col, Modal, Row, Select, Space, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useTranslation } from 'react-i18next'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'

import { BaseButton } from '~/components'
import { DeleteOutlined, SearchOutlined } from '@ant-design/icons'
import { useCustomerEvaluation } from '~/hooks/customer-evaluation/useCustomerEvaluation'
import { ICustomerEvaluation, ITreeNode } from '~/dto/customer-evaluation.dto'
import { DetailButton, EditButton } from './component'
import { Form } from 'antd'
import { modalContent } from './component/add-or-edit-modal'
import BaseModal from '~/components/BaseModal'

interface IFilterCustomerEvaluation {
  pageIndex: number
  pageSize: number
}
const { Option } = Select
type IProps = {}

const ListCustomerEvaluationView: FC<IProps> = () => {
  const { t } = useTranslation()
  const [filter, setFilter] = useState<IFilterCustomerEvaluation>({
    pageIndex: 1,
    pageSize: 10
  })
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [form] = Form.useForm()

  const { data, isLoading, total } = useCustomerEvaluation()

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const handleDelete = (record: ICustomerEvaluation) => {
    console.log(record)
  }

  const columns: ColumnsType<ICustomerEvaluation> = [
    {
      title: t('customer:customer_evaluation.columns.stt') || 'STT',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: t('customer:customer_evaluation.columns.code') || 'Mã nhóm',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer_evaluation.columns.name') || 'Tên nhóm',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center'
    },
    {
      title: t('customer:customer_evaluation.columns.customerCount') || 'Số lượng khách hàng',
      dataIndex: 'customerCount',
      key: 'customerCount',
      width: 180,
      align: 'center'
    },
    {
      title: t('customer:customer_evaluation.columns.status') || 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: (value: boolean) => {
        return value ? <Tag color='green'>Hoạt động</Tag> : <Tag color='red'>Không hoạt động</Tag>
      }
    },
    {
      title: t('customer:customer_evaluation.columns.createdBy') || 'Người tạo',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 150,
      align: 'center'
    },
    {
      title: t('customer:customer_evaluation.columns.createdAt') || 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      render: (value: string) => (value ? new Date(value).toLocaleDateString('vi-VN') : '-')
    },
    {
      title: t('customer:customer_evaluation.columns.action') || 'Tác vụ',
      key: 'action',
      width: 150,
      fixed: 'right',
      align: 'center',
      render: (_, record) => {
        return (
          <>
            <EditButton data={record} />
            <DetailButton data={record} />
            <BaseButton
              danger
              type='primary'
              shape='circle'
              icon={<DeleteOutlined />}
              tooltip='Delete'
              onClick={() => handleDelete(record)}
            />
          </>
        )
      }
    }
  ]

  const extractSelectedValues = (criteria: ITreeNode[]): string[] => {
    const selectedValues: string[] = []

    const extractValues = (nodes: ITreeNode[]) => {
      nodes.forEach((node) => {
        if (node.children && node.children.length > 0) {
          extractValues(node.children)
        } else {
          selectedValues.push(node.value)
        }
      })
    }

    extractValues(criteria)
    return selectedValues
  }

  const handleManufacturingClick = () => {
    const data = {
      evaluationCriteria: [
        {
          key: '1',
          title: '1. Quy mô hoạt động',
          value: '1',
          children: [
            {
              key: '1-1',
              title: 'Ngành nghề',
              value: '1-1',
              children: [{ key: '1-1-1', title: 'Sản xuất', value: '1-1-1' }]
            },
            {
              key: '1-2',
              title: 'Thị trường của khách hàng',
              value: '1-2',
              children: [{ key: '1-2-3', title: 'Cả hai', value: '1-2-3' }]
            },
            {
              key: '1-3',
              title: 'Số lượng nhân sự công ty',
              value: '1-3',
              children: [
                { key: '1-3-3', title: '151-300', value: '1-3-3' },
                { key: '1-3-4', title: '301-500', value: '1-3-4' },
                { key: '1-3-5', title: '501-1000', value: '1-3-5' },
                { key: '1-3-6', title: 'Trên 1000', value: '1-3-6' }
              ]
            }
          ]
        },
        {
          key: '2',
          title: '2. Vị trí địa lý',
          value: '2',
          children: [
            {
              key: '2-1',
              title: 'Châu lục',
              value: '2-1',
              children: [{ key: '2-1-1', title: 'Châu Á', value: '2-1-1' }]
            },
            {
              key: '2-2',
              title: 'Khu vực',
              value: '2-2',
              children: [
                { key: '2-2-1', title: 'Bắc', value: '2-2-1' },
                { key: '2-2-2', title: 'Trung', value: '2-2-2' },
                { key: '2-2-3', title: 'Nam', value: '2-2-3' }
              ]
            }
          ]
        },
        {
          key: '3',
          title: '3. Tài chính khách hàng',
          value: '3',
          children: [
            {
              key: '3-1',
              title: 'Doanh số',
              value: '3-1',
              children: [{ key: '3-1-1', title: 'Trên 100 triệu', value: '3-1-1' }]
            },
            {
              key: '3-6',
              title: 'Tăng trưởng Doanh số theo năm',
              value: '3-6',
              children: [{ key: '3-6-1', title: 'Trên 20%', value: '3-6-1' }]
            }
          ]
        }
      ]
    }
    const selectedCriteriaValues = extractSelectedValues(data.evaluationCriteria)
    form.setFieldsValue({
      name: 'Nhóm khách hàng VIP ngành nghề Sản xuất',
      status: 'active',
      criteria: selectedCriteriaValues
    })
    setIsModalVisible(true)
  }

  const handleCommercialClick = () => {
    const data = {
      evaluationCriteria: [
        {
          key: '1',
          title: '1. Quy mô hoạt động',
          value: '1',
          children: [
            {
              key: '1-1',
              title: 'Ngành nghề',
              value: '1-1',
              children: [{ key: '1-1-2', title: 'Thương mại', value: '1-1-2' }]
            },
            {
              key: '1-2',
              title: 'Thị trường của khách hàng',
              value: '1-2',
              children: [{ key: '1-2-3', title: 'Cả hai', value: '1-2-3' }]
            },
            {
              key: '1-3',
              title: 'Số lượng nhân sự công ty',
              value: '1-3',
              children: [
                { key: '1-3-3', title: '151-300', value: '1-3-3' },
                { key: '1-3-4', title: '301-500', value: '1-3-4' },
                { key: '1-3-5', title: '501-1000', value: '1-3-5' },
                { key: '1-3-6', title: 'Trên 1000', value: '1-3-6' }
              ]
            }
          ]
        },
        {
          key: '2',
          title: '2. Vị trí địa lý',
          value: '2',
          children: [
            {
              key: '2-1',
              title: 'Châu lục',
              value: '2-1',
              children: [{ key: '2-1-1', title: 'Châu Á', value: '2-1-1' }]
            },
            {
              key: '2-2',
              title: 'Khu vực',
              value: '2-2',
              children: [
                { key: '2-2-1', title: 'Bắc', value: '2-2-1' },
                { key: '2-2-2', title: 'Trung', value: '2-2-2' },
                { key: '2-2-3', title: 'Nam', value: '2-2-3' }
              ]
            }
          ]
        },
        {
          key: '3',
          title: '3. Tài chính khách hàng',
          value: '3',
          children: [
            {
              key: '3-1',
              title: 'Doanh số',
              value: '3-1',
              children: [{ key: '3-1-1', title: 'Trên 100 triệu', value: '3-1-1' }]
            },
            {
              key: '3-6',
              title: 'Tăng trưởng Doanh số theo năm',
              value: '3-6',
              children: [{ key: '3-6-1', title: 'Trên 20%', value: '3-6-1' }]
            }
          ]
        }
      ]
    }
    const selectedCriteriaValues = extractSelectedValues(data.evaluationCriteria)
    form.setFieldsValue({
      name: 'Nhóm khách hàng VIP ngành nghề Sản xuất',
      status: 'active',
      criteria: selectedCriteriaValues
    })
    setIsModalVisible(true)
  }

  const handleServiceClick = () => {
    const data = {
      evaluationCriteria: [
        {
          key: '1',
          title: '1. Quy mô hoạt động',
          value: '1',
          children: [
            {
              key: '1-1',
              title: 'Ngành nghề',
              value: '1-1',
              children: [{ key: '1-1-3', title: 'Dịch vụ', value: '1-1-3' }]
            },
            {
              key: '1-2',
              title: 'Thị trường của khách hàng',
              value: '1-2',
              children: [{ key: '1-2-3', title: 'Cả hai', value: '1-2-3' }]
            },
            {
              key: '1-3',
              title: 'Số lượng nhân sự công ty',
              value: '1-3',
              children: [
                { key: '1-3-3', title: '151-300', value: '1-3-3' },
                { key: '1-3-4', title: '301-500', value: '1-3-4' },
                { key: '1-3-5', title: '501-1000', value: '1-3-5' },
                { key: '1-3-6', title: 'Trên 1000', value: '1-3-6' }
              ]
            }
          ]
        },
        {
          key: '2',
          title: '2. Vị trí địa lý',
          value: '2',
          children: [
            {
              key: '2-1',
              title: 'Châu lục',
              value: '2-1',
              children: [{ key: '2-1-1', title: 'Châu Á', value: '2-1-1' }]
            },
            {
              key: '2-2',
              title: 'Khu vực',
              value: '2-2',
              children: [
                { key: '2-2-1', title: 'Bắc', value: '2-2-1' },
                { key: '2-2-2', title: 'Trung', value: '2-2-2' },
                { key: '2-2-3', title: 'Nam', value: '2-2-3' }
              ]
            }
          ]
        },
        {
          key: '3',
          title: '3. Tài chính khách hàng',
          value: '3',
          children: [
            {
              key: '3-1',
              title: 'Doanh số',
              value: '3-1',
              children: [{ key: '3-1-1', title: 'Trên 100 triệu', value: '3-1-1' }]
            },
            {
              key: '3-6',
              title: 'Tăng trưởng Doanh số theo năm',
              value: '3-6',
              children: [{ key: '3-6-1', title: 'Trên 20%', value: '3-6-1' }]
            }
          ]
        }
      ]
    }
    const selectedCriteriaValues = extractSelectedValues(data.evaluationCriteria)
    form.setFieldsValue({
      name: 'Nhóm khách hàng VIP ngành nghề Sản xuất',
      status: 'active',
      criteria: selectedCriteriaValues
    })
    setIsModalVisible(true)
  }

  const onClose = () => {
    form.resetFields()
    setIsModalVisible(false)
  }

  const handleModalSave = () => {
    setIsModalVisible(false)
  }

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          {/*
            Bộ lọc filter
           */}
          {/* Lọc theo nhóm Nhóm */}
          <Space style={{ marginBottom: 16, display: 'flex' }}>
            <Select
              style={{ width: 200, marginRight: 16 }}
              onChange={(value) => console.log(value)}
              placeholder='Chọn nhóm'>
              <Option value=''>--Chọn nhóm--</Option>
              <Option value='PK001'>Nhóm 1</Option>
              <Option value='PK002'>Nhóm 2</Option>
            </Select>
            <Select
              style={{ width: 200 }}
              onChange={(value) => console.log(value)}
              placeholder='Chọn trạng thái'>
              <Option value=''>--Chọn trạng thái--</Option>
              <Option value='active'>Hoạt động</Option>
              <Option value='inactive'>Không hoạt động</Option>
            </Select>
            <Button type='primary' icon={<SearchOutlined />}>
              Tìm kiếm
            </Button>
          </Space>
          <Space style={{ width: '100%', marginBottom: 16, display: 'flex' }}>
            <Button
              style={{
                width: '100%',
                height: 'auto',
                wordWrap: 'break-word', // Allow text to break into a new line
                whiteSpace: 'normal',
                fontWeight: '600',
                marginRight: 8
              }}
              type='primary'
              onClick={handleManufacturingClick}>
              Nhóm khách hàng VIP ngành nghề Sản xuất
            </Button>

            <Button
              style={{
                width: '100%',
                height: 'auto',
                wordWrap: 'break-word', // Allow text to break into a new line
                whiteSpace: 'normal',
                fontWeight: '600',
                marginRight: 8
              }}
              type='primary'
              onClick={handleCommercialClick}>
              Nhóm khách hàng VIP ngành nghề Thương mại
            </Button>

            <Button
              style={{
                width: '100%',
                height: 'auto',
                wordWrap: 'break-word', // Allow text to break into a new line
                whiteSpace: 'normal',
                fontWeight: '600',
                marginRight: 8
              }}
              type='primary'
              onClick={handleServiceClick}>
              Nhóm khách hàng VIP ngành nghề Dịch vụ
            </Button>
          </Space>

          <BaseTable
            columns={columns}
            data={data?.data || []}
            total={total || 0}
            isLoading={isLoading}
            onPageChange={handlePageChange}
            scroll={{ x: 1000 }}
          />
        </Col>
      </Row>

      <BaseModal
        title='Quản lý phân khúc khách hàng'
        open={isModalVisible}
        onClose={onClose}
        childrenBody={modalContent(false, form, handleModalSave, onClose)}
      />
    </BaseView>
  )
}

export default ListCustomerEvaluationView
