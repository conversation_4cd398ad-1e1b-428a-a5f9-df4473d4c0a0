import { But<PERSON>, Card, Col, Form, Input, Row, Space, Table } from 'antd'
import BaseModal from '~/components/BaseModal'
import { Select } from 'antd'
import { useState } from 'react'
import { co } from '@fullcalendar/core/internal-common'
import { render } from '@testing-library/react'
import { ReloadOutlined, SaveOutlined } from '@ant-design/icons'
const { Option } = Select

export const InvoiceEditComponent = ({ open, onClose, onSuccess }) => {
  const [form] = Form.useForm()
  const [useContract, setUseContract] = useState(false)
  const [listProducts, setListProducts] = useState([])
  const [totalPrice, setTotalPrice] = useState(0)

  const handleSwitchChange = (checked: boolean) => {
    setUseContract(checked)
  }

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      console.log('Submitted values:', values)
      // Gọi API hoặc xử lý logic tạo nhóm ở đây
      onSuccess?.()
      onClose()
      form.resetFields()
    } catch (err) {
      console.log('Validation failed:', err)
    }
  }

  const handleAddProductToInvoice = (value, option) => {
    const id = value
    const name = option?.children
    const quantity = Math.random() * 100
    const price = Math.random() * 100000
    const newProduct = {
      id,
      name,
      quantity: quantity.toFixed(),
      price: price.toFixed(),
      total: (quantity * price).toFixed()
    }
    setListProducts([...listProducts, newProduct])
    setTotalPrice(totalPrice + Number(newProduct.total))
  }

  const handleClearProducts = () => {
    setListProducts([])
    setTotalPrice(0)
  }

  const columnsProduct = [
    {
      title: 'STT',
      dataIndex: 'stt',
      width: 60,
      render: (_, __, index) => index + 1
    },
    {
      title: 'Tên sản phẩm / dịch vụ',
      dataIndex: 'name',
      width: 200
    },
    {
      title: 'Số lượng',
      dataIndex: 'quantity',
      width: 80
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      width: 120,
      render: (value: number) => value.toLocaleString() + ' vn₫'
    },
    {
      title: 'Thành tiền',
      dataIndex: 'total',
      width: 120,
      render: (value: number) => value.toLocaleString() + ' vn₫'
    }
  ]

  const contentModal = (
    <Card>
      <Form layout='vertical' form={form} onFinish={handleSubmit}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='Tên khách hàng' name='useContract' required>
              <Input placeholder='Tên khách hàng'></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='Địa chỉ'>
              <Input placeholder='Địa chỉ'></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='Mã số thuế'>
              <Input placeholder='Mã số thuế'></Input>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label='Thuế xuất'>
              <Input placeholder='Thuế xuất'></Input>
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label='Chọn danh sách sản phẩm / dịch vụ' required>
              <Select
                placeholder='Chọn danh sách sản phẩm / dịch vụ'
                style={{ width: '100%' }}
                onChange={handleAddProductToInvoice}>
                <Option value=''>--Chọn danh sách sản phẩm / dịch vụ--</Option>
                <Option value='DV-00001'>Dịch vụ A</Option>
                <Option value='DV-00002'>Dịch vụ B</Option>
                <Option value='SP-00001'>Sản phẩm B</Option>
                <Option value='SP-00002'>Sản phẩm A</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Space>
              {' '}
              <Form.Item label=' '>
                <Button type='default' icon={<ReloadOutlined />} onClick={handleClearProducts}>
                  Làm mới
                </Button>
              </Form.Item>
              <Form.Item label=' '>
                <Button
                  type='primary'
                  htmlType='submit'
                  icon={<SaveOutlined />}
                  onClick={handleSubmit}>
                  Lưu
                </Button>
              </Form.Item>
            </Space>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={24}>
            <Table
              columns={columnsProduct}
              dataSource={listProducts}
              pagination={false}
              footer={() => <strong>Tổng tiền: {totalPrice.toLocaleString()} vn₫</strong>}
            />
          </Col>
        </Row>
      </Form>
    </Card>
  )

  return (
    <BaseModal
      open={open}
      title='Chỉnh sửa hóa đơn'
      description='Cập nhật thông tin hóa đơn'
      onClose={onClose}
      childrenBody={contentModal}></BaseModal>
  )
}
