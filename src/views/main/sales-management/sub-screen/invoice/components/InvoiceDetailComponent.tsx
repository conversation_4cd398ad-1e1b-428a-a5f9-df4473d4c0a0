import React from 'react'
import {
  Card,
  Descriptions,
  Table,
  Typography,
  Divider,
  Checkbox,
  Input,
  DatePicker,
  Button,
  Space
} from 'antd'
import { EyeOutlined } from '@ant-design/icons'
import { exportInvoicePDF } from '~/common/utils/invoice-pdf'


const { Text } = Typography

const serviceData = [
  {
    key: '1',
    name: 'Phí vận chuyển',
    quantity: 1,
    price: 20000,
    vat: '10%',
    checked: true
  },
  {
    key: '2',
    name: '<PERSON><PERSON> bốc xếp',
    quantity: 1,
    price: 2000,
    vat: '10%',
    checked: true
  },
  {
    key: '3',
    name: '<PERSON><PERSON> phí cầu đường',
    quantity: 1,
    price: 1000,
    vat: '10%',
    checked: true
  },
  {
    key: '4',
    name: '<PERSON><PERSON> lưu kho (nếu có)',
    quantity: 1,
    price: 3000,
    vat: '10%',
    checked: false
  }
]

export const InvoiceDetailComponent = () => {
  const columns = [
    {
      title: 'Tên dịch vụ',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <Checkbox checked={record.checked} disabled>
          {text}
        </Checkbox>
      )
    },
    {
      title: 'SL',
      dataIndex: 'quantity',
      key: 'quantity'
    },
    {
      title: 'Đơn giá',
      dataIndex: 'price',
      key: 'price',
      render: (value: number) => value.toLocaleString()
    },
    {
      title: 'VAT',
      dataIndex: 'vat',
      key: 'vat'
    }
  ]

  return (
    <Card title='Chi tiết hóa đơn' bordered={false}>
      <Space style={{ display: 'flex', marginBottom: 16 }}>
        <Button
          type='default'
          icon={<EyeOutlined />}
          onClick={exportInvoicePDF}>
          Xem trước hóa đơn PDF
        </Button>
        <Button type='primary' icon={<EyeOutlined />}>
          Xuất Hóa đơn
        </Button>
      </Space>
      <Descriptions column={1} bordered>
        <Descriptions.Item label='Khách hàng'>
          Công ty TNHH ABC Logistics
        </Descriptions.Item>
        <Descriptions.Item label='Mã số thuế'>0312345678</Descriptions.Item>
        {/*Mã hợp đồng */}
        <Descriptions.Item label='Mã hợp đồng'>
          <strong>HD-2025-057</strong>
        </Descriptions.Item>
        <Descriptions.Item label='Tuyến vận chuyển'>
          Hồ Chí Minh → Hải Phòng
        </Descriptions.Item>
        <Descriptions.Item label='Thời gian vận chuyển'>
          12/06/2025 – 15/06/2025
        </Descriptions.Item>
        <Descriptions.Item label='Điều khoản thanh toán'>
          Thanh toán 1 lần
        </Descriptions.Item>
        <Descriptions.Item label='Ngày xuất hóa đơn'>
          17/06/2025
        </Descriptions.Item>
      </Descriptions>

      <Divider />

      <Text strong>Danh sách dịch vụ</Text>
      <Table
        columns={columns}
        dataSource={serviceData}
        pagination={false}
        style={{ marginTop: 16 }}
      />
      <Divider />
      {/* Tổng giá trị hóa đơn */}
      <Text strong>Tổng giá trị hóa đơn: 25.000.000 ₫</Text>
      <Divider />
      <Text strong>Ghi chú hóa đơn:</Text>
      <Input
        placeholder='Nhập ghi chú'
        style={{ marginTop: 8, marginBottom: 16 }}
      />
    </Card>
  )
}
