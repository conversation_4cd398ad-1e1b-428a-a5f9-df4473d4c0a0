import React, { useState } from 'react'
import {
  Table,
  Tag,
  Typography,
  Card,
  Button,
  Space,
  Select,
  Input,
  Col,
  Row,
  Collapse,
  Form,
  Tooltip,
  Popconfirm
} from 'antd'
import BaseTable from '~/components/BaseTable'
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PrinterOutlined,
  ReloadOutlined,
  SearchOutlined,
  SendOutlined
} from '@ant-design/icons'
import { toastService } from '~/services'
import { ColumnsType } from 'antd/es/table'
import { useNavigate } from 'react-router-dom'
import { exportInvoicePDF } from '~/common/utils/invoice-pdf'
import { InvoiceEditComponent } from './components/InvoiceEditComponent'
import { useTranslation } from 'react-i18next'

const { Title } = Typography
const { Option } = Select

interface Invoice {
  key: string
  invoiceCode: string
  contractCode: string
  customerName: string
  issueDate: string
  dueDate: string
  amount: number
  taxRate: number
  totalAmount: number
  status: 'unpaid' | 'paid' | 'overdue'
  paymentDate: string
  contractType: string
  misaStatus: 'issued' | 'pending' // đã phát hành / chờ phát hành
  paymentStatus: string
  createdBy: string
}

const moreMockInvoices: Invoice[] = [
  {
    key: '4',
    invoiceCode: 'INV-2025-004',
    contractCode: 'HD-2025-003',
    customerName: 'Công ty DEF',
    issueDate: '2025-06-05',
    dueDate: '2025-06-20',
    amount: 9500000,
    taxRate: 10,
    totalAmount: 10450000,
    status: 'unpaid',
    paymentDate: '',
    contractType: 'Vận chuyển',
    misaStatus: 'pending',
    paymentStatus: 'unpaid',
    createdBy: 'Lê Văn C'
  },
  {
    key: '5',
    invoiceCode: 'INV-2025-005',
    contractCode: 'HD-2025-004',
    customerName: 'Công ty GHI',
    issueDate: '2025-06-01',
    dueDate: '2025-06-10',
    amount: 5000000,
    taxRate: 5,
    totalAmount: 5250000,
    status: 'paid',
    paymentDate: '2025-06-09',
    contractType: 'Dịch vụ',
    misaStatus: 'issued',
    paymentStatus: 'paid',
    createdBy: 'Nguyễn Văn D'
  },
  {
    key: '6',
    invoiceCode: 'INV-2025-006',
    contractCode: 'HD-2025-005',
    customerName: 'Công ty JKL',
    issueDate: '2025-05-20',
    dueDate: '2025-06-05',
    amount: 7000000,
    taxRate: 8,
    totalAmount: 7560000,
    status: 'overdue',
    paymentDate: '',
    contractType: 'Bảo trì',
    misaStatus: 'pending',
    paymentStatus: 'unpaid',
    createdBy: 'Phạm Thị E'
  },
  {
    key: '7',
    invoiceCode: 'INV-2025-007',
    contractCode: 'HD-2025-006',
    customerName: 'Công ty MNO',
    issueDate: '2025-06-10',
    dueDate: '2025-06-25',
    amount: 15000000,
    taxRate: 10,
    totalAmount: 16500000,
    status: 'unpaid',
    paymentDate: '',
    contractType: 'Dịch vụ',
    misaStatus: 'issued',
    paymentStatus: 'unpaid',
    createdBy: 'Lê Văn F'
  },
  {
    key: '8',
    invoiceCode: 'INV-2025-008',
    contractCode: 'HD-2025-007',
    customerName: 'Công ty PQR',
    issueDate: '2025-06-08',
    dueDate: '2025-06-23',
    amount: 3000000,
    taxRate: 10,
    totalAmount: 3300000,
    status: 'paid',
    paymentDate: '2025-06-12',
    contractType: 'Vận chuyển',
    misaStatus: 'issued',
    paymentStatus: 'paid',
    createdBy: 'Nguyễn Văn G'
  },
  {
    key: '9',
    invoiceCode: 'INV-2025-009',
    contractCode: 'HD-2025-008',
    customerName: 'Công ty STU',
    issueDate: '2025-05-25',
    dueDate: '2025-06-10',
    amount: 4000000,
    taxRate: 5,
    totalAmount: 4200000,
    status: 'overdue',
    paymentDate: '',
    contractType: 'Dịch vụ',
    misaStatus: 'pending',
    paymentStatus: 'unpaid',
    createdBy: 'Trần Thị H'
  },
  {
    key: '10',
    invoiceCode: 'INV-2025-010',
    contractCode: 'HD-2025-009',
    customerName: 'Công ty VWX',
    issueDate: '2025-06-12',
    dueDate: '2025-06-27',
    amount: 8500000,
    taxRate: 8,
    totalAmount: 9180000,
    status: 'unpaid',
    paymentDate: '',
    contractType: 'Dịch vụ',
    misaStatus: 'pending',
    paymentStatus: 'unpaid',
    createdBy: 'Phạm Văn I'
  },
  {
    key: '11',
    invoiceCode: 'INV-2025-011',
    contractCode: 'HD-2025-010',
    customerName: 'Công ty YZA',
    issueDate: '2025-06-02',
    dueDate: '2025-06-16',
    amount: 6200000,
    taxRate: 10,
    totalAmount: 6820000,
    status: 'paid',
    paymentDate: '2025-06-14',
    contractType: 'Bảo trì',
    misaStatus: 'issued',
    paymentStatus: 'paid',
    createdBy: 'Nguyễn Thị K'
  },
  {
    key: '12',
    invoiceCode: 'INV-2025-012',
    contractCode: 'HD-2025-011',
    customerName: 'Công ty BCD',
    issueDate: '2025-06-14',
    dueDate: '2025-06-28',
    amount: 9400000,
    taxRate: 10,
    totalAmount: 10340000,
    status: 'unpaid',
    paymentDate: '',
    contractType: 'Dịch vụ',
    misaStatus: 'pending',
    paymentStatus: 'unpaid',
    createdBy: 'Trần Văn L'
  }
]

const getStatusTag = (status: Invoice['status'] | Invoice['misaStatus']) => {
  switch (status) {
    case 'unpaid':
      return <Tag color='orange'>Chưa thanh toán</Tag>
    case 'paid':
      return <Tag color='green'>Đã thanh toán</Tag>
    case 'overdue':
      return <Tag color='red'>Quá hạn</Tag>
    case 'pending':
      return <Tag color='blue'>Chờ xử lý</Tag>
    case 'issued':
      return <Tag color='green'>Đã phát hành</Tag>
    default:
      return <Tag color='default'>Chờ xử lý</Tag>
  }
}

export const InvoicesComponent = () => {
  const navigate = useNavigate()
  const [filters, setFilters] = useState({
    contractName: '',
    contractType: ''
  })
  const { t } = useTranslation()
  const [isModalOpen, setIsModalOpen] = useState(false)

  const columns = [
    {
      title: 'Mã hóa đơn',
      dataIndex: 'invoiceCode',
      key: 'invoiceCode'
    },
    {
      title: 'Mã hợp đồng',
      dataIndex: 'contractCode',
      key: 'contractCode'
    },
    {
      title: 'Loại hình dịch vụ',
      dataIndex: 'contractType',
      key: 'contractType'
    },
    // Người tạo
    {
      title: 'Người tạo',
      dataIndex: 'createdBy',
      key: 'createdBy'
    },
    {
      title: 'Khách hàng',
      dataIndex: 'customerName',
      key: 'customerName'
    },
    {
      title: 'Ngày phát hành',
      dataIndex: 'issueDate',
      key: 'issueDate'
    },
    {
      title: 'Hạn thanh toán',
      dataIndex: 'dueDate',
      key: 'dueDate'
    },
    {
      title: 'Thành tiền (chưa thuế)',
      dataIndex: 'amount',
      key: 'amount',
      render: (value: number) => value.toLocaleString() + ' ₫',
      align: 'right' as const
    },
    {
      title: 'Thuế (%)',
      dataIndex: 'taxRate',
      key: 'taxRate',
      align: 'right' as const
    },
    {
      title: 'Tổng tiền (gồm thuế)',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (value: number) => value.toLocaleString() + ' ₫',
      align: 'right' as const
    },
    // Trạng thái thanh toán
    {
      title: 'Trạng thái thanh toán',
      dataIndex: 'paymentStatus',
      key: 'paymentStatus',
      render: (status: Invoice['status']) => getStatusTag(status)
    },
    // Ngày thanh toán
    {
      title: 'Ngày thanh toán',
      dataIndex: 'paymentDate',
      key: 'paymentDate'
    },
    // Trạng thái hóa đơn MISA
    {
      title: 'Trạng thái hóa đơn MISA',
      dataIndex: 'misaStatus',
      key: 'misaStatus',
      render: (status: Invoice['status']) => getStatusTag(status)
    }
  ]

  const handleDelete = (record: any) => {
    toastService.success('Xóa thành công')
  }

  const handleFilterChange = (field: string, value: string) => {
    setFilters({ ...filters, [field]: value })
  }

  const handleViewDetail = (id: string) => {
    navigate(`detail?id=${id}`)
  }

  const handleEdit = (id: string) => {
    setIsModalOpen(true)
  }

  const handleExportInvoice = (record: any) => {
    exportInvoicePDF()
  }

  const handleSendInvoice = () => {
    toastService.success('Gửi hóa đơn thành công')
  }

  const hiddenButtonSend = (record: any) => {
    switch (record.misaStatus) {
      case 'issued':
        return false
      default:
        return true
    }
  }

  const hiddenButtonEdit = (record: any) => {
    switch (record.misaStatus) {
      case 'issued':
        return false
      default:
        return true
    }
  }

  const columnsAction: ColumnsType<Invoice> = [
    {
      title: 'Tác vụ',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (value: any, record: any, index: number) => {
        return (
          <>
            <Space>
              <Button icon={<EyeOutlined />} onClick={() => handleViewDetail(record.key)}>
              </Button>
              {hiddenButtonSend(record) && (
                <Popconfirm
                  title='Xác nhận phát hành hóa đơn?'
                  onConfirm={() => handleSendInvoice()}>
                  <Tooltip title='Phát hành hóa đơn'>
                    <Button icon={<SendOutlined />} type='primary'></Button>
                  </Tooltip>
                </Popconfirm>
              )}
              {
                hiddenButtonEdit(record) && (
                <Button icon={<EditOutlined />} onClick={() => handleEdit(record.key)}>
                </Button>
              )
              }

              <Button
                type='primary'
                icon={<PrinterOutlined />}
                onClick={() => handleExportInvoice(record)}></Button>
              <Button
                danger
                type='primary'
                icon={<DeleteOutlined />}
                onClick={() => handleDelete(record)}
              />
            </Space>
          </>
        )
      }
    }
  ]

  return (
    <Card title='Danh sách hợp đồng đã xuất hóa đơn'>
      <Collapse style={{ marginBottom: 16 }}>
        <Collapse.Panel header='Tìm kiếm' key='0'>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label='Mã hợp đồng'>
                <Input
                  placeholder='Tìm theo hợp đồng'
                  onChange={(e) => handleFilterChange('contractName', e.target.value)}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Loại hợp đồng'>
                <Select
                  placeholder='Loại hợp đồng'
                  allowClear
                  onChange={(value) => handleFilterChange('contractType', value)}
                  style={{ width: '100%' }}>
                  {['Dịch vụ', 'Bảo trì', 'Phân phối'].map((type) => (
                    <Option key={type} value={type}>
                      {type}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Người tạo'>
                <Select
                  placeholder='Người tạo'
                  allowClear
                  onChange={(value) => handleFilterChange('createdBy', value)}
                  style={{ width: '100%' }}>
                  {['Nguyễn Văn A', 'Trần Thị B'].map((type) => (
                    <Option key={type} value={type}>
                      {type}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Mã hóa đơn'>
                <Input
                  placeholder='Tìm theo hóa đơn'
                  onChange={(e) => handleFilterChange('invoiceCode', e.target.value)}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Tên khách hàng'>
                <Input
                  placeholder='Tìm theo khách hàng'
                  onChange={(e) => handleFilterChange('customerName', e.target.value)}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Loại hình dịch vụ'>
                <Select
                  placeholder='Loại hình dịch vụ'
                  allowClear
                  onChange={(value) => handleFilterChange('serviceType', value)}
                  style={{ width: '100%' }}>
                  {['Dịch vụ', 'Bảo trì', 'Phân phối'].map((type) => (
                    <Option key={type} value={type}>
                      {type}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Trạng thái hóa đơn'>
                <Select
                  placeholder='Trạng thái hóa đơn'
                  allowClear
                  onChange={(value) => handleFilterChange('status', value)}
                  style={{ width: '100%' }}>
                  {['Chưa thanh toán', 'Đã thanh toán', 'Quá hạn'].map((type) => (
                    <Option key={type} value={type}>
                      {type}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Trạng thái thanh toán'>
                <Select
                  placeholder='Trạng thái thanh toán'
                  allowClear
                  onChange={(value) => handleFilterChange('paymentStatus', value)}
                  style={{ width: '100%' }}>
                  {['Chưa thanh toán', 'Đã thanh toán', 'Quá hạn'].map((type) => (
                    <Option key={type} value={type}>
                      {type}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24} style={{ textAlign: 'center' }}>
              <Space>
                <Button type='default' icon={<ReloadOutlined />}>
                  Làm mới
                </Button>
                <Button type='primary' icon={<SearchOutlined />}>
                  Tìm kiếm
                </Button>
              </Space>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>

      <InvoiceEditComponent
        open={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={() => {}}
      />

      <BaseTable
        columns={[...columns, ...columnsAction]}
        data={moreMockInvoices}
        total={moreMockInvoices.length}
        isLoading={false}
        pagination={{ pageSize: 10 }}
        scroll={{ x: 'max-content' }}
      />
    </Card>
  )
}
