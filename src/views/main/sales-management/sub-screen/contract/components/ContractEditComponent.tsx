import { Card, Col, Form, Row, Select } from 'antd'
import { useState } from 'react'
import BaseModal from '~/components/BaseModal'

const { Option } = Select

export const ContractEditComponent = () => {
  const [form] = Form.useForm()
  const [useQuotation, setUseQuotation] = useState(false)

  const handleSwitchChange = (checked: boolean) => {
    setUseQuotation(checked)
  }

  const contentModal = (
    <Card>
      <Row gutter={16} justify='start' style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Form.Item label='Chọn báo giá' name='useQuotation'>
            <Select placeholder='Chọn báo giá' onChange={handleSwitchChange}>
              <Option value=''>--Chọn báo giá--</Option>
              <Option value='BG-2025-056'>BG-2025-056</Option>
              <Option value='BG-2025-057'>BG-2025-057</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label='Chọn mẫu hợp đồng'>
            <Select placeholder='Chọn mẫu hợp đồng'>
              <Option value=''>--Chọn mẫu hợp đồng--</Option>
              <Option value='HD-2025-056'>HD-2025-056</Option>
              <Option value='HD-2025-057'>HD-2025-057</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
    </Card>
  )
  return (
    <BaseModal
      open={true}
      title='Chỉnh sửa hợp đồng'
      description='Cập nhật thông tin hợp đồng'
      onClose={() => {}}
      childrenBody={contentModal}></BaseModal>
  )
}
