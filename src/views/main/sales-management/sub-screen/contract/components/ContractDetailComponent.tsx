import React, { useRef } from 'react'
import { Card, Typography, Descriptions, Table, Divider, Row, Col, Button } from 'antd'
import html2pdf from 'html2pdf.js'
import { EyeOutlined } from '@ant-design/icons'
import sign_a from '../../../../../../assets/sign/sign_a.png'
import sign_b from '../../../../../../assets/sign/sign_b.png'

const { Title, Text } = Typography

const serviceDetails = [
  {
    key: '1',
    stt: 1,
    service: 'Vận chuyển nội địa',
    description: 'TP.HCM → Hà Nội (3 ngày)',
    unit: 'Chuyến',
    quantity: 1,
    unitPrice: 15000000,
    total: 15000000
  },
  {
    key: '2',
    stt: 2,
    service: 'Phí bốc xếp container',
    description: '2 container 40ft',
    unit: 'Lần',
    quantity: 1,
    unitPrice: 2000000,
    total: 2000000
  },
  {
    key: '3',
    stt: 3,
    service: '<PERSON><PERSON><PERSON> kho (nếu có)',
    description: 'Sau 3 ngày đầu miễn phí',
    unit: 'Ngày',
    quantity: 2,
    unitPrice: 50000,
    total: 100000
  }
]

const columns = [
  {
    title: 'STT',
    dataIndex: 'stt'
  },
  {
    title: 'Dịch vụ',
    dataIndex: 'service'
  },
  {
    title: 'Mô tả tuyến đường / điều kiện',
    dataIndex: 'description'
  },
  {
    title: 'Đơn vị',
    dataIndex: 'unit'
  },
  {
    title: 'Số lượng',
    dataIndex: 'quantity'
  },
  {
    title: 'Đơn giá (VNĐ)',
    dataIndex: 'unitPrice',
    render: (value: number) => value.toLocaleString()
  },
  {
    title: 'Thành tiền',
    dataIndex: 'total',
    render: (value: number) => value.toLocaleString()
  }
]

export const ContractDetailComponent = () => {
  const contractRef = useRef<HTMLDivElement>(null)

  //HopDong-HD-2025-057.pdf'
  const handleExportPdf = () => {
    if (contractRef.current) {
      const fileName = 'HopDong-HD-2025-057.pdf'

      html2pdf()
        .set({
          margin: 0.5,
          filename: fileName,
          image: { type: 'jpeg', quality: 0.98 },
          html2canvas: { scale: 2 },
          jsPDF: { unit: 'in', format: 'a4', orientation: 'portrait' }
        })
        .from(contractRef.current)
        .outputPdf('blob') // trả về Blob thay vì bloburl
        .then((pdfBlob: Blob) => {
          const blobUrl = URL.createObjectURL(pdfBlob)

          // Mở tab mới xem
          window.open(blobUrl, '_blank')

          // Tạo link để tải với đúng tên file
          const link = document.createElement('a')
          link.href = blobUrl
          link.download = fileName
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        })
    }
  }

  return (
    <div>
      <Button
        type='primary'
        icon={<EyeOutlined />}
        onClick={() => handleExportPdf()}
        style={{ marginBottom: 16, marginLeft: 8 }}>
        Xem PDF
      </Button>
      <div ref={contractRef}>
        <Card bordered>
          <Title level={3} style={{ textAlign: 'center' }}>
            HỢP ĐỒNG DỊCH VỤ LOGISTICS
          </Title>
          <Descriptions column={1} size='middle'>
            <Descriptions.Item label={<strong>Số hợp đồng</strong>}>
              <Text strong>HD-2025-057</Text>
            </Descriptions.Item>
            <Descriptions.Item label={<strong>Ngày ký</strong>}>
              <Text strong>17/06/2025</Text>
            </Descriptions.Item>
          </Descriptions>

          <Divider orientation='center'>Căn cứ ký kết</Divider>
          <Typography.Paragraph>
            Căn cứ vào <strong>Bộ luật Dân sự 2015</strong> và các văn bản pháp luật liên quan.
            <br />
            Căn cứ vào <strong>báo giá số BG-2025-057</strong> ngày 17/06/2025.
            <br />
            Căn cứ vào <strong>nhu cầu và thỏa thuận giữa hai bên</strong>.
          </Typography.Paragraph>

          <Divider orientation='center'>Thông tin các bên</Divider>
          <Row gutter={32}>
            <Col span={12}>
              <Title level={5}>BÊN A (Bên thuê dịch vụ)</Title>
              <Descriptions column={1}>
                <Descriptions.Item label='Tên công ty'>CÔNG TY TNHH XYZ VIỆT NAM</Descriptions.Item>
                <Descriptions.Item label='Địa chỉ'>
                  456 Nguyễn Văn Cừ, Quận 5, TP.HCM
                </Descriptions.Item>
                <Descriptions.Item label='Đại diện'>
                  Ông Nguyễn Văn A – Trưởng phòng Logistics
                </Descriptions.Item>
                <Descriptions.Item label='MST'>0312xxxxxx</Descriptions.Item>
                <Descriptions.Item label='Điện thoại'>0903 xxx xxx</Descriptions.Item>
                <Descriptions.Item label='Email'><EMAIL></Descriptions.Item>
              </Descriptions>
            </Col>
            <Col span={12}>
              <Title level={5}>BÊN B (Bên cung cấp dịch vụ)</Title>
              <Descriptions column={1}>
                <Descriptions.Item label='Tên công ty'>
                  CÔNG TY TNHH DỊCH VỤ VẬN TẢI ABC LOGISTICS
                </Descriptions.Item>
                <Descriptions.Item label='Địa chỉ'>
                  123 Nguyễn Văn Linh, Quận 7, TP.HCM
                </Descriptions.Item>
                <Descriptions.Item label='Đại diện'>
                  Bà Nguyễn Thị B – Trưởng phòng Kinh doanh
                </Descriptions.Item>
                <Descriptions.Item label='MST'>0312yyyyyy</Descriptions.Item>
                <Descriptions.Item label='Điện thoại'>0933 xxx xxx</Descriptions.Item>
                <Descriptions.Item label='Email'><EMAIL></Descriptions.Item>
              </Descriptions>
            </Col>
          </Row>

          <Divider orientation='left'>Điều 1. Nội dung dịch vụ</Divider>
          <Table
            dataSource={serviceDetails}
            columns={columns}
            pagination={false}
            bordered
            summary={() => (
              <>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={6} align='right'>
                    <strong>Tổng tiền chưa thuế:</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={1}>17.100.000 VND</Table.Summary.Cell>
                </Table.Summary.Row>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={6} align='right'>
                    <strong>Thuế GTGT (10%):</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={1}>1.710.000 VND</Table.Summary.Cell>
                </Table.Summary.Row>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={6} align='right'>
                    <strong>Tổng thanh toán:</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={1}>
                    <Text strong>18.810.000 VND</Text>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              </>
            )}
          />

          <Divider orientation='left'>Điều 2. Thời gian và phương thức thực hiện</Divider>
          <Typography.Paragraph>
            <strong>Thời gian vận chuyển:</strong> từ 12/06/2025 đến 15/06/2025
            <br />
            Bên B chịu trách nhiệm đảm bảo đúng tiến độ, an toàn hàng hóa.
          </Typography.Paragraph>

          <Divider orientation='left'>Điều 3. Thanh toán</Divider>
          <Typography.Paragraph>
            Bên A thanh toán cho Bên B trong vòng <strong>07 ngày</strong> kể từ khi hoàn tất dịch
            vụ.
            <br />
            <strong>Hình thức:</strong> Chuyển khoản vào tài khoản của Bên B:
            <br />
            Ngân hàng: <strong>Vietcombank – CN TP.HCM</strong>
            <br />
            STK: <strong>**********</strong>
            <br />
            Chủ TK: <strong>CÔNG TY TNHH ABC LOGISTICS</strong>
          </Typography.Paragraph>

          <Divider orientation='left'>Điều 4. Trách nhiệm và cam kết</Divider>
          <Typography.Paragraph>
            <strong>Bên A cam kết:</strong>
            <br />
            - Cung cấp đầy đủ thông tin hàng hóa, giấy tờ liên quan.
            <br />
            <br />
            <strong>Bên B cam kết:</strong>
            <br />
            - Đảm bảo an toàn, đúng thời gian.
            <br />- Xuất hóa đơn GTGT sau khi hoàn tất dịch vụ.
          </Typography.Paragraph>

          <Divider orientation='left'>Điều 5. Điều khoản chung</Divider>
          <Typography.Paragraph>
            Hợp đồng có hiệu lực kể từ ngày ký và kết thúc khi hai bên hoàn thành nghĩa vụ.
            <br />
            Hợp đồng được lập thành 02 bản, mỗi bên giữ 01 bản có giá trị pháp lý như nhau.
          </Typography.Paragraph>

          <Divider orientation='center'>ĐẠI DIỆN HAI BÊN</Divider>
          <Row>
            <Col span={12}>
              <Text strong>BÊN A</Text>
              <br />
              <img src={sign_a} alt='sign' style={{ width: 200, height: 100 }} />
              <br />
              (Chữ ký điện tử, chữ ký số)
              <br />
              <Text strong>Nguyễn Văn A</Text>
              <br />
              Chức vụ: Trưởng phòng Logistics
            </Col>
            <Col span={12}>
              <Text strong>BÊN B</Text>
              <br />
              <img src={sign_b} alt='sign' style={{ width: 200, height: 100 }} />
              <br />
              (Chữ ký điện tử, chữ ký số)
              <br />
              <Text strong>Nguyễn Thị B</Text>
              <br />
              Chức vụ: Trưởng phòng Kinh doanh
            </Col>
          </Row>
        </Card>
      </div>
    </div>
  )
}
