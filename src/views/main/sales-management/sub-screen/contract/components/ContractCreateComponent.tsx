import React, { useState } from 'react'
import {
  Form,
  Input,
  DatePicker,
  Button,
  Card,
  Row,
  Col,
  Switch,
  Select,
  Table,
  Typography
} from 'antd'
import dayjs from 'dayjs'
import BaseModal from '~/components/BaseModal'
import { ReloadOutlined, SaveOutlined } from '@ant-design/icons'

const { Title } = Typography
const { Option } = Select

// Dữ liệu giả báo giá
const quotationData = {
  quotationCode: 'BG-2025-057',
  quotationDate: '2025-06-17',
  customer: {
    name: 'CÔNG TY TNHH XYZ VIỆT NAM',
    address: '456 <PERSON><PERSON><PERSON><PERSON>, Quận 5, TP.HCM',
    representative: 'Ông <PERSON>uyễn <PERSON>n A – Trưởng phòng Logistics',
    taxCode: '0312xxxxxx',
    phone: '0903 xxx xxx',
    email: '<EMAIL>'
  },
  provider: {
    name: 'CÔNG TY TNHH DỊCH VỤ VẬN TẢI ABC LOGISTICS',
    address: '123 <PERSON><PERSON><PERSON><PERSON>, Quận 7, TP.HCM',
    representative: '<PERSON><PERSON>ễn <PERSON>h<PERSON> B – Trưởng phòng Kinh doanh',
    taxCode: '0312yyyyyy',
    phone: '0933 xxx xxx',
    email: '<EMAIL>'
  },
  services: [
    {
      key: '1',
      service: 'Vận chuyển nội địa',
      description: 'TP.HCM → Hà Nội (3 ngày)',
      unit: 'Chuyến',
      quantity: 1,
      price: 15000000,
      total: 15000000
    },
    {
      key: '2',
      service: 'Phí bốc xếp container',
      description: '2 container 40ft',
      unit: 'Lần',
      quantity: 1,
      price: 2000000,
      total: 2000000
    }
  ]
}

interface ContractCreateModal {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

export const ContractCreateComponent = ({ open, onClose, onSuccess }: ContractCreateModal) => {
  const [form] = Form.useForm()
  const [useQuotation, setUseQuotation] = useState(false)

  const handleSwitchChange = (checked: boolean) => {
    setUseQuotation(checked)

    if (checked) {
      const q = quotationData
      form.setFieldsValue({
        contractCode: 'HD-2025-057',
        signDate: dayjs(q.quotationDate),
        customerName: q.customer.name,
        providerName: q.provider.name
      })
    } else {
      form.resetFields()
    }
  }

  const columns = [
    { title: 'Dịch vụ', dataIndex: 'service' },
    { title: 'Mô tả', dataIndex: 'description' },
    { title: 'Đơn vị', dataIndex: 'unit' },
    { title: 'Số lượng', dataIndex: 'quantity' },
    { title: 'Đơn giá (VNĐ)', dataIndex: 'price' },
    { title: 'Thành tiền (VNĐ)', dataIndex: 'total' }
  ]

  const onFinish = (values: any) => {
    console.log('Contract Created:', values)
  }

  const modalContent = (
    <Card>
      <Row gutter={16} justify='start' style={{ marginBottom: 16 }}>
        <Col span={12}>
          <Form.Item label='Chọn báo giá' name='useQuotation'>
            <Select placeholder='Chọn báo giá' onChange={handleSwitchChange}>
              <Option value=''>--Chọn báo giá--</Option>
              <Option value='BG-2025-056'>BG-2025-056</Option>
              <Option value='BG-2025-057'>BG-2025-057</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label='Chọn mẫu hợp đồng'>
            <Select placeholder='Chọn mẫu hợp đồng'>
              <Option value=''>--Chọn mẫu hợp đồng--</Option>
              <Option value='HD-2025-056'>HD-2025-056</Option>
              <Option value='HD-2025-057'>HD-2025-057</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Form layout='vertical' form={form} onFinish={onFinish}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name='contractCode' label='Số hợp đồng' rules={[{ required: true }]}>
              <Input placeholder='VD: HD-2025-057' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item name='signDate' label='Ngày ký'>
              <DatePicker style={{ width: '100%' }} format='DD/MM/YYYY' />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name='customerName'
              label='Chọn Bên A (Khách hàng)'
              rules={[{ required: true }]}>
              <Select placeholder='Chọn Bên A'>
                <Option value='Công ty TNHH XYZ'>Công ty TNHH XYZ</Option>
                <Option value='Công ty TNHH ABC'>Công ty TNHH ABC</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name='providerName'
              label='Chọn Bên B (Nhà cung cấp)'
              rules={[{ required: true }]}>
              <Select placeholder='Chọn Bên B'>
                <Option value='ABC Logistics'>ABC Logistics</Option>
                <Option value='XYZ Logistics'>XYZ Logistics</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        {useQuotation && (
          <>
            <Title level={5} style={{ marginTop: 24 }}>
              Dịch vụ từ báo giá
            </Title>
            <Table
              columns={columns}
              dataSource={quotationData.services}
              pagination={false}
              bordered
              rowKey='key'
            />
          </>
        )}
        <Row gutter={16}>
          <Col span={24} style={{ textAlign: 'center' }}>
            {/* clear */}
            <Button icon={<ReloadOutlined />} onClick={() => form.resetFields()} style={{ marginRight: 8 }}>
              Làm mới
            </Button>
            <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
              Tạo hợp đồng
            </Button>
          </Col>
        </Row>
      </Form>
    </Card>
  )
  return (
    <BaseModal
      open={open}
      title='Tạo hợp đồng mới'
      description='Thêm hợp đồng mới vào hệ thống'
      onClose={onClose}
      childrenBody={modalContent}
    />
  )
}
