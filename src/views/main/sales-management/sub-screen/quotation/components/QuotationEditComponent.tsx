import React, { useState, useEffect } from "react";
import { Card, Form, Input, InputNumber, Row, Col, Select, Button } from "antd";
import { SaveOutlined } from "@ant-design/icons";

const { TextArea } = Input;
const { Option } = Select;

export const QuotationEditComponent = () => {
  const [form] = Form.useForm();
  const [totalFee, setTotalFee] = useState(0);

  const initialValues = {
    customer: "Công ty ABC",
    fromAddress: "Hà Nội",
    toAddress: "TP. Hồ Chí Minh",
    goodsType: "Hàng khô",
    weight: 500,
    packageCount: 25,
    transportMethod: "Đường bộ",
    baseFee: 1500000,
    packingFee: 200000,
    insuranceFee: 100000,
    storageFee: 50000,
    deliveryTime: "3 ngày",
    paymentTerm: "Thanh toán trong 7 ngày sau giao hàng",
  };

  useEffect(() => {
    const values = form.getFieldsValue();
    const total =
      (values.baseFee || 0) +
      (values.packingFee || 0) +
      (values.insuranceFee || 0) +
      (values.storageFee || 0);
    setTotalFee(total);
  }, [form]);

  const handleValuesChange = () => {
    const values = form.getFieldsValue();
    const total =
      (values.baseFee || 0) +
      (values.packingFee || 0) +
      (values.insuranceFee || 0) +
      (values.storageFee || 0);
    setTotalFee(total);
  };

  const onFinish = (values: any) => {
    console.log("Form Submitted: ", values);
  };

  return (
    <Card title="Chỉnh sửa báo giá">
      <Form
        form={form}
        layout="vertical"
        initialValues={initialValues}
        onValuesChange={handleValuesChange}
        onFinish={onFinish}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item label="Khách hàng" name="customer" rules={[{ required: true }]}>
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Loại dịch vụ" name="goodsType">
              <Input />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item label="Địa chỉ gửi hàng" name="fromAddress">
              <Input />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Địa chỉ nhận hàng" name="toAddress">
              <Input />
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="Khối lượng (kg)" name="weight">
              <InputNumber style={{ width: "100%" }} min={0} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Số lượng kiện" name="packageCount">
              <InputNumber style={{ width: "100%" }} min={0} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Phương thức vận chuyển" name="transportMethod">
              <Select>
                <Option value="Đường bộ">Đường bộ</Option>
                <Option value="Đường thủy">Đường thủy</Option>
                <Option value="Đường hàng không">Đường hàng không</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={6}>
            <Form.Item label="Phí vận chuyển cơ bản" name="baseFee">
              <InputNumber style={{ width: "100%" }} min={0} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Phí đóng gói" name="packingFee">
              <InputNumber style={{ width: "100%" }} min={0} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Phí bảo hiểm" name="insuranceFee">
              <InputNumber style={{ width: "100%" }} min={0} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item label="Phí lưu kho" name="storageFee">
              <InputNumber style={{ width: "100%" }} min={0} />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item label="Tổng phí">
              <InputNumber style={{ width: "100%" }} value={totalFee} readOnly />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item label="Thời gian giao hàng" name="deliveryTime">
              <Input />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item label="Điều kiện thanh toán" name="paymentTerm">
              <TextArea rows={2} />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
            Lưu báo giá
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};
