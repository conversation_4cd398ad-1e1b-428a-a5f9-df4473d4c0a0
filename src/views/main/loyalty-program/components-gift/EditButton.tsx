import { EditOutlined, SaveOutlined } from '@ant-design/icons'
import {
  Card,
  Tag,
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  DatePicker,
  message
} from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import { useModal } from '../../../../hooks/useModal'
import BaseModal from '~/components/BaseModal'
import { useEffect } from 'react'
import { useForm } from 'antd/es/form/Form'
import { IGift } from '~/dto/gift.dto'
import dayjs from 'dayjs'

const { Option } = Select

interface EditButtonProps {
  data: IGift
  onSuccess?: () => void
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { open, openModal, closeModal } = useModal()
  const [form] = useForm()

  const {
    programName,
    memberLevel,
    conversionType,
    giftValue,
    unit,
    startDate,
    endDate,
    status
  } = data || {}

  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        programName,
        memberLevel,
        conversionType,
        giftValue,
        unit,
        startDate: startDate ? dayjs(startDate) : undefined,
        endDate: endDate ? dayjs(endDate) : undefined,
        status
      })
    }
  }, [open, data, form])

  if (!data) return null

  const handleSave = async (values: any) => {
    if (!data) return
    const body = {
      ...values,
      id: data?.id,
      startDate: values.startDate?.toISOString(),
      endDate: values.endDate?.toISOString()
    }

    try {
      // TODO: Implement update loyalty mutation
      // await updateLoyalty(body)
      closeModal()
      onSuccess && onSuccess()
      form.resetFields()
    } catch (error) {
      message.error('Cập nhật chương trình thất bại')
    }
  }

  // Header thông tin chương trình
  const loyaltyHeader = (
    <Card style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={8}>
          <div>
            <BaseText color='textSecondary'>Tên chương trình:</BaseText>
            <br />
            <BaseText weight='bold'>{programName || 'N/A'}</BaseText>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color='textSecondary'>Cấp độ thành viên:</BaseText>
            <br />
            <BaseText weight='bold'>{memberLevel || 'N/A'}</BaseText>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color='textSecondary'>Trạng thái:</BaseText>
            <br />
            <Tag color={status === 'ACTIVE' ? 'green' : 'red'}>
              {status === 'ACTIVE' ? 'Đang hoạt động' : 'Không hoạt động'}
            </Tag>
          </div>
        </Col>
      </Row>
    </Card>
  )

  const modalContent = (
    <div>
      {loyaltyHeader}

      <Form form={form} layout='vertical' onFinish={handleSave}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='Tên chương trình'
              name='programName'
              rules={[
                { required: true, message: 'Vui lòng nhập tên chương trình' }
              ]}>
              <Input placeholder='Tên chương trình' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='Cấp độ thành viên'
              name='memberLevel'
              rules={[
                { required: true, message: 'Vui lòng chọn cấp độ thành viên' }
              ]}>
              <Select placeholder='Chọn cấp độ thành viên'>
                <Option value='BRONZE'>Đồng</Option>
                <Option value='SILVER'>Bạc</Option>
                <Option value='GOLD'>Vàng</Option>
                <Option value='PLATINUM'>Bạch kim</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='Loại quy đổi'
              name='conversionType'
              rules={[
                { required: true, message: 'Vui lòng chọn loại quy đổi' }
              ]}>
              <Select placeholder='Chọn loại quy đổi'>
                <Option value='POINT'>Điểm</Option>
                <Option value='MONEY'>Tiền</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='Giá trị quà'
              name='giftValue'
              rules={[
                { required: true, message: 'Vui lòng nhập giá trị quà' }
              ]}>
              <InputNumber
                style={{ width: '100%' }}
                placeholder='Giá trị quà'
                min={0}
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='Đơn vị'
              name='unit'
              rules={[{ required: true, message: 'Vui lòng nhập đơn vị' }]}>
              <Input placeholder='Đơn vị' />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='Trạng thái'
              name='status'
              rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}>
              <Select placeholder='Chọn trạng thái'>
                <Option value='ACTIVE'>Đang hoạt động</Option>
                <Option value='INACTIVE'>Không hoạt động</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label='Ngày bắt đầu'
              name='startDate'
              rules={[
                { required: true, message: 'Vui lòng chọn ngày bắt đầu' }
              ]}>
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label='Ngày kết thúc'
              name='endDate'
              rules={[
                { required: true, message: 'Vui lòng chọn ngày kết thúc' }
              ]}>
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <div
          style={{
            textAlign: 'right',
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}>
          <Button onClick={closeModal} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Cập nhật chương trình
          </Button>
        </div>
      </Form>
    </div>
  )

  return (
    <>
      <BaseButton
        icon={<EditOutlined />}
        onClick={openModal}
        type='primary'
        tooltip='Chỉnh sửa'
      />
      <BaseModal
        open={open}
        onClose={closeModal}
        title='Chỉnh sửa chương trình'
        description='Cập nhật thông tin chương trình'
        childrenBody={modalContent}
      />
    </>
  )
}

export default EditButton
