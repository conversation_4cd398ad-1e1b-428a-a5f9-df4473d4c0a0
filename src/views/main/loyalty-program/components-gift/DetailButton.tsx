import {
  EyeOutlined,
  AppstoreOutlined,
  DollarOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  CopyOutlined,
  TagOutlined,
  StarOutlined,
  PictureOutlined
} from '@ant-design/icons'
import {
  Card,
  Descriptions,
  Tag,
  Typography,
  Row,
  Col,
  Space,
  Statistic,
  message,
  Image,
  Empty
} from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import { useModal } from '../../../../hooks/useModal'
import BaseModal from '~/components/BaseModal'
import { formatDateCustom, formatMoneyVND } from '~/common/helper/helper'
import { IProduct } from '~/dto/product.dto'
import { EProduct, NSProduct } from '~/common/enums/NSProduct'
import { useDetailProduct } from '~/hooks/product/useDetailProduct'
import { IGift } from '~/dto/gift.dto'

const { Title, Paragraph } = Typography

interface DetailButtonProps {
  data: IGift
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal()

  if (!data) return null

  const {
    programName,
    memberLevel,
    conversionType,
    giftValue,
    unit,
    startDate,
    endDate,
    status,
    createdAt,
    updatedAt,
    images
  } = data

  const modalContent = (
    <div>
      {/* Program Overview Card */}
      <Card
        title={
          <Space>
            <AppstoreOutlined style={{ color: '#1890ff' }} />
            <BaseText>Chi tiết chương trình</BaseText>
          </Space>
        }
        style={{ marginBottom: '16px' }}
        size='small'>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={16}>
            <div>
              <Space align='start'>
                <Title level={3} style={{ margin: 0 }}>
                  {programName || 'N/A'}
                </Title>
                <Tag color='blue' icon={<StarOutlined />}>
                  {memberLevel}
                </Tag>
              </Space>
              <BaseText
                color='textSecondary'
                size='lg'
                style={{ marginTop: '8px', display: 'block' }}>
                Chương trình khách hàng thân thiết
              </BaseText>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <Space direction='vertical' size='middle' style={{ width: '100%' }}>
              <div>
                <BaseText color='textSecondary'>Trạng thái:</BaseText>
                <br />
                <Tag
                  color={status === 'active' ? 'green' : 'red'}
                  style={{ fontSize: '14px', padding: '4px 12px' }}>
                  {status?.toUpperCase() || 'CHƯA XÁC ĐỊNH'}
                </Tag>
              </div>
              <div>
                <BaseText color='textSecondary'>Loại chuyển đổi:</BaseText>
                <br />
                <Tag
                  color='purple'
                  style={{ fontSize: '14px', padding: '4px 12px' }}>
                  {conversionType?.toUpperCase() || 'N/A'}
                </Tag>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      <Row gutter={[16, 16]}>
        {/* Gift Information */}
        <Col xs={24} lg={24}>
          <Card
            title={
              <Space>
                <DollarOutlined style={{ color: '#722ed1' }} />
                <BaseText>Thông tin quà tặng</BaseText>
              </Space>
            }
            size='small'
            style={{ height: '100%' }}>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12}>
                <Statistic
                  title='Giá trị quà tặng'
                  value={giftValue || 0}
                  suffix={unit || 'VNĐ'}
                  valueStyle={{ color: '#722ed1', fontSize: '24px' }}
                />
              </Col>
              <Col xs={24} sm={12}>
                <Statistic
                  title='Đơn vị'
                  value={unit || 'VNĐ'}
                  valueStyle={{ color: '#fa8c16', fontSize: '20px' }}
                />
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>

      {/* Date Information */}
      <Card
        title={
          <Space>
            <CalendarOutlined style={{ color: '#fa8c16' }} />
            <BaseText>Thông tin thời gian</BaseText>
          </Space>
        }
        style={{ marginTop: '16px' }}
        size='small'>
        <Row gutter={[24, 16]}>
          <Col xs={24} sm={12}>
            <div style={{ textAlign: 'center' }}>
              <BaseText color='textSecondary'>Ngày bắt đầu</BaseText>
              <br />
              <BaseText weight='bold'>
                {formatDateCustom(startDate, 'DD/MM/YYYY')}
              </BaseText>
            </div>
          </Col>
          <Col xs={24} sm={12}>
            <div style={{ textAlign: 'center' }}>
              <BaseText color='textSecondary'>Ngày kết thúc</BaseText>
              <br />
              <BaseText weight='bold'>
                {formatDateCustom(endDate, 'DD/MM/YYYY')}
              </BaseText>
            </div>
          </Col>
        </Row>
      </Card>

      {/* System Information */}
      <Card
        title={
          <Space>
            <InfoCircleOutlined style={{ color: '#13c2c2' }} />
            <BaseText>Thông tin hệ thống</BaseText>
          </Space>
        }
        style={{ marginTop: '16px' }}
        size='small'>
        <Row gutter={[24, 16]}>
          <Col xs={24} sm={12}>
            <div style={{ textAlign: 'center' }}>
              <BaseText color='textSecondary'>Ngày tạo</BaseText>
              <br />
              <BaseText weight='bold'>
                {formatDateCustom(createdAt, 'DD/MM/YYYY')}
              </BaseText>
            </div>
          </Col>
          <Col xs={24} sm={12}>
            <div style={{ textAlign: 'center' }}>
              <BaseText color='textSecondary'>Ngày cập nhật</BaseText>
              <br />
              <BaseText weight='bold'>
                {formatDateCustom(updatedAt, 'DD/MM/YYYY')}
              </BaseText>
            </div>
          </Col>
        </Row>
      </Card>

      {/* Media Gallery */}
      {images && images.length > 0 && (
        <Card
          title={
            <Space>
              <PictureOutlined style={{ color: '#eb2f96' }} />
              <BaseText>Hình ảnh chương trình</BaseText>
            </Space>
          }
          style={{ marginTop: '16px' }}
          size='small'>
          <div>
            <BaseText
              color='textSecondary'
              style={{ marginBottom: '12px', display: 'block' }}>
              {images.length} hình ảnh có sẵn
            </BaseText>
            <Row gutter={[12, 12]}>
              {images.map((image, index) => (
                <Col xs={12} sm={8} md={6} lg={4} key={index}>
                  <div
                    style={{
                      border: '1px solid #f0f0f0',
                      borderRadius: '8px',
                      overflow: 'hidden',
                      aspectRatio: '1',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: '#fafafa'
                    }}>
                    <Image
                      src={image}
                      alt={`${programName} - Image ${index + 1}`}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'cover'
                      }}
                      preview={{
                        mask: (
                          <div
                            style={{
                              background: 'rgba(0,0,0,0.5)',
                              color: 'white',
                              fontSize: '12px',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              height: '100%'
                            }}>
                            <EyeOutlined style={{ marginRight: '4px' }} />
                            View
                          </div>
                        )
                      }}
                      fallback='data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN'
                    />
                  </div>
                </Col>
              ))}
            </Row>
          </div>
        </Card>
      )}
    </div>
  )

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />
      <BaseModal
        open={open}
        title='Chi tiết chương trình'
        description='Thông tin chi tiết chương trình khách hàng thân thiết'
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  )
}

export default DetailButton
