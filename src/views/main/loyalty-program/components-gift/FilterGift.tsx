import { Button, Col, Collapse, Form, Input, Row, Select } from 'antd'
import { FC, useCallback } from 'react'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { ELoyaltyStatus } from '~/dto/gift.dto'

interface IProps {
  onFilter: (values: IFilterGift) => void
  onReset: () => void
  isLoading: boolean
}

interface IFilterGift {
  programName: string
  phoneNumber: string
  customerCode: string
  rewardExchange: string
  productName: string
  version: string
  status: string
}

const FilterGift: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading } = props
  const [form] = Form.useForm()

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue())
  }, [form, onFilter])

  const handleReset = useCallback(() => {
    form.resetFields()
    onReset()
  }, [form, onReset])

  return (
    <Collapse>
      <Collapse.Panel header='T<PERSON><PERSON> kiếm' key='0'>
        <Form form={form} layout='vertical'>
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item
                initialValue={null}
                label='Tên chương trình'
                name='programName'>
                <Input placeholder='Nhập tên chương trình' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                initialValue={null}
                label='Số điện thoại'
                name='phoneNumber'>
                <Input placeholder='Nhập số điện thoại' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                initialValue={null}
                label='Mã khách hàng'
                name='customerCode'>
                <Input placeholder='Nhập mã khách hàng' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                initialValue={null}
                label='Đổi thưởng'
                name='rewardExchange'>
                <Input placeholder='Nhập thông tin đổi thưởng' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                initialValue={null}
                label='Tên sản phẩm'
                name='productName'>
                <Input placeholder='Nhập tên sản phẩm' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item initialValue={null} label='Phiên bản' name='version'>
                <Input placeholder='Nhập phiên bản' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item initialValue={null} label='Trạng thái' name='status'>
                <Select placeholder='Chọn trạng thái'>
                  {Object.values(ELoyaltyStatus).map(
                    (status: any, index: number) => (
                      <Select.Option key={index} value={status.code}>
                        {status.name}
                      </Select.Option>
                    )
                  )}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label=' '>
                <div
                  style={{
                    display: 'flex',
                    gap: 10
                  }}>
                  <Button
                    type='primary'
                    style={{ width: '50%' }}
                    htmlType='submit'
                    onClick={handleFilter}
                    loading={isLoading}>
                    <SearchOutlined />
                    Tìm kiếm
                  </Button>
                  <Button
                    type='default'
                    style={{ width: '50%' }}
                    htmlType='submit'
                    onClick={handleReset}>
                    <ReloadOutlined />
                    Làm mới
                  </Button>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  )
}

export default FilterGift
