import {
  AreaChartOutlined,
  CodepenOutlined,
  CustomerServiceOutlined,
  FileProtectOutlined,
  GiftOutlined,
  UnorderedListOutlined,
  UserOutlined,
  TeamOutlined,
  MessageOutlined,
  SettingOutlined,
  HeatMapOutlined,
  ShoppingCartOutlined,
  FlagOutlined,
  ReconciliationOutlined
} from '@ant-design/icons'
import { IRouter } from '~/routers'
import { ListProductView } from './Product'
import { ComplaintManagementView } from './complaint-management'
import { SurveyFeedbackView } from './survey-feedback'
import { Outlet } from 'react-router-dom'
import { MarketingCampaignView } from './Marketing-campaign'
import { MissionsView } from './Customer/list-customer-evaluation/component/tabDetail/activities-tab/Missions'
import { CustomerContactView } from './Customer/list-customer-evaluation/component/tabDetail/activities-tab/CustomerContact'
import {
  subMenuReports,
  subMenuCustomerChildren,
  subMenuMarketingChildren,
  subMenuSurveyFeedbackChildren
} from './sub-menu'
import subMenuSettings from './sub-menu/settings'
import loyaltyChildren from './sub-menu/loyalty'
import subMenuCustomerSupport from './sub-menu/customerSupport'
import { SalesManagementView } from './sales-management'
import subMenuSalesChildren from './sub-menu/sales-management'
import subMenuSettingKpi from './sub-menu/kpi'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

export const MainRouter = () => {
  return [
    createRoute(
      '/',
      <Outlet />,
      'Báo cáo & Dashboard',
      <ReconciliationOutlined  />,
      true,
      subMenuReports
    ),
    createRoute(
      'customer-management',
      <Outlet />,
      'Quản lý khách hàng',
      <UserOutlined />,
      true,
      subMenuCustomerChildren
    ),

    createRoute(
      'sales-management',
      <SalesManagementView />,
      'Quản lý bán hàng',
      <ShoppingCartOutlined />,
      true,
      subMenuSalesChildren
    ),

    createRoute(
      'customer-support',
      <Outlet />,
      'Chăm sóc khách hàng',
      <CustomerServiceOutlined />,
      true,
      subMenuCustomerSupport
    ),

    // ! Chuyển vào menu Chăm sóc khách hàng
    // createRoute(
    //   '/complaint-management',
    //   <ComplaintManagementView />,
    //   'Xử lý khiếu nại',
    //   <UnorderedListOutlined />
    // ),

    createRoute(
      '/marketing-campaign',
      <MarketingCampaignView />,
      'Chiến dịch Marketing',
      <FlagOutlined />,
      true,
      subMenuMarketingChildren
    ),

    createRoute(
      '/survey-feedback',
      <Outlet />,
      'Khảo sát và đánh giá',
      <FileProtectOutlined />,
      true,
      subMenuSurveyFeedbackChildren
    ),

    createRoute(
      '/loyalty-program',
      <Outlet />,
      'Tích điểm đổi quà',
      <GiftOutlined />,
      true,
      loyaltyChildren
    ),
    createRoute('kpi', <Outlet />, 'KPI', <HeatMapOutlined  />, true, subMenuSettingKpi),

    createRoute('settings', <Outlet />, 'Thiết lập', <SettingOutlined />, true, subMenuSettings),
  ]
}
