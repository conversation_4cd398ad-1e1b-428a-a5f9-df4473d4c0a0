import {
  LineChartOutlined,
  Pie<PERSON>hartOutlined,
  RadarChartOutlined,
  UnorderedListOutlined,
  UserOutlined
} from '@ant-design/icons'
import { IRouter } from '~/routers'
import { KPICategoryView } from '../KPI/KPICategory'
import { KPIGroupView } from '../KPI/KPIGroup'
import { EvaluateKPIView } from '../KPI/EvaluateKPI'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const subMenuSettingKpi = [
  {
    path: 'kpi-category',
    title: '<PERSON><PERSON> mụ<PERSON>',
    view: <KPICategoryView />,
    icon: <LineChartOutlined />,
    isMenu: true
  },
  {
    path: 'kpi-group',
    title: 'B<PERSON> KPI',
    view: <KPIGroupView />,
    icon: <PieChartOutlined />,
    isMenu: true
  },
  {
    path: 'evaluate-kpi',
    title: 'Đánh giá KPI',
    view: <EvaluateKPIView />,
    icon: <RadarChartOutlined />,
    isMenu: true
  }
].map((item) => createMenuItem(item.path, item.view, item.title, item.icon, item.isMenu))

export default subMenuSettingKpi
