import {
  AppstoreOutlined,
  BulbOutlined,
  CalendarOutlined,
  GiftOutlined,
  LineC<PERSON>Outlined,
  MailOutlined,
  RiseOutlined,
  UnorderedListOutlined
} from '@ant-design/icons'
import { IRouter } from '~/routers'
import { TargetView } from '../Marketing-campaign/sub-screen/target'
import { ContentView } from '../Marketing-campaign/sub-screen/content'
import { CampaignView } from '../Marketing-campaign/sub-screen/campaign'
import { TargetEditComponent } from '../Marketing-campaign/sub-screen/target/components/TargetEditComponent'
import { ContentEditComponent } from '../Marketing-campaign/sub-screen/content/components/ContentEditComponent'
import { CampaignEditComponent } from '../Marketing-campaign/sub-screen/campaign/components/CampaignEditComponent'
import { ReportBirthdayView } from '../Marketing-campaign/sub-screen/report-birthday'
import { MarketingReportKPIView } from '../Marketing-campaign/sub-screen/report-kpi'
import { MarketingReportZNSView } from '../Marketing-campaign/sub-screen/report-zns'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const subMenuChildren = [
  {
    path: 'target-group',
    title: 'Nhóm mục tiêu',
    view: <TargetView />,
    icon: <RiseOutlined />,
    isMenu: true
  },
  {
    path: 'target-group/edit',
    title: 'Chỉnh sửa nhóm mục tiêu',
    view: <TargetEditComponent />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  {
    path: 'content',
    title: 'Quản lý nội dung',
    view: <ContentView />,
    icon: <BulbOutlined />,
    isMenu: true
  },
  {
    path: 'content/edit',
    title: 'Chỉnh sửa nội dung',
    view: <ContentEditComponent />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  {
    path: 'campaign',
    title: 'Quản lý chiến dịch',
    view: <CampaignView />,
    icon: <CalendarOutlined />,
    isMenu: true
  },
  {
    path: 'campaign/edit',
    title: 'Chỉnh sửa chiến dịch',
    view: <CampaignEditComponent />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  /**
   * Báo cáo sinh nhật khách hàng
   * Báo cáo KPI
   * Báo cáo kết quả gửi ZNS
   */
  {
    path: 'report-birthday',
    title: 'Báo cáo sinh nhật khách hàng',
    view: <ReportBirthdayView />,
    icon: <GiftOutlined />,
    isMenu: true
  },
  {
    path: 'report-kpi-marketing',
    title: 'Báo cáo KPI nhân viên marketing',
    view: <MarketingReportKPIView />,
    icon: <LineChartOutlined />,
    isMenu: true
  },
  {
    path: 'report-zns',
    title: 'Báo cáo kết quả gửi ZNS',
    view: <MarketingReportZNSView />,
    icon: <MailOutlined />,
    isMenu: true
  }
].map((item) => createMenuItem(item.path, item.view, item.title, item.icon, item.isMenu))

export default subMenuChildren
