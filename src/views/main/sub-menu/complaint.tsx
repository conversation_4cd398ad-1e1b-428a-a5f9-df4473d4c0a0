import { AppstoreOutlined, UnorderedListOutlined } from '@ant-design/icons'
import { IRouter } from '~/routers'
import { ListProductView } from '../Product'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const subMenuChildren = [
  {
    path: 'report',
    title: 'List Report',
    view: <ListProductView />,
    icon: <AppstoreOutlined />
  },
  {
    path: 'product',
    title: 'List Product',
    view: <ListProductView />,
    icon: <AppstoreOutlined />
  }
].map((item) => createMenuItem(item.path, item.view, item.title, item.icon))

export default subMenuChildren
