import { AppstoreOutlined, LineChartOutlined, SolutionOutlined, TeamOutlined, UnorderedListOutlined, UserOutlined } from '@ant-design/icons'
import { IRouter } from '~/routers'
import {
  ListContactView,
  ListContactEvaluationView,
  ListCustomerEvaluationView,
  ListNotIdentifyView,
  ListCustomerView
} from '../Customer'
import { CustomerReportKPIView } from '../Customer/report-kpi'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const subMenuChildren = [
  {
    path: 'list-customer',
    title: '<PERSON>h sách khách hàng',
    view: <ListCustomerView />,
    icon: <TeamOutlined />,
    isMenu: true
  },
  {
    path: 'list-contact',
    title: 'Danh sách liên hệ',
    view: <ListContactView />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  {
    path: 'list-not-identify',
    title: 'Khách hàng chưa định danh',
    view: <ListNotIdentifyView />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  {
    path: 'list-customer-evaluation',
    title: 'Đánh giá khách hàng',
    view: <ListCustomerEvaluationView />,
    icon: <SolutionOutlined />,
    isMenu: true
  },
  {
    path: 'list-contact-evaluation',
    title: 'Đánh giá liên hệ',
    view: <ListContactEvaluationView />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  // Báo cáo KPI
  {
    path: 'report-kpi-customer',
    title: 'Báo cáo KPI',
    view: <CustomerReportKPIView />,
    icon: <LineChartOutlined />,
    isMenu: false
  }
].map((item) =>
  createMenuItem(item.path, item.view, item.title, item.icon, item.isMenu)
)

export default subMenuChildren
