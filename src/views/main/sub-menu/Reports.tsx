import {
  AreaChartOutlined,
  DollarOutlined,
  FileDoneOutlined,
  LineChartOutlined,
  UnorderedListOutlined,
  UsergroupAddOutlined,
  UserOutlined
} from '@ant-design/icons'
import { IRouter } from '~/routers'
import { ReportView } from '../Report'
import { CustomerSummaryReport } from '../Report/subScreen/CustomerSummaryReport'
import { CustomerRevenueReport } from '../Report/subScreen/CustomerRevenueReport'
import { CustomerBySalesRepReport } from '../Report/subScreen/CustomerBySalesRepReport'
import { CustomerKPIReport } from '../Report/subScreen/CustomerKPIReport'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const subMenuReports = [
  {
    path: '/report-dashboard',
    title: 'Phân tích',
    view: <ReportView />,
    icon: <AreaChartOutlined />,
    isMenu: true
  },
  {
    path: '/report-customer-summary',
    title: 'Báo cáo tổng hợp khách hàng',
    view: <CustomerSummaryReport />,
    icon: <UserOutlined />,
    isMenu: true
  },
  {
    path: '/report-customer-revenue',
    title: 'Doanh số khách hàng',
    view: <CustomerRevenueReport />,
    icon: <DollarOutlined />,
    isMenu: true
  },
  {
    path: '/report-customer-by-sales-rep',
    title: 'Báo cáo khách hàng theo nhân viên kinh doanh',
    view: <CustomerBySalesRepReport />,
    icon: <UsergroupAddOutlined />,
    isMenu: true
  },
  {
    path: '/report-kpi-dashboard',
    title: 'KPI Dashboard',
    view: <CustomerKPIReport />,
    icon: <LineChartOutlined />,
    isMenu: true
  }
].map((item) =>
  createMenuItem(item.path, item.view, item.title, item.icon, item.isMenu)
)

export default subMenuReports
