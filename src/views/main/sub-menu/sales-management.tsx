import {
  AppstoreOutlined,
  AuditOutlined,
  BookOutlined,
  ContactsOutlined,
  ContainerOutlined,
  FileDoneOutlined,
  LineChartOutlined,
  UnorderedListOutlined
} from '@ant-design/icons'
import { IRouter } from '~/routers'
import { QuotationsComponent } from '../sales-management/sub-screen/quotation/QuotationsComponent'
import { ContractsComponent } from '../sales-management/sub-screen/contract/ContractsComponent'
import { InvoicesComponent } from '../sales-management/sub-screen/invoice/InvoicesComponent'
import { InvoiceDetailComponent } from '../sales-management/sub-screen/invoice/components/InvoiceDetailComponent'
import { QuotationEditComponent } from '../sales-management/sub-screen/quotation/components/QuotationEditComponent'
import { QuotationDetailComponent } from '../sales-management/sub-screen/quotation/components/QuotationDetailComponent'
import { ContractEditComponent } from '../sales-management/sub-screen/contract/components/ContractEditComponent'
import { ContractDetailComponent } from '../sales-management/sub-screen/contract/components/ContractDetailComponent'
import { QuotationReviewComponent } from '../sales-management/sub-screen/quotation/components/QuotationReviewComponent'
import { SalesReportKPIView } from '../sales-management/sub-screen/report-kpi'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const subMenuSalesChildren = [
  {
    path: 'quotations',
    title: 'Báo giá',
    view: <QuotationsComponent />,
    icon: <ContainerOutlined />,
    isMenu: true
  },
  {
    path: 'quotations/edit',
    title: 'Chỉnh sửa báo giá',
    view: <QuotationEditComponent />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  {
    path: 'quotations/detail',
    title: 'Chi tiết báo giá',
    view: <QuotationDetailComponent />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  {
    path: 'quotations/review',
    title: 'Xem trước báo giá',
    view: <QuotationReviewComponent />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  {
    path: 'contracts',
    title: 'Hợp đồng',
    view: <ContractsComponent />,
    icon: <FileDoneOutlined  />,
    isMenu: true
  },
  {
    path: 'contracts/edit',
    title: 'Chỉnh sửa hợp đồng',
    view: <ContractEditComponent />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  {
    path: 'contracts/detail',
    title: 'Chi tiết hợp đồng',
    view: <ContractDetailComponent />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  {
    path: 'invoices',
    title: 'Hóa đơn',
    view: <InvoicesComponent />,
    icon: <AuditOutlined  />,
    isMenu: true
  },
  {
    path: 'invoices/detail',
    title: 'Chi tiết hóa đơn',
    view: <InvoiceDetailComponent />,
    icon: <AppstoreOutlined />,
    isMenu: false
  },
  {
    path: 'report-kpi-sale',
    title: 'Báo cáo KPI nhân viên kinh doanh',
    view: <SalesReportKPIView />,
    icon: <LineChartOutlined />,
    isMenu: true
  }
].map((item) => createMenuItem(item.path, item.view, item.title, item.icon, item.isMenu))

export default subMenuSalesChildren
