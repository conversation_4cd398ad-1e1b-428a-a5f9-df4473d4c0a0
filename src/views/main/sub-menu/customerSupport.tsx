import { AppstoreOutlined, FileExclamationOutlined, FileUnknownOutlined, HeartOutlined, IdcardOutlined, LineChartOutlined, TeamOutlined, UnorderedListOutlined } from '@ant-design/icons'
import { IRouter } from '~/routers'
import { CustomerSupportView } from '../CustomerContact'
import { SupportMissionsView } from '../Missions'
import { ListContactView } from '../Customer'
import { SupportReportKPIView } from '../CustomerContact/sub-screen/report-kpi'
import { ComplaintManagementView } from '../complaint-management'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const subMenuCustomerSupport = [
  // Danh sách liên hệ
  {
    path: 'list-contact',
    title: 'Danh sách liên hệ',
    view: <ListContactView />,
    icon: <IdcardOutlined  />,
  },
  {
    path: 'customer-contact',
    title: 'Thăm hỏi khách hàng',
    view: <CustomerSupportView />,
    icon: <HeartOutlined />
  },
  {
    path: 'missions',
    title: 'Nhiệm vụ',
    view: <SupportMissionsView />,
    icon: <FileUnknownOutlined />
  },
  // Xử lý khiếu nại
  {
    path: 'complaint-management',
    title: 'Xử lý khiếu nại',
    view: <ComplaintManagementView />,
    icon: <FileExclamationOutlined />,
  },
  // Báo cáo KPI Support
  {
    path: 'report-kpi-support',
    title: 'Báo cáo KPI nhân viên CSKH',
    view: <SupportReportKPIView />,
    icon: <LineChartOutlined />,
  }
].map((item) => createMenuItem(item.path, item.view, item.title, item.icon))

export default subMenuCustomerSupport
