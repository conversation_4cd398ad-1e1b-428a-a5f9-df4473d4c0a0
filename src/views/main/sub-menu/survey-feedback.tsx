import {
  AreaChartOutlined,
  CommentOutlined,
  FileDoneOutlined,
  FileProtectOutlined,
  UnorderedListOutlined
} from '@ant-design/icons'
import { IRouter } from '~/routers'
import { ReportView } from '../Report'
import ReportSurveyFeedback from '../survey-feedback/ReportSurveyFeedback'
import { SurveyFeedbackView } from '../survey-feedback'
import { SurveyReportKPIView } from '../survey-feedback/sub-screen/report-kpi.tsx'

const createRoute = (
  path: string,
  element: JSX.Element,
  title: string,
  icon: JSX.Element,
  isMenu = true,
  children: IRouter[] = []
): IRouter => ({
  path,
  key: `route-${path}`,
  element,
  title,
  isMenu,
  icon,
  children
})

const createMenuItem = (
  path: string,
  element: JSX.Element,
  title: string,
  icon = <UnorderedListOutlined />,
  isMenu?: boolean
): IRouter => createRoute(path, element, title, icon, isMenu)

const subMenuSurveyFeedbackChildren = [
  {
    path: 'survey-feedback',
    title: 'Danh sách khảo sát',
    view: <SurveyFeedbackView />,
    icon: <FileProtectOutlined />,
    isMenu: true
  },
  {
    path: 'report-survey-feedback',
    title: 'Báo cáo kết quả khảo sát',
    view: <ReportSurveyFeedback />,
    icon: <CommentOutlined  />,
    isMenu: true
  },
  // {
  //   path: 'survey-report-kpi',
  //   title: 'Báo cáo KPI Khảo sát',
  //   view: <SurveyReportKPIView />,
  //   icon: <AreaChartOutlined />,
  //   isMenu: true
  // }
].map((item) => createMenuItem(item.path, item.view, item.title, item.icon))

export default subMenuSurveyFeedbackChildren
