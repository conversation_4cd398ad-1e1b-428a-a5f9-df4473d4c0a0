import BaseView from '~/components/BaseView'
import { useTranslation } from 'react-i18next'
import { IKpiCategory, IKpiCategoryFilter } from '~/dto/Kpi.dto'
import { Tag } from 'antd'
import { ColumnsType } from 'antd/es/table'
import BaseTable from '~/components/BaseTable'
import EditButton from './components/EditButton'
import { DeleteOutlined } from '@ant-design/icons'
import { BaseButton } from '~/components'
import DetailButton from './components/DetailButton'
import { useState } from 'react'
import FilterKPICategory from './components/FilterKPICategory'
export const KPICategoryView = () => {
  const { t } = useTranslation()
  //filter
  const [filter, setFilter] = useState<IKpiCategoryFilter>({
    pageIndex: 0,
    pageSize: 10
  })
  const columns: ColumnsType<IKpiCategory> = [
    {
      title: 'STT',
      key: 'stt',
      width: '5%',
      align: 'center',
      render: (_, __, index) => index + 1
    },
    { title: 'Mã KPI', dataIndex: 'kpiCode', key: 'kpiCode', width: '10%', align: 'center' },
    {
      title: 'Tên KPI',
      dataIndex: 'kpiName',
      key: 'kpiName',
      align: 'center',
      width: '20%'
    },
    { title: 'Phòng ban', dataIndex: 'department', key: 'department', width: '15%',align: 'center' },
    { title: 'Đơn vị đo', dataIndex: 'unit', key: 'unit', width: '10%', align: 'center' },
    { title: 'Tần suất đo', dataIndex: 'frequency', key: 'frequency', width: '10%', align: 'center' },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      width: '10%',
      align: 'center',
      render: (value: string) => <Tag color={value === 'Hoạt động' ? 'green' : 'gray'}>{value}</Tag>
    },
    {
      title: 'Tác vụ',
      key: 'action',
      width: 100,
      align: 'center',
      render: (_, record) => (
        <>
          <EditButton data={record}></EditButton>
          <DetailButton data={record}></DetailButton>
          <BaseButton
            danger
            type='primary'
            shape='circle'
            icon={<DeleteOutlined />}
            tooltip='Delete'
            onClick={() => handleDelete(record)}
          />
        </>
      )
    }
  ]
  const handleDelete = (record: IKpiCategory) => {
    setFakeData(fakeData.filter((item) => item.kpiCode !== record.kpiCode))
  }

  const [fakeData, setFakeData] = useState<IKpiCategory[]>([
    {
      kpiCode: 'KPI01',
      kpiName: 'Số khách hàng mới',
      department: 'Kinh doanh',
      unit: 'khách hàng',
      frequency: 'tháng',
      status: 'Hoạt động'
    },
    {
      kpiCode: 'KPI02',
      kpiName: 'Số cuộc gọi / gặp khách',
      department: 'Kinh doanh',
      unit: 'lượt',
      frequency: 'tháng',
      status: 'Hoạt động'
    },
    {
      kpiCode: 'KPI03',
      kpiName: 'Số báo giá gửi đi',
      department: 'Kinh doanh',
      unit: 'báo giá',
      frequency: 'tháng',
      status: 'Hoạt động'
    },
    {
      kpiCode: 'KPI04',
      kpiName: 'Doanh số ký hợp đồng',
      department: 'Kinh doanh',
      unit: 'VND',
      frequency: 'tháng',
      status: 'Hoạt động'
    },
    {
      kpiCode: 'KPI05',
      kpiName: 'Tỷ lệ chốt đơn',
      department: 'Kinh doanh',
      unit: '%',
      frequency: 'tháng',
      status: 'Hoạt động'
    },
    {
      kpiCode: 'KPI06',
      kpiName: 'Tỷ lệ cập nhật CRM',
      department: 'Kinh doanh',
      unit: '%',
      frequency: 'tháng',
      status: 'Hoạt động'
    }
  ])

  const handleFilter = (values: IKpiCategoryFilter) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFakeData(fakeData)
  }

  return (
    <BaseView>
      <FilterKPICategory onFilter={handleFilter} onReset={handleReset} isLoading={false} />
      <BaseTable columns={columns} data={fakeData} total={fakeData.length} isLoading={false} />
    </BaseView>
  )
}
