import { <PERSON><PERSON>, Col, Collapse, DatePicker, Form, Input, Row, Select } from 'antd'
import { FC, useCallback } from 'react'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { EProduct } from '~/common/enums/NSProduct'
import { IFilterCustomerContact } from '~/dto/complaint.dto'
import { IKpiCategoryFilter } from '~/dto/Kpi.dto'

interface IProps {
  onFilter: (values: IKpiCategoryFilter) => void
  onReset: () => void
  isLoading: boolean
}

const FilterKPICategory: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading } = props
  const [form] = Form.useForm()

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue())
  }, [form, onFilter])

  const handleReset = useCallback(() => {
    form.resetFields()
    onReset()
  }, [form, onReset])

  const unit: any[] = [
    { label: 'Kh<PERSON>ch hàng', key: 'khách hàng' },
    { label: 'L<PERSON>ợ<PERSON>', key: 'lượt' },
    { label: 'Báo giá', key: 'báo giá' },
    { label: 'VNĐ', key: 'VNĐ' },
    { label: '%', key: '%' },
    { label: 'Cuộc gọi', key: 'cuộc' },
    { label: 'Lead', key: 'Lead' }
  ]

  const frequency: any[] = [
    { label: 'Tháng', key: 'tháng' },
    { label: 'Quý', key: 'quý' },
    { label: 'Năm', key: 'năm' }
  ]

  const department: any[] = [
    { label: 'Kinh doanh', key: 'Kinh doanh' },
    { label: 'Marketing', key: 'Marketing' },
    { label: 'Chăm sóc khách hàng', key: 'CSKH' }
  ]

  return (
    <Collapse>
      <Collapse.Panel header='Tìm kiếm' key='0'>
        <Form form={form} layout='vertical'>
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item name='kpiCode' label='Mã KPI'>
                <Input placeholder='Nhập mã KPI' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='kpiName' label='Tên KPI'>
                <Input placeholder='Nhập tên KPI' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='department' label='Phòng ban'>
                <Select placeholder='Chọn phòng ban' allowClear>
                  {department.map((item) => (
                    <Select.Option key={item.key}>{item.label}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='unit' label='Đơn vị đo'>
                <Select placeholder='Chọn đơn vị đo' allowClear>
                  {unit.map((item) => (
                    <Select.Option key={item.key}>{item.label}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='frequency' label='Tần suất đo'>
                <Select placeholder='Chọn tần suất đo' allowClear>
                  {frequency.map((item) => (
                    <Select.Option key={item.key}>{item.label}</Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='status' label='Trạng thái'>
                <Select placeholder='Chọn trạng thái' allowClear>
                  <Select.Option value='Hoạt động'>Hoạt động</Select.Option>
                  <Select.Option value='Không hoạt động'>Không hoạt động</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24} style={{ marginTop: 10 }}>
            <Form.Item style={{ width: '100%' }}>
              <div
                style={{
                  display: 'flex',
                  gap: 10,
                  justifyContent: 'center'
                }}>
                <Button
                  type='primary'
                  style={{ width: '15%' }}
                  htmlType='submit'
                  onClick={handleFilter}
                  loading={isLoading}>
                  <SearchOutlined />
                  Tìm kiếm
                </Button>
                <Button
                  type='default'
                  style={{ width: '15%' }}
                  htmlType='submit'
                  onClick={handleReset}>
                  <ReloadOutlined />
                  Làm mới
                </Button>
              </div>
            </Form.Item>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  )
}

export default FilterKPICategory
