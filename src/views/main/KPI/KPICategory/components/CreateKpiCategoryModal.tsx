import { SaveOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Switch,
  Upload,
  message,
  DatePicker,
  Radio
} from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useEffect, useState } from 'react'
import { useCreateProduct } from '~/hooks/product/useCreateProduct'
import { CreateProductReq } from '~/dto/product.dto'
import { toastService } from '~/services'
import type { UploadFile } from 'antd'
import useUploadSingle from '~/hooks/uploadFile/useUploadSingle'
import { useModal } from '~/hooks/useModal'
import moment from 'moment'

const { Option } = Select
const { TextArea } = Input

interface CreateProductModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateKpiCategoryModal = ({ open, onClose, onSuccess }: CreateProductModalProps) => {
  const [form] = useForm()

  useEffect(() => {
    if (open) {
      form.setFieldsValue({
        kpiCode: null,
        kpiName: null,
        department: null,
        unit: null,
        frequency: null,
        description: null,
        status: null
      })
      // Set existing images to fileList
    }
  }, [open])

  const unit: any[] = [
    { label: 'Khách hàng', key: 'khách hàng' },
    { label: 'Lượt', key: 'lượt' },
    { label: 'Báo giá', key: 'báo giá' },
    { label: 'VNĐ', key: 'VNĐ' },
    { label: '%', key: '%' },
    { label: 'Cuộc gọi', key: 'cuộc' },
    { label: 'Lead', key: 'Lead' }
  ]

  const frequency: any[] = [
    { label: 'Tháng', key: 'tháng' },
    { label: 'Quý', key: 'quý' },
    { label: 'Năm', key: 'năm' }
  ]

  const department: any[] = [
    { label: 'Kinh doanh', key: 'Kinh doanh' },
    { label: 'Marketing', key: 'Marketing' },
    { label: 'Chăm sóc khách hàng', key: 'CSKH' }
  ]

  const modalContent = (
    <div>
      <Form form={form} layout='vertical'>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label='Mã KPI' name='kpiCode'>
              <Input placeholder='Nhập mã KPI' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Tên KPI' name='kpiName'>
              <Input placeholder='Nhập tên KPI' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Phòng ban' name='department'>
              <Select>
                {department.map((item) => (
                  <Select.Option key={item.key}>{item.label}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Đơn vị đo' name='unit'>
              <Select>
                {unit.map((item) => (
                  <Select.Option key={item.key}>{item.label}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Tần suất đo' name='frequency'>
              <Select>
                {frequency.map((item) => (
                  <Select.Option key={item.key}>{item.label}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label='Mô tả' name='description'>
              <TextArea rows={4} placeholder='Nhập mô tả' />
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Row>
        <Col span={24} style={{ textAlign: 'center' }}>
          <Button onClick={onClose} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Thêm mới
          </Button>
        </Col>
      </Row>
    </div>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo mới danh mục KPI'
      description='Tạo mới danh mục KPI'
      childrenBody={modalContent}
    />
  )
}

export default CreateKpiCategoryModal
