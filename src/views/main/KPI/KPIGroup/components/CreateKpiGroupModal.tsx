import { PlusOutlined, SaveOutlined } from '@ant-design/icons'
import { Row, Col, Form, Input, Button, Select, Card, Table } from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useEffect, useState } from 'react'

const { Option } = Select
const { TextArea } = Input

interface CreateProductModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateKpiGroupModal = ({ open, onClose, onSuccess }: CreateProductModalProps) => {
  const [form] = useForm()

  useEffect(() => {
    if (open) {
      form.setFieldsValue({
        groupName: null,
        department: null,
        createdBy: null,
        createdDate: null,
        updatedDate: null,
        status: null
      })
      setKpiCategoryData([])
      // Set existing images to fileList
    }
  }, [open])
  const handleSelectKpi = (index: number) => (value: string) => {
    const selectedKpi = allKpiCategoryData.find(
      (item) => item.kpiCode === value || item.kpiName === value
    )
    if (selectedKpi) {
      const newData = [...kpiCategoryData]
      newData[index] = {
        kpiCode: selectedKpi.kpiCode,
        kpiName: selectedKpi.kpiName,
        unit: selectedKpi.unit,
        target: 0,
        weight: 0
      }
      setKpiCategoryData(newData)
    }
  }

  const frequency: any[] = [
    { label: 'Tháng', key: 'tháng' },
    { label: 'Quý', key: 'quý' },
    { label: 'Năm', key: 'năm' }
  ]

  const department: any[] = [
    { label: 'Kinh doanh', key: 'Kinh doanh' },
    { label: 'Marketing', key: 'Marketing' },
    { label: 'Chăm sóc khách hàng', key: 'CSKH' }
  ]

  const [kpiCategoryData, setKpiCategoryData] = useState<any[]>([])

  const columns: any[] = [
    {
      title: 'Mã KPI',
      dataIndex: 'kpiCode',
      key: 'kpiCode',
      width: 50,
      align: 'center',
      render: (value: string, record: any, index: number) => (
        <Select
          value={value}
          style={{ width: '100%', textAlign: 'left' }}
          onChange={handleSelectKpi(index)}>
          {allKpiCategoryData.map((item) => (
            <Option key={item.kpiCode} value={item.kpiCode}>
              {item.kpiCode}
            </Option>
          ))}
        </Select>
      )
    },
    {
      title: 'Tên KPI',
      dataIndex: 'kpiName',
      key: 'kpiName',
      width: 200,
      align: 'center',
      render: (value: string, _: any, index: number) => (
        <Select
          value={value}
          style={{ width: '100%', textAlign: 'left' }}
          onChange={handleSelectKpi(index)}>
          {allKpiCategoryData.map((item) => (
            <Option key={item.kpiCode} value={item.kpiName}>
              {item.kpiName}
            </Option>
          ))}
        </Select>
      )
    },
    {
      title: 'Đơn vị đo',
      dataIndex: 'unit',
      key: 'unit',
      width: 100,
      align: 'center',
      render: (value: string) => (
        <Select disabled value={value} style={{ width: '100%', textAlign: 'left' }}>
          {kpiCategoryData.map((item) => (
            <Option key={item.kpiCode} value={item.unit}>
              {item.unit}
            </Option>
          ))}
        </Select>
      )
    },
    {
      title: 'Chỉ tiêu',
      dataIndex: 'target',
      key: 'target',
      width: 100,
      align: 'center',
      render: (_, record, index) => (
        <Input
          value={record.target}
          style={{ width: '100%', textAlign: 'left' }}
          onChange={(e) => handleInputChange(e.target.value, index, 'target')}
        />
      )
    },
    {
      title: 'Trọng số (%)',
      dataIndex: 'weight',
      key: 'weight',
      width: 100,
      align: 'center',
      render: (_, record, index) => (
        <Input
          value={record.weight}
          style={{ width: '100%', textAlign: 'left' }}
          onChange={(e) => handleInputChange(e.target.value, index, 'weight')}
        />
      )
    }
  ]
  const handleAddNewKpi = () => {
    setKpiCategoryData([
      ...kpiCategoryData,
      { kpiCode: '', kpiName: '', unit: '', target: 0, weight: 0 }
    ])
  }

  const handleInputChange = (value: any, index: number, key: string) => {
    setKpiCategoryData(
      kpiCategoryData?.map((item, dataIndex: number) => {
        if (dataIndex === index) {
          item[key] = value
        }
        return item
      })
    )
  }
  const allKpiCategoryData = [
    { kpiCode: 'KPI01', kpiName: 'Số khách hàng mới', unit: 'khách hàng', target: 10, weight: 30 },
    { kpiCode: 'KPI02', kpiName: 'Số cuộc gọi / gặp khách', unit: 'lượt', target: 20, weight: 10 },
    { kpiCode: 'KPI03', kpiName: 'Số báo giá gửi đi', unit: 'báo giá', target: 20, weight: 10 },
    { kpiCode: 'KPI04', kpiName: 'Doanh số ký hợp đồng', unit: 'VND', target: 3, weight: 30 },
    { kpiCode: 'KPI05', kpiName: 'Tỷ lệ chốt đơn', unit: '%', target: 30, weight: 10 },
    { kpiCode: 'KPI06', kpiName: 'Tỷ lệ cập nhật CRM', unit: '%', target: 100, weight: 10 }
  ]

  const modalContent = (
    <div>
      <Form form={form} layout='vertical'>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label='Tên bộ KPI' name='groupName'>
              <Input placeholder='Nhập tên bộ KPI' />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Tần suất đo' name='frequency'>
              <Select allowClear placeholder='Chọn tần suất đo'>
                {frequency.map((item) => (
                  <Select.Option key={item.key}>{item.label}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label='Phòng ban' name='department'>
              <Select allowClear placeholder='Chọn phòng ban'>
                {department.map((item) => (
                  <Select.Option key={item.key}>{item.label}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Card
          title={
            <Row>
              <span style={{ fontSize: 16, marginRight: 16 }}>Thiết lập chỉ tiêu</span>
              <span>
                <Button onClick={handleAddNewKpi} type='primary' icon={<PlusOutlined />} />
              </span>
            </Row>
          }>
          <Table dataSource={kpiCategoryData} columns={columns} pagination={false} />
        </Card>
      </Form>
      <Row>
        <Col span={24} style={{ textAlign: 'center' }}>
          <Button onClick={onClose} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Thêm mới
          </Button>
        </Col>
      </Row>
    </div>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo mới Bộ KPI'
      description='Tạo mới Bộ KPI'
      childrenBody={modalContent}
    />
  )
}

export default CreateKpiGroupModal
