import { <PERSON><PERSON>, Col, Collapse, DatePicker, Form, Input, Row, Select } from 'antd'
import { FC, useCallback } from 'react'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { EProduct } from '~/common/enums/NSProduct'
import { IFilterCustomerContact } from '~/dto/complaint.dto'
import { IKpiCategoryFilter, IKpiGroupFilter } from '~/dto/Kpi.dto'

interface IProps {
  onFilter: (values: IKpiGroupFilter) => void
  onReset: () => void
  isLoading: boolean
}

const FilterKPI: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading } = props
  const [form] = Form.useForm()

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue())
  }, [form, onFilter])

  const handleReset = useCallback(() => {
    form.resetFields()
    onReset()
  }, [form, onReset])

  return (
    <Collapse>
      <Collapse.Panel header='Tìm kiếm' key='0'>
        <Form form={form} layout='vertical'>
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item name='kpiGroupName' label='Tên nhóm KPI'>
                <Input placeholder='Nhập tên nhóm KPI' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='frequency' label='Tần suất đo'>
                <Input placeholder='Nhập tần suất đo' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='department' label='Phòng ban'>
                <Input placeholder='Nhập phòng ban' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='createdBy' label='Người tạo'>
                <Input placeholder='Nhập người tạo' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='createdDate' label='Ngày tạo'>
                <DatePicker placeholder='Chọn ngày tạo' allowClear style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='updatedDate' label='Ngày cập nhật'>
                <DatePicker placeholder='Chọn ngày cập nhật' allowClear style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={6}>
              <Form.Item name='status' label='Trạng thái'>
                <Select placeholder='Chọn trạng thái'>
                  <Select.Option value='Hoạt động'>Hoạt động</Select.Option>
                  <Select.Option value='Ngưng hoạt động'>Ngưng hoạt động</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24} style={{ marginTop: 10 }}>
            <Form.Item style={{ width: '100%' }}>
              <div
                style={{
                  display: 'flex',
                  gap: 10,
                  justifyContent: 'center'
                }}>
                <Button
                  type='primary'
                  style={{ width: '15%' }}
                  htmlType='submit'
                  onClick={handleFilter}
                  loading={isLoading}>
                  <SearchOutlined />
                  Tìm kiếm
                </Button>
                <Button
                  type='default'
                  style={{ width: '15%' }}
                  htmlType='submit'
                  onClick={handleReset}>
                  <ReloadOutlined />
                  Làm mới
                </Button>
              </div>
            </Form.Item>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  )
}

export default FilterKPI
