import {
  EyeOutlined,
  AppstoreOutlined,
  DollarOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  CopyOutlined,
  TagOutlined,
  StarOutlined,
  PictureOutlined
} from '@ant-design/icons'
import {
  Card,
  Descriptions,
  Tag,
  Typography,
  Row,
  Col,
  Space,
  Statistic,
  message,
  Image,
  Empty,
  Form,
  StepProps,
  Steps,
  DatePicker,
  Input,
  Radio,
  Select,
  Divider
} from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import { useModal } from '../../../../../hooks/useModal'
import BaseModal from '~/components/BaseModal'
import { formatDateCustom, formatMoneyVND } from '~/common/helper/helper'
import { IProduct } from '~/dto/product.dto'
import { EProduct, NSProduct } from '~/common/enums/NSProduct'
import { useDetailProduct } from '~/hooks/product/useDetailProduct'
import { ICustomerContact } from '~/dto/customer_contact.dto'
import TextArea from 'antd/es/input/TextArea'
import { useEffect, useState } from 'react'
import { IKpiCategory, IKpiGroup } from '~/dto/Kpi.dto'

const { Title, Paragraph, Text } = Typography

interface DetailButtonProps {
  data: IKpiGroup
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal()

  useEffect(() => {}, [open])

  const modalContent = (
    <div>
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Row gutter={16}>
          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Tên Bộ KPI:</Text>
            <br />
            <Text>{data.groupName}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Phòng ban:</Text>
            <br />
            <Text>{data.department}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Tần suất đo:</Text>
            <br />
            <Text>{data.frequency}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Người tạo:</Text>
            <br />
            <Text>{data.createdBy}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Ngày tạo:</Text>
            <br />
            <Text>{data.createdDate}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Ngày cập nhật:</Text>
            <br />
            <Text>{data.updatedDate}</Text>
          </Col>

          <Col span={8} style={{ marginBottom: 16 }}>
            <Text strong>Trạng thái:</Text>
            <br />
            <Tag color={data.status === 'Hoạt động' ? 'green' : 'red'}>{data.status}</Tag>
          </Col>
        </Row>
      </Card>
    </div>
  )

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />
      <BaseModal
        open={open}
        title='Chi tiết Bộ KPI'
        description='Thông tin chi tiết Bộ KPI'
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  )
}

export default DetailButton
