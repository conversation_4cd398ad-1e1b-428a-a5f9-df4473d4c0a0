import {
  EyeOutlined,
  AppstoreOutlined,
  DollarOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  CopyOutlined,
  TagOutlined,
  StarOutlined,
  PictureOutlined,
  PlusOutlined,
  EditOutlined
} from '@ant-design/icons'
import {
  Card,
  Descriptions,
  Tag,
  Typography,
  Row,
  Col,
  Space,
  Statistic,
  message,
  Image,
  Empty,
  Form,
  StepProps,
  Steps,
  DatePicker,
  Input,
  Radio,
  Select,
  Divider,
  Button,
  Tooltip
} from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import { useModal } from '../../../../../hooks/useModal'
import BaseModal from '~/components/BaseModal'
import { formatDateCustom, formatMoneyVND } from '~/common/helper/helper'
import { IProduct } from '~/dto/product.dto'
import { EProduct, NSProduct } from '~/common/enums/NSProduct'
import { useDetailProduct } from '~/hooks/product/useDetailProduct'
import { ICustomerContact } from '~/dto/customer_contact.dto'
import TextArea from 'antd/es/input/TextArea'
import { useEffect, useState } from 'react'
import { IKpiCategory, IKpiEvaluate } from '~/dto/Kpi.dto'
import { IEmployee } from '~/dto/employee.dto'
import BaseTable from '~/components/BaseTable'
import { getColorPercent } from '~/common/utils/common.utils'
import { title } from 'process'
import { text } from 'stream/consumers'
import { ColumnsType } from 'antd/es/table'
import { SupportKPI } from '~/views/main/CustomerContact/sub-screen/report-kpi'

const { Title, Paragraph, Text } = Typography

interface DetailButtonProps {
  data: IKpiEvaluate
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal()
  let [current, setCurrent] = useState(0)
  const [isEdit, setIsEdit] = useState(false)

  useEffect(() => {}, [open])

  const allData: any[] = [
    {
      id: '1',
      employeeCode: 'NV0001',
      name: 'Nguyễn Thị Trang',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=1',
      status: 'Hoạt động',
      note: 'Ghi chú'
    },
    {
      id: '2',
      employeeCode: 'NV0002',
      name: 'Nguyễn Thị Dung',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=2',
      status: 'Hoạt động',
      note: 'Ghi chú'
    },
    {
      id: '3',
      employeeCode: 'NV0003',
      name: 'Nguyễn Thị Huyền',
      email: '<EMAIL>',
      phone: '0901234567',
      department: 'Phòng kinh doanh',
      position: 'Giám đốc',
      dateOfBirth: '1990-01-01',
      gender: 'Nam',
      address: '123 Lê Duẩn, Quận 1, TP.HCM',
      avatar: 'https://i.pravatar.cc/150?u=3',
      status: 'Hoạt động',
      note: 'Ghi chú'
    }
  ]
  //fakeData
  const [fakeData, setFakeData] = useState<IEmployee[]>()

  const saleColumns: any[] = [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_, __, index) => index + 1,
      fixed: 'left'
    },
    { title: 'Mã NV', dataIndex: 'key', key: 'key', align: 'center', width: 100, fixed: 'left' },
    {
      title: 'Nhân viên',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      width: 200,
      fixed: 'left'
    },
    {
      title: (
        <Tooltip title='Số lượng khách hàng mới mà nhân viên đã tạo trên hệ thống CRM (chưa từng có trước đó).'>
          <Text strong> Số KH mới</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'newCustomersTarget',
          key: 'newCustomersTarget',
          align: 'center',
          width: 150
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'newCustomersActual',
          key: 'newCustomersActual',
          align: 'center',
          width: 150
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'newCustomersRate',
          key: 'newCustomersRate',
          align: 'center',
          width: 150,
          render: (value: number) => <Tag color={getColorPercent(value)}> {value}%</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tổng số cuộc gọi điện, cuộc gặp, hoặc demo giới thiệu sản phẩm/dịch vụ đến khách hàng.'>
          <Text strong> Cuộc gọi/Gặp KH</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'callsTarget',
          key: 'callsTarget',
          align: 'center',
          width: 150
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'callsActual',
          key: 'callsActual',
          align: 'center',
          width: 150
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'callsRate',
          key: 'callsRate',
          align: 'center',
          width: 150,
          render: (value: number) => <Tag color={getColorPercent(value)}> {value}%</Tag>
        }
      ]
    },

    {
      title: (
        <Tooltip title='Số báo giá đã được gửi cho khách hàng (qua CRM, email,...), phản ánh hoạt động tiếp cận.'>
          <Text strong> Báo giá gửi đi</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'quotesTarget',
          key: 'quotesTarget',
          align: 'center',
          width: 150
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'quotesActual',
          key: 'quotesActual',
          align: 'center',
          width: 150
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'quotesRate',
          key: 'quotesRate',
          align: 'center',
          width: 150,
          render: (value: number) => <Tag color={getColorPercent(value)}> {value}%</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tổng giá trị tiền từ các hợp đồng đã được ký kết với khách hàng trong kỳ báo cáo.'>
          <Text strong> Doanh số hợp đồng</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'ordersTarget',
          key: 'ordersTarget',
          align: 'center',
          width: 150
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'ordersActual',
          key: 'ordersActual',
          align: 'center',
          width: 150
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'ordersRate',
          key: 'ordersRate',
          align: 'center',
          width: 150,
          render: (value: number) => <Tag color={getColorPercent(value)}> {value}%</Tag>
        }
      ]
    },

    {
      title: (
        <Tooltip title='Tỷ lệ số cơ hội bán hàng đã chuyển đổi thành hợp đồng chính thức.'>
          <Text strong> Tỷ lệ chốt đơn</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'closingRateTarget',
          key: 'closingRateTarget',
          align: 'center',
          width: 150
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'closingRateActual',
          key: 'closingRateActual',
          align: 'center',
          width: 150
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'closingRateRate',
          key: 'closingRateRate',
          align: 'center',
          width: 150,
          render: (value: number) => <Tag color={getColorPercent(value)}> {value}%</Tag>
        }
      ]
    },

    {
      title: (
        <Tooltip title='Tỷ lệ cơ hội, khách hàng hoặc deal được cập nhật đầy đủ trạng thái, thông tin, ghi chú,... trên CRM.'>
          <Text strong> Tỷ lệ cập nhật CRM</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'crmUpdateTarget',
          key: 'crmUpdateTarget',
          align: 'center',
          width: 150
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'crmUpdateActual',
          key: 'crmUpdateActual',
          align: 'center',
          width: 150
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'crmUpdateRate',
          key: 'crmUpdateRate',
          align: 'center',
          width: 150,
          render: (value: number) => <Tag color={getColorPercent(value)}> {value}%</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tổng điểm KPI = ∑ (Tỷ lệ hoàn thành KPI × Trọng số) / 100'>
          <Text strong> Tổng điểm KPI</Text>
        </Tooltip>
      ),
      align: 'center',
      dataIndex: 'totalKpi',
      key: 'totalKpi',
      width: 150,
      render: (value: number) => <Tag color={getColorPercent(value)}> {value}</Tag>
    }
  ]

  const marketingColumns: any[] = [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      fixed: 'left',
      align: 'center',
      render: (_: any, __: any, index: number) => index + 1
    },
    { title: 'Mã NV', dataIndex: 'code', key: 'code', align: 'center', width: 100, fixed: 'left' },
    {
      title: 'Nhân viên',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      width: 200,
      fixed: 'left'
    },
    {
      title: (
        <Tooltip title='Số lượng chiến dịch marketing đã được lên kế hoạch và thực hiện (email, quảng cáo, sự kiện,...).'>
          <Text strong> Chiến dịch triển khai</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'campaignsTarget',
          key: 'campaignsTarget',
          align: 'center',
          width: 100
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'campaignsActual',
          key: 'campaignsActual',
          align: 'center',
          width: 100
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'campaignsRate',
          key: 'campaignsRate',
          align: 'center',
          width: 100,
          render: (v: number) => <Tag color={getColorPercent(v)}>{v}%</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tổng số người nhìn thấy hoặc tương tác với chiến dịch (view, impression, reach,...).'>
          <Text strong> Lượt tiếp cận</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'reachTarget',
          key: 'reachTarget',
          align: 'center',
          width: 100
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'reachActual',
          key: 'reachActual',
          align: 'center',
          width: 100
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'reachRate',
          key: 'reachRate',
          align: 'center',
          width: 100,
          render: (v: number) => <Tag color={getColorPercent(v)}>{v}%</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tỷ lệ người tiếp cận chuyển đổi thành hành động cụ thể (đăng ký, mua hàng, điền form,...).'>
          <Text strong> Tỷ lệ chuyển đổi</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'conversionRateTarget',
          key: 'conversionRateTarget',
          align: 'center',
          width: 100
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'conversionRateActual',
          key: 'conversionRateActual',
          align: 'center',
          width: 100
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'conversionRateRate',
          key: 'conversionRateRate',
          align: 'center',
          width: 100,
          render: (v: number) => <Tag color={getColorPercent(v)}>{v}%</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tổng chi phí marketing chia cho số lượng lead thu về (đánh giá hiệu quả ngân sách).'>
          <Text strong> Chi phí/lead</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'costPerLeadTarget',
          key: 'costPerLeadTarget',
          align: 'center',
          width: 100
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'costPerLeadActual',
          key: 'costPerLeadActual',
          align: 'center',
          width: 100
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'costPerLeadRate',
          key: 'costPerLeadRate',
          align: 'center',
          width: 100,
          render: (v: number) => <Tag color={getColorPercent(v)}>{v}%</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Số lượng khách hàng tiềm năng (lead) mà marketing chuyển giao cho sale (qualified lead).'>
          <Text strong> Số lead MKT</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'leadsTarget',
          key: 'leadsTarget',
          align: 'center',
          width: 100
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'leadsActual',
          key: 'leadsActual',
          align: 'center',
          width: 100
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'leadsRate',
          key: 'leadsRate',
          align: 'center',
          width: 100,
          render: (v: number) => <Tag color={getColorPercent(v)}>{v}%</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tổng điểm KPI = ∑ (Tỷ lệ hoàn thành × Trọng số) / 100'>
          <Text strong> Tổng điểm KPI</Text>
        </Tooltip>
      ),
      align: 'center',
      dataIndex: 'totalKpi',
      key: 'totalKpi',
      width: 120,
      render: (v: number) => <Tag color={getColorPercent(v)}>{v}%</Tag>
    }
  ]

  const supportColumns: ColumnsType<SupportKPI> = [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      fixed: 'left',
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: 'Mã Nhân viên',
      dataIndex: 'code',
      fixed: 'left',
      align: 'center',
      width: 120
    },
    {
      title: 'Nhân viên',
      dataIndex: 'name',
      fixed: 'left',
      align: 'center',
      width: 150
    },
    {
      title: (
        <Tooltip title='Số lượng yêu cầu, khiếu nại, hoặc phản hồi từ khách hàng đã được xử lý thành công.'>
          <Text strong>Ticket xử lý - Số lượng</Text>
        </Tooltip>
      ),
      children: [
        { title: 'Chỉ tiêu', dataIndex: 'ticketTarget', width: 70, align: 'center' },
        { title: 'Kết quả', dataIndex: 'ticketResult', width: 70, align: 'center' },
        {
          title: 'Tỷ lệ \n hoàn thành',
          dataIndex: 'ticketRate',
          width: 100,
          align: 'center',
          render: (value: number) => <Tag color={getColorPercent(value)}>{value}%</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Số lượng nhiệm vụ hoàn thành trong tháng'>
          <Text strong>Hoàn thành nhiệm vụ</Text>
        </Tooltip>
      ),
      children: [
        { title: 'Chỉ tiêu', dataIndex: 'responseTarget', width: 70, align: 'center' },
        { title: 'Kết quả', dataIndex: 'responseResult', width: 70, align: 'center' },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'responseRate',
          width: 100,
          align: 'center',
          render: (value: number) => <Tag color={getColorPercent(value)}>{value}%</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Điểm khảo sát sự hài lòng của khách hàng sau khi xử lý xong ticket (thường từ 1–5 hoặc %).'>
          <Text strong>Hài lòng KH (CSAT) - Điểm</Text>
        </Tooltip>
      ),
      children: [
        { title: 'Chỉ tiêu', dataIndex: 'csatTarget', width: 70, align: 'center' },
        { title: 'Kết quả', dataIndex: 'csatResult', width: 70, align: 'center' },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'csatRate',
          width: 100,
          align: 'center',
          render: (value: number) => <Tag color={getColorPercent(value)}>{value}%</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Ticket được xử lý sau thời hạn cam kết (SLA), phản ánh sự chậm trễ.'>
          <Text strong>Hoàn thành ticket quá hạn</Text>
        </Tooltip>
      ),
      children: [
        { title: 'Chỉ tiêu', dataIndex: 'overdueTarget', width: 70, align: 'center' },
        { title: 'Kết quả', dataIndex: 'overdueResult', width: 70, align: 'center' },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'overdueRate',
          width: 100,
          align: 'center',
          render: (value: number) => <Tag color={getColorPercent(value, false)}>{value}%</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Ticket được cập nhật trạng thái kịp thời trên hệ thống (ví dụ: Đang xử lý → Hoàn tất).'>
          <Text strong>Cập nhật trạng thái ticket đúng hạn</Text>
        </Tooltip>
      ),
      children: [
        { title: 'Chỉ tiêu', dataIndex: 'updateTarget', width: 70, align: 'center' },
        { title: 'Kết quả', dataIndex: 'updateResult', width: 70, align: 'center' },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'updateRate',
          width: 100,
          align: 'center',
          render: (value: number) => <Tag color={getColorPercent(value)}>{value}%</Tag>
        }
      ]
    }
  ]

  useEffect(() => {
    setFakeData(allData)
  }, [open])

  const handleAddNewEmployee = () => {
    setFakeData([
      ...fakeData,
      {
        id: null,
        employeeCode: null,
        name: null,
        email: null,
        phone: null,
        department: null,
        position: null,
        dateOfBirth: null,
        gender: null,
        address: null,
        avatar: null,
        status: null,
        note: null
      }
    ])
  }

  const modalContent = (
    <div>
      <Row style={{ marginTop: 16, marginBottom: 16, display: 'flex', justifyContent: 'flex-end' }}>
        <Button style={{ marginRight: 8, backgroundColor: 'green', color: 'white' }}>Duyệt</Button>
        <Button style={{ backgroundColor: 'red', color: 'white' }}>Từ chối</Button>
      </Row>
      <Row>
        <Col span={24}>
          <Card style={{ marginBottom: '16px' }} size='small' title='Thông tin phiếu đánh giá'>
            <Row gutter={16}>
              <Col span={6} style={{ marginBottom: 16 }}>
                <Text strong>Phòng ban:</Text>
                <br />
                <Text>{data.department}</Text>
              </Col>

              <Col span={6} style={{ marginBottom: 16 }}>
                <Text strong>Bộ KPI áp dụng:</Text>
                <br />
                <Text>{data.kpiGroup}</Text>
              </Col>

              <Col span={6} style={{ marginBottom: 16 }}>
                <Text strong>Tháng đánh giá:</Text>
                <br />
                <Text>{data.evaluateMonth}</Text>
              </Col>

              <Col span={6} style={{ marginBottom: 16 }}>
                <Text strong>Người duyệt:</Text>
                <br />
                <Text>{data.approvedBy}</Text>
              </Col>

              <Col span={6} style={{ marginBottom: 16 }}>
                <Text strong>Ngày duyệt:</Text>
                <br />
                <Text>{data.approvedDate}</Text>
              </Col>
            </Row>
          </Card>
        </Col>

        <Col span={8}>
          <Card
            title='Nhân viên áp dụng'
            size='small'
            extra={
              <Row>
                <Button
                  style={{ marginTop: 16, marginBottom: 16, marginRight: 8 }}
                  type='primary'
                  icon={<PlusOutlined />}
                  // onClick={handleAddNewEmployee}
                />
                <Button
                  style={{ marginTop: 16, marginBottom: 16 }}
                  type='primary'
                  icon={<EditOutlined />}
                  onClick={() => setIsEdit(!isEdit)}
                />
              </Row>
            }>
            {fakeData?.map((item) => (
              <div>
                [{item.employeeCode}] {item.name}
              </div>
            ))}
          </Card>
        </Col>

        <Col span={24}>
          <Card title='Đánh giá KPI' size='small'>
            <Row gutter={16}>
              {(() => {
                switch (data.department) {
                  case 'Kinh doanh':
                    return (
                      <BaseTable
                        data={data.departmentData}
                        total={0}
                        isLoading={false}
                        columns={saleColumns}
                        scroll={{ x: 3000 }}
                        pagination={false}
                      />
                    )
                  case 'Marketing':
                    return (
                      <BaseTable
                        data={data.departmentData}
                        total={0}
                        isLoading={false}
                        columns={marketingColumns}
                        scroll={{ x: 3000 }}
                        pagination={false}
                      />
                    )
                  case 'CSKH':
                    return (
                      <BaseTable
                        data={data.departmentData}
                        total={0}
                        isLoading={false}
                        columns={supportColumns}
                        scroll={{ x: 3000 }}
                        pagination={false}
                      />
                    )
                  default:
                    return (
                      <BaseTable
                        data={[]}
                        total={0}
                        isLoading={false}
                        columns={[]}
                        scroll={{ x: 3000 }}
                        pagination={false}
                      />
                    )
                }
              })()}
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  )

  const handleInputChange = (e: any, id: string) => {
    setFakeData(
      fakeData?.map((item) => {
        if (item.id === id) {
          item.name = e.target.value
        }
        return item
      })
    )
  }

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />
      <BaseModal
        open={open}
        title='Chi tiết Đánh giá KPI'
        description='Thông tin chi tiết Đánh giá KPI'
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  )
}

export default DetailButton
