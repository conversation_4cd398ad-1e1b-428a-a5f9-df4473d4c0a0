import {
  SaveOutlined,
  PlusOutlined,
  DeleteOutlined,
  UploadOutlined,
  EditOutlined
} from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Upload,
  message,
  Checkbox,
  Card
} from 'antd'
import { useForm } from 'antd/es/form/Form'
import { useState, useEffect, FC } from 'react'
import BaseText from '~/components/BaseText'
import { COLORS } from '~/common/constants'
import { useParams, useNavigate } from 'react-router-dom'
import { ISurveyFeedback } from '~/dto/survey-feedback.dto'
import BaseModal from '~/components/BaseModal'
import { BaseButton } from '~/components'
import { useModal } from '~/hooks/useModal'

const { TextArea } = Input

interface IProps {
  data: ISurveyFeedback
}

const QUESTION_TYPES = [
  { label: 'Trắc nghiệm', value: 'multiple_choice' },
  { label: 'Hộp kiểm', value: 'checkbox' },
  { label: 'T<PERSON><PERSON> lời ngắn', value: 'short_text' },
  { label: 'Tr<PERSON> lời đoạn', value: 'long_text' },
  { label: 'Đánh giá', value: 'rate' }
]

const EditSurveyFeedback: FC<IProps> = ({ data }) => {
  const { open, openModal, closeModal } = useModal()
  const [form] = useForm()
  const [fileList, setFileList] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const navigate = useNavigate()

  useEffect(() => {
    const fetchSurveyData = async () => {
      form.setFieldsValue(data)
      setFileList(data.images || [])
    }

    fetchSurveyData()
  }, [data, form])

  const handleSave = async (values: any) => {
    try {
      setLoading(true)
      message.success('Cập nhật khảo sát thành công')
      navigate('/survey-feedback')
    } catch (error) {
      message.error('Không thể cập nhật khảo sát')
    } finally {
      setLoading(false)
    }
  }

  const handleUploadChange = (res: any) => {
    if (res.Location) {
      setFileList((curr) => {
        return [
          ...curr,
          {
            uid: res.Location,
            name: res.Location,
            status: 'done',
            url: res.Location
          }
        ]
      })
    }
  }

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      message.error('Chỉ được upload file hình ảnh!')
      return false
    }
  }

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  )

  const modalContent = (
    <div>
      <Card>
        <Form
          form={form}
          layout='vertical'
          onFinish={handleSave}
          disabled={loading}>
          {/* Thông tin khảo sát */}
          <BaseText variant='h4' weight='bold'>
            Thông tin khảo sát
          </BaseText>
          <Row gutter={24}>
            <Col span={18}>
              <Form.Item
                name='surveyName'
                label='Tên khảo sát'
                rules={[{ required: true, message: 'Bắt buộc' }]}>
                <Input placeholder='Nhập tên khảo sát' />
              </Form.Item>
              <Form.Item
                name='title'
                label='Tiêu đề'
                rules={[{ required: true, message: 'Bắt buộc' }]}>
                <Input placeholder='Nhập tiêu đề' />
              </Form.Item>
              <Form.Item
                name='description'
                label='Mô tả'
                rules={[{ required: true, message: 'Bắt buộc' }]}>
                <TextArea rows={2} placeholder='Nhập mô tả' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='validDate' label='Valid Date'>
                <Input
                  addonAfter='Ngày'
                  type='number'
                  min={1}
                  defaultValue={5}
                />
              </Form.Item>
              <Form.Item
                label='Hình ảnh sản phẩm'
                name='images'
                style={{
                  display: 'flex',
                  justifyContent: 'center',
                  flexDirection: 'column'
                }}>
                <Upload
                  listType='picture-card'
                  fileList={fileList}
                  onChange={handleUploadChange}
                  beforeUpload={beforeUpload}
                  multiple
                  accept='image/*'
                  customRequest={async ({ file, onSuccess }) => {
                    try {
                      // handleUploadChange(response.data)
                      // onSuccess?.(response.data)
                    } catch (error) {
                      message.error('Không thể tải lên hình ảnh')
                    }
                  }}>
                  {fileList.length >= 8 ? null : uploadButton}
                </Upload>
              </Form.Item>
            </Col>
          </Row>

          {/* Danh sách phần */}
          <Form.List name='sections'>
            {(fields, { add, remove }) => (
              <>
                {fields.map((field, idx) => (
                  <div
                    key={field.key}
                    style={{
                      border: '1px solid #e0e0e0',
                      borderRadius: 6,
                      marginBottom: 24,
                      padding: 16,
                      background: '#fafbfc'
                    }}>
                    <Row align='middle' justify='space-between'>
                      <Col>
                        <BaseText variant='h5' weight='bold'>
                          Phần {idx + 1}
                        </BaseText>
                      </Col>
                      <Col>
                        <Button
                          danger
                          icon={<DeleteOutlined />}
                          onClick={() => remove(field.name)}>
                          Xóa phần
                        </Button>
                      </Col>
                    </Row>
                    <Row gutter={16} style={{ marginTop: 8 }}>
                      <Col span={12}>
                        <Form.Item name={[field.name, 'title']} label='Tiêu đề'>
                          <Input placeholder='Nhập tiêu đề phần' />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name={[field.name, 'description']}
                          label='Mô tả'>
                          <TextArea rows={1} placeholder='Nhập mô tả phần' />
                        </Form.Item>
                      </Col>
                    </Row>
                    {/* Danh sách câu hỏi */}
                    <Form.List name={[field.name, 'questions']}>
                      {(qFields, { add: addQ, remove: removeQ }) => (
                        <>
                          {qFields.map((qField, qIdx) => (
                            <div
                              key={qField.key}
                              style={{
                                border: '1px dashed #bfbfbf',
                                borderRadius: 4,
                                margin: '12px 0',
                                padding: 12,
                                background: '#fff'
                              }}>
                              <Row align='middle' justify='space-between'>
                                <Col>
                                  <BaseText variant='body1' weight='bold'>
                                    Câu hỏi {qIdx + 1}
                                  </BaseText>
                                </Col>
                                <Col>
                                  <Button
                                    danger
                                    icon={<DeleteOutlined />}
                                    onClick={() => removeQ(qField.name)}>
                                    Xóa câu hỏi
                                  </Button>
                                </Col>
                              </Row>
                              <Row gutter={16} style={{ marginTop: 8 }}>
                                <Col span={12}>
                                  <Form.Item
                                    name={[qField.name, 'question']}
                                    label='Câu hỏi'
                                    rules={[
                                      { required: true, message: 'Bắt buộc' }
                                    ]}>
                                    <Input placeholder='Nhập câu hỏi' />
                                  </Form.Item>
                                </Col>
                                <Col span={4}>
                                  <Form.Item
                                    name={[qField.name, 'score']}
                                    label='Điểm'>
                                    <InputNumber
                                      min={0}
                                      placeholder='Điểm'
                                      style={{ width: '100%' }}
                                    />
                                  </Form.Item>
                                </Col>
                                <Col span={8}>
                                  <Form.Item
                                    name={[qField.name, 'type']}
                                    label='Loại câu hỏi'
                                    rules={[
                                      { required: true, message: 'Bắt buộc' }
                                    ]}>
                                    <Select
                                      options={QUESTION_TYPES}
                                      placeholder='Chọn loại'
                                    />
                                  </Form.Item>
                                </Col>
                              </Row>
                              <Row>
                                <Col>
                                  <Form.Item
                                    name={[qField.name, 'isRequired']}
                                    valuePropName='checked'
                                    initialValue={true}>
                                    <Checkbox>Bắt buộc trả lời</Checkbox>
                                  </Form.Item>
                                </Col>
                              </Row>
                            </div>
                          ))}
                          <Button
                            type='primary'
                            block
                            icon={<PlusOutlined />}
                            onClick={() => addQ()}>
                            Thêm câu hỏi
                          </Button>
                        </>
                      )}
                    </Form.List>
                  </div>
                ))}
                <Button
                  type='default'
                  style={{
                    backgroundColor: COLORS.PRIMARY,
                    color: COLORS.WHITE
                  }}
                  block
                  icon={<PlusOutlined />}
                  onClick={() => add()}>
                  Thêm phần
                </Button>
              </>
            )}
          </Form.List>

          {/* Phần kết thúc */}
          <div
            style={{
              border: '1px solid #e0e0e0',
              borderRadius: 6,
              marginTop: 18,
              marginBottom: 24,
              padding: 16,
              background: '#fafbfc'
            }}>
            <BaseText variant='h5' weight='bold'>
              Phần kết thúc
            </BaseText>
            <Form.Item
              name={['endSection', 'description']}
              label='Mô tả'
              rules={[{ required: true, message: 'Bắt buộc' }]}>
              <TextArea rows={2} placeholder='Vui lòng nhập...' />
            </Form.Item>
          </div>

          <Row justify='end'>
            <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
              Lưu khảo sát
            </Button>
          </Row>
        </Form>
      </Card>
    </div>
  )

  return (
    <>
      <BaseButton
        type='primary'
        shape='circle'
        icon={<EditOutlined />}
        tooltip='Edit'
        onClick={openModal}
      />
      <BaseModal
        open={open}
        title='Chỉnh sửa khảo sát'
        description='Thông tin chỉnh sửa khảo sát'
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  )
}

export default EditSurveyFeedback
