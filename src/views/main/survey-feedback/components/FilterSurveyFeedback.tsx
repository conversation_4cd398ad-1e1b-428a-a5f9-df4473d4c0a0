import { FC } from 'react'
import { Form, Input, Select, Button, Row, Col, Collapse, Space, DatePicker } from 'antd'
import { IFilterSurveyFeedback, ESurveyFeedbackStatus } from '~/dto/survey-feedback.dto'
import BaseButton from '~/components/BaseButton'
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons'

interface IProps {
  onFilter: (values: IFilterSurveyFeedback) => void
  onReset: () => void
  isLoading?: boolean
}

const FilterSurveyFeedback: FC<IProps> = ({ onFilter, onReset, isLoading }) => {
  const [form] = Form.useForm()

  const handleSubmit = (values: IFilterSurveyFeedback) => {
    onFilter({
      ...values,
      pageIndex: 1,
      pageSize: 10
    })
  }

  const handleReset = () => {
    form.resetFields()
    onReset()
  }

  return (
    <Collapse>
      <Collapse.Panel header='Tìm kiếm' key='0'>
        <Form form={form} onFinish={handleSubmit} layout='vertical' className='filter-form'>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item name='surveyName' label='ID khảo sát' rules={[{ required: false }]}>
                <Input placeholder='Nhập ID khảo sát' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='createdBy' label='Người tạo' rules={[{ required: false }]}>
                <Input placeholder='Nhập tên người tạo' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='status' label='Trạng thái' rules={[{ required: false }]}>
                <Select
                  placeholder='Chọn trạng thái'
                  allowClear
                  options={Object.values(ESurveyFeedbackStatus).map((status) => ({
                    label: status,
                    value: status
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              {/* Từ ngày đến ngày */}
              <Form.Item name='fromDateToDate' label='Từ ngày - đến ngày'>
                <DatePicker.RangePicker style={{ width: '100%' }} allowClear placeholder={['Từ ngày', 'Đến ngày']} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24} style={{ textAlign: 'center' }}>
              <Space>
                <Button
                  type='primary'
                  htmlType='submit'
                  onClick={() => handleSubmit(form.getFieldsValue())}
                  loading={isLoading}>
                  <SearchOutlined />
                  Tìm kiếm
                </Button>
                <Button type='default' htmlType='submit' onClick={() => handleReset()}>
                  <ReloadOutlined />
                  Làm mới
                </Button>
              </Space>
            </Col>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  )
}

export default FilterSurveyFeedback
