import { FC, useState } from 'react'
import {
  EyeOutlined,
  AppstoreOutlined,
  UserOutlined,
  ClockCircleOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons'
import { Card, Descriptions, Tag, Row, Col, Space, Steps, Image } from 'antd'
import type { StepsProps } from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import { useModal } from '../../../../hooks/useModal'
import BaseModal from '~/components/BaseModal'
import { formatDateCustom } from '~/common/helper/helper'
import {
  ISurveyFeedback,
  SurveyFeedbackStatusConfig
} from '~/dto/survey-feedback.dto'

interface IProps {
  data: ISurveyFeedback
}

const DetailButton: FC<IProps> = ({ data }) => {
  const { open, openModal, closeModal } = useModal()
  const { id, surveyName, createdAt, createdBy, status } = data
  const [currentStep, setCurrentStep] = useState(0)

  // Mock data for demonstration - replace with actual data structure
  const surveyQuestions = [
    {
      id: 1,
      question:
        '<PERSON><PERSON><PERSON> đ<PERSON>h giá trải nghiệm sử dụng sản phẩm của chúng tôi như thế nào?',
      image:
        'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg',
      answer: 'Rất hài lòng',
      type: 'rating'
    },
    {
      id: 2,
      question: 'Bạn thích nhất tính năng nào của sản phẩm?',
      image:
        'https://images.pexels.com/photos/3184292/pexels-photo-3184292.jpeg',
      answer: 'Giao diện thân thiện, dễ sử dụng',
      type: 'text'
    }
    // Add more questions as needed
  ]

  const steps: StepsProps['items'] = surveyQuestions.map((q, index) => ({
    title: `Câu hỏi ${index + 1}`,
    description: q.question,
    status:
      currentStep === index
        ? 'process'
        : currentStep > index
        ? 'finish'
        : 'wait'
  }))

  const renderQuestionContent = (question: (typeof surveyQuestions)[0]) => (
    <Card style={{ marginBottom: '16px' }}>
      <Row gutter={[16, 16]}>
        {question.image && (
          <Col span={24}>
            <Image
              src={question.image}
              alt={question.question}
              style={{ maxHeight: 200, objectFit: 'contain' }}
            />
          </Col>
        )}
        <Col span={24}>
          <BaseText size='lg' weight='bold'>
            {question.question}
          </BaseText>
        </Col>
        <Col span={24}>
          <Card type='inner' style={{ backgroundColor: '#f5f5f5' }}>
            <Space>
              <CheckCircleOutlined style={{ color: '#52c41a' }} />
              <BaseText>{question.answer}</BaseText>
            </Space>
          </Card>
        </Col>
      </Row>
    </Card>
  )

  const modalContent = (
    <div>
      {/* Survey Overview Card */}
      <Card
        title={
          <Space>
            <AppstoreOutlined style={{ color: '#1890ff' }} />
            <BaseText>Thông tin khảo sát</BaseText>
          </Space>
        }
        style={{ marginBottom: '16px' }}
        size='small'>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={16}>
            <div>
              <BaseText size='xl' weight='bold'>
                {surveyName}
              </BaseText>
              <Tag
                color={SurveyFeedbackStatusConfig[status]?.color}
                style={{ marginLeft: 8 }}>
                {SurveyFeedbackStatusConfig[status]?.label}
              </Tag>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <Descriptions size='small' column={1}>
              <Descriptions.Item label='ID mẫu khảo sát'>
                {id}
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>

      {/* Survey Progress Steps */}
      <Card style={{ marginBottom: '16px' }}>
        <Steps
          current={currentStep}
          onChange={setCurrentStep}
          items={steps}
          direction='vertical'
          size='small'
        />
      </Card>

      {/* Current Question Content */}
      {renderQuestionContent(surveyQuestions[currentStep])}

      {/* Creator Information */}
      <Card
        title={
          <Space>
            <UserOutlined style={{ color: '#722ed1' }} />
            <BaseText>Thông tin người tạo</BaseText>
          </Space>
        }
        size='small'
        style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Descriptions size='small' column={1}>
              <Descriptions.Item label='Người tạo'>
                {createdBy}
              </Descriptions.Item>
              <Descriptions.Item label='Thời gian tạo'>
                {formatDateCustom(createdAt, 'DD/MM/YYYY HH:mm')}
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>
    </div>
  )

  return (
    <>
      <BaseButton
        type='primary'
        shape='circle'
        icon={<EyeOutlined />}
        tooltip='Xem chi tiết'
        onClick={openModal}
      />
      <BaseModal
        open={open}
        title='Chi tiết khảo sát'
        description='Thông tin chi tiết khảo sát'
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  )
}

export default DetailButton
