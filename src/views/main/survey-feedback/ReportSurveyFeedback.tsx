import { FC, useState } from 'react'
import { useTranslation } from 'react-i18next'
import BaseTable from '~/components/BaseTable'
import { useListSurveyFeedback } from '~/hooks/survey/useListSurveyFeedback'
import { ISurveyFeedback } from '~/dto/survey-feedback.dto'
import dayjs from 'dayjs'
import BaseText from '~/components/BaseText'
import {
  IReportSurveyFeedback,
  useReportSurveyDashbroad
} from '~/hooks/report/useReportSurveyDashbroad'
import { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import { Button, Col, Collapse, DatePicker, Form, Input, Row, Select, Space } from 'antd'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons'

const { Option } = Select

const ReportSurveyFeedback: FC = () => {
  const { t } = useTranslation()
  const [pageIndex, setPageIndex] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const { data, isLoading } = useReportSurveyDashbroad()

  const columns: ColumnsType<IReportSurveyFeedback> = [
    {
      title: t('survey_feedback:report_survey_feedback.columns.stt'),
      dataIndex: 'index',
      key: 'index',
      width: 60,
      align: 'center',
      render: (_: any, __: any, index: number) => index + 1 + (pageIndex - 1) * pageSize
    },
    {
      title: t('survey_feedback:report_survey_feedback.columns.id'),
      dataIndex: 'id',
      key: 'id',
      width: 120,
      align: 'center'
    },
    {
      title: t('survey_feedback:report_survey_feedback.columns.surveyName'),
      dataIndex: 'surveyName',
      key: 'surveyName',
      width: 200,
      align: 'center'
    },
    {
      title: t('survey_feedback:report_survey_feedback.columns.trackingId'),
      dataIndex: 'trackingId',
      key: 'trackingId',
      width: 120,
      align: 'center'
    },
    {
      title: t('survey_feedback:report_survey_feedback.columns.customerName'),
      dataIndex: 'customerName',
      key: 'customerName',
      width: 200,
      align: 'center'
    },
    {
      title: t('survey_feedback:report_survey_feedback.columns.customerCode'),
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 120,
      align: 'center'
    },
    {
      title: t('survey_feedback:report_survey_feedback.columns.createdBy'),
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 150,
      align: 'center'
    },
    {
      title: t('survey_feedback:report_survey_feedback.columns.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      align: 'center',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY HH:mm')
    },
    {
      title: t('survey_feedback:report_survey_feedback.columns.totalScore'),
      dataIndex: 'totalScore',
      key: 'totalScore',
      width: 100,
      align: 'center',
      render: (score: number) => score?.toFixed(1) || '-'
    },
    {
      title: t('survey_feedback:report_survey_feedback.columns.percentage'),
      dataIndex: 'percentage',
      key: 'percentage',
      width: 100,
      align: 'center',
      render: (percentage: number) => (
        <BaseText color={percentage >= 80 ? 'success' : percentage >= 60 ? 'warning' : 'error'}>
          {percentage?.toFixed(1)}%
        </BaseText>
      )
    }
  ]

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setPageIndex(newPageIndex)
    setPageSize(newPageSize)
  }

  return (
    <BaseView>
      <Collapse style={{ marginBottom: 16 }}>
        <Collapse.Panel header='Tìm kiếm' key='0'>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item name='surveyName' label='ID khảo sát' rules={[{ required: false }]}>
                <Input placeholder='Nhập ID khảo sát' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='createdBy' label='Người tạo' rules={[{ required: false }]}>
                <Input placeholder='Nhập tên người tạo' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='status' label='Trạng thái' rules={[{ required: false }]}>
                <Select placeholder='Chọn trạng thái' allowClear>
                  <Option value='DRAFT'>Nháp</Option>
                  <Option value='PUBLISHED'>Đã đăng</Option>
                  <Option value='CLOSED'>Đã đóng</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              {/* Phần trăm */}
              <Form.Item name='percentage' label='Phần trăm'>
                <Select placeholder='Chọn phần trăm' allowClear>
                  <Option value=''>--Chọn phần trăm--</Option>
                  <Option value='1'>Từ 0% đến 50%</Option>
                  <Option value='2'>Từ 50% đến 100%</Option>
                  <Option value='3'>Trên 100%</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={6}>
              {/* Chọn nhân viên phản hồi */}
              <Form.Item name='respondent' label='NV phản hồi' rules={[{ required: false }]}>
                <Select placeholder='Chọn nhân viên phản hồi' allowClear>
                  <Option value=''>--Chọn nhân viên phản hồi--</Option>
                  <Option value='1'>Nguyễn Văn A</Option>
                  <Option value='2'>Trần Thị B</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              {/* Chọn khách hàng */}
              <Form.Item name='customer' label='Khách hàng' rules={[{ required: false }]}>
                <Select placeholder='Chọn khách hàng' allowClear>
                  <Option value=''>--Chọn khách hàng--</Option>
                  <Option value='1'>Công ty TNHH ABC</Option>
                  <Option value='2'>Công ty TNHH XYZ</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              {/* Thời gian phản hồi */}
              <Form.Item name='respondentTime' label='Thời gian phản hồi'>
                <Select placeholder='Chọn thời gian phản hồi' allowClear>
                  <Option value=''>--Chọn thời gian phản hồi--</Option>
                  <Option value='1'>Từ 00:00 đến 06:00</Option>
                  <Option value='2'>Từ 06:00 đến 12:00</Option>
                  <Option value='3'>Từ 12:00 đến 18:00</Option>
                  <Option value='4'>Từ 18:00 đến 24:00</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24} style={{ textAlign: 'center' }}>
              <Space>
                <Button type='primary' htmlType='submit' onClick={() => {}} loading={isLoading}>
                  <SearchOutlined />
                  Tìm kiếm
                </Button>
                <Button type='default' htmlType='submit' onClick={() => {}}>
                  <ReloadOutlined />
                  Làm mới
                </Button>
              </Space>
            </Col>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <BaseTable<IReportSurveyFeedback>
        columns={columns}
        data={data?.data || []}
        total={data?.total || 0}
        isLoading={isLoading}
        onPageChange={handlePageChange}
        defaultPageSize={pageSize}
        scroll={{ x: 'max-content' }}
      />
    </BaseView>
  )
}

export default ReportSurveyFeedback
