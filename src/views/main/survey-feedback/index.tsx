import { useState, FC } from 'react'
import { Col, Row, Tag } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { useTranslation } from 'react-i18next'
import BaseView from '~/components/BaseView'
import { DeleteOutlined } from '@ant-design/icons'
import { toastService } from '~/services'
import BaseButton from '~/components/BaseButton'
import DetailButton from './components/DetailButton'
import BaseTable from '~/components/BaseTable'
import {
  ISurveyFeedback,
  IFilterSurveyFeedback,
  SurveyFeedbackStatusConfig
} from '~/dto/survey-feedback.dto'
import FilterSurveyFeedback from './components/FilterSurveyFeedback'
import { useListSurveyFeedback } from '~/hooks/survey/useListSurveyFeedback'
import dayjs from 'dayjs'
import EditSurveyFeedback from './components/EditSurveyFeedback'

type IProps = {}

export const SurveyFeedbackView: FC<IProps> = () => {
  const { t } = useTranslation()
  const [filter, setFilter] = useState<IFilterSurveyFeedback>({
    surveyName: '',
    createdBy: '',
    status: '',
    pageIndex: 1,
    pageSize: 10
  })

  // TODO: Replace with actual API hook
  const { data, total, isLoading } = useListSurveyFeedback({})

  const handleFilter = (values: IFilterSurveyFeedback) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFilter({
      surveyName: '',
      createdBy: '',
      status: '',
      pageIndex: 1,
      pageSize: 10
    })
  }

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const handleDelete = async (item: ISurveyFeedback) => {
    try {
      // TODO: Implement delete functionality
      console.log('Delete item:', item)
    } catch (error) {
      toastService.handleError(error)
    }
  }

  const columns: ColumnsType<ISurveyFeedback> = [
    {
      title: t('survey_feedback:survey_feedback.columns.stt'),
      key: 'index',
      width: 80,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      title: t('survey_feedback:survey_feedback.columns.id'),
      dataIndex: 'id',
      key: 'id',
      width: 150,
      align: 'center'
    },
    {
      title: t('survey_feedback:survey_feedback.columns.surveyName'),
      dataIndex: 'surveyName',
      key: 'surveyName',
      align: 'center'
    },
    {
      title: t('survey_feedback:survey_feedback.columns.createdAt'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      align: 'center',
      render: (createdAt: string) => {
        return <span>{dayjs(createdAt).format('DD/MM/YYYY')}</span>
      }
    },
    {
      title: t('survey_feedback:survey_feedback.columns.createdBy'),
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 150,
      align: 'center'
    },
    {
      title: t('survey_feedback:survey_feedback.columns.status'),
      dataIndex: 'status',
      key: 'status',
      width: 120,
      align: 'center',
      render: (status: string) => {
        const statusConfig = SurveyFeedbackStatusConfig[status]
        return (
          <Tag
            color={statusConfig?.color}
            style={{
              fontSize: '14px',
              padding: '4px 12px',
              width: '100%',
              textAlign: 'center'
            }}>
            {statusConfig?.label || status}
          </Tag>
        )
      }
    },
    {
      title: t('survey_feedback:survey_feedback.columns.action'),
      key: 'action',
      width: 150,
      align: 'center',
      fixed: 'right',
      render: (_, record: ISurveyFeedback) => {
        return (
          <>
            <DetailButton data={record} />
            <EditSurveyFeedback data={record} />
            <BaseButton
              danger
              type='primary'
              shape='circle'
              icon={<DeleteOutlined />}
              tooltip='Delete'
              onClick={() => handleDelete(record)}
            />
          </>
        )
      }
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24}>
          <FilterSurveyFeedback
            onFilter={handleFilter}
            onReset={handleReset}
            isLoading={isLoading}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data.data}
            total={total}
            isLoading={isLoading}
            onPageChange={handlePageChange}
          />
        </Col>
      </Row>
    </BaseView>
  )
}
