import { FC } from 'react'
import {
  EyeOutlined,
  AppstoreOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
  TeamOutlined,
  TagOutlined,
  StarOutlined,
  PictureOutlined,
  EnvironmentOutlined,
  UserOutlined,
  ClockCircleOutlined,
  BellOutlined
} from '@ant-design/icons'
import {
  Card,
  Descriptions,
  Tag,
  Typography,
  Row,
  Col,
  Space,
  Statistic,
  Image,
  Empty
} from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseText from '~/components/BaseText'
import { useModal } from '../../../../hooks/useModal'
import BaseModal from '~/components/BaseModal'
import { formatDateCustom } from '~/common/helper/helper'
import {
  IComplaint,
  EComplaintType,
  EComplaintStatus,
  EComplaintPriority,
  ENotificationStatus,
  ComplaintTypeLabels,
  ComplaintStatusLabels,
  ComplaintPriorityLabels,
  NotificationStatusLabels,
  ComplaintStatusColors,
  ComplaintPriorityColors
} from '~/dto/complaint.dto'

const { Title, Paragraph } = Typography

interface IProps {
  data: IComplaint
}

const DetailButton: FC<IProps> = ({ data }) => {
  const { open, openModal, closeModal } = useModal()
  const {
    title,
    description,
    type,
    status,
    priority,
    complaintAddress,
    customerCode,
    sapCode,
    customer,
    address,
    supervisor,
    assignedStaff,
    startDate,
    dueDate,
    endDate,
    checkinTime,
    checkoutTime,
    notificationStatus,
    media,
    notes,
    solution,
    rating,
    feedback
  } = data

  const modalContent = (
    <div>
      {/* Complaint Overview Card */}
      <Card
        title={
          <Space>
            <AppstoreOutlined style={{ color: '#1890ff' }} />
            <BaseText>Thông tin khiếu nại</BaseText>
          </Space>
        }
        style={{ marginBottom: '16px' }}
        size='small'>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={16}>
            <div>
              <Space align='start'>
                <Title level={3} style={{ margin: 0 }}>
                  {title}
                </Title>
              </Space>
              <BaseText
                color='textSecondary'
                size='lg'
                style={{ marginTop: '8px', display: 'block' }}>
                {description}
              </BaseText>
            </div>
          </Col>
          <Col xs={24} sm={8}>
            <Space direction='vertical' size='middle' style={{ width: '100%' }}>
              <div>
                <BaseText color='textSecondary'>Trạng thái:</BaseText>
                <br />
                <Tag
                  color={ComplaintStatusColors[status]}
                  style={{ fontSize: '14px', padding: '4px 12px' }}>
                  {ComplaintStatusLabels[status]}
                </Tag>
              </div>
              <div>
                <BaseText color='textSecondary'>Loại khiếu nại:</BaseText>
                <br />
                <Tag
                  color='blue'
                  style={{ fontSize: '14px', padding: '4px 12px' }}>
                  {ComplaintTypeLabels[type]}
                </Tag>
              </div>
              <div>
                <BaseText color='textSecondary'>Mức độ:</BaseText>
                <br />
                <Tag
                  color={ComplaintPriorityColors[priority]}
                  style={{ fontSize: '14px', padding: '4px 12px' }}>
                  {ComplaintPriorityLabels[priority]}
                </Tag>
              </div>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* Customer Information */}
      <Card
        title={
          <Space>
            <UserOutlined style={{ color: '#722ed1' }} />
            <BaseText>Thông tin khách hàng</BaseText>
          </Space>
        }
        size='small'
        style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Descriptions size='small' column={1}>
              <Descriptions.Item label='Mã khách hàng'>
                {customerCode}
              </Descriptions.Item>
              <Descriptions.Item label='Mã SAP'>{sapCode}</Descriptions.Item>
              <Descriptions.Item label='Tên khách hàng'>
                {customer}
              </Descriptions.Item>
            </Descriptions>
          </Col>
          <Col xs={24} sm={12}>
            <Descriptions size='small' column={1}>
              <Descriptions.Item label='Địa chỉ khiếu nại'>
                {complaintAddress}
              </Descriptions.Item>
              <Descriptions.Item label='Địa chỉ'>{address}</Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>

      {/* Staff Information */}
      <Card
        title={
          <Space>
            <TeamOutlined style={{ color: '#fa8c16' }} />
            <BaseText>Thông tin nhân viên</BaseText>
          </Space>
        }
        size='small'
        style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Descriptions size='small' column={1}>
              <Descriptions.Item label='NV theo dõi/giám sát'>
                {supervisor}
              </Descriptions.Item>
            </Descriptions>
          </Col>
          <Col xs={24} sm={12}>
            <Descriptions size='small' column={1}>
              <Descriptions.Item label='NV được phân công'>
                {assignedStaff}
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>

      {/* Timeline Information */}
      <Card
        title={
          <Space>
            <ClockCircleOutlined style={{ color: '#13c2c2' }} />
            <BaseText>Thông tin thời gian</BaseText>
          </Space>
        }
        size='small'
        style={{ marginBottom: '16px' }}>
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={8}>
            <Descriptions size='small' column={1}>
              <Descriptions.Item label='Ngày bắt đầu'>
                {formatDateCustom(startDate, 'DD/MM/YYYY')}
              </Descriptions.Item>
              <Descriptions.Item label='Thời gian checkin'>
                {checkinTime}
              </Descriptions.Item>
            </Descriptions>
          </Col>
          <Col xs={24} sm={8}>
            <Descriptions size='small' column={1}>
              <Descriptions.Item label='Ngày hết hạn'>
                {formatDateCustom(dueDate, 'DD/MM/YYYY')}
              </Descriptions.Item>
              <Descriptions.Item label='Thời gian checkout'>
                {checkoutTime}
              </Descriptions.Item>
            </Descriptions>
          </Col>
          <Col xs={24} sm={8}>
            <Descriptions size='small' column={1}>
              <Descriptions.Item label='Ngày kết thúc'>
                {endDate
                  ? formatDateCustom(endDate, 'DD/MM/YYYY')
                  : 'Chưa kết thúc'}
              </Descriptions.Item>
              <Descriptions.Item label='Trạng thái gửi tin'>
                <Tag color='blue'>
                  {NotificationStatusLabels[notificationStatus]}
                </Tag>
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>

      {/* Media Gallery */}
      {media && media.length > 0 && (
        <Card
          title={
            <Space>
              <PictureOutlined style={{ color: '#eb2f96' }} />
              <BaseText>Hình ảnh đính kèm</BaseText>
            </Space>
          }
          size='small'
          style={{ marginBottom: '16px' }}>
          <Row gutter={[12, 12]}>
            {media.map((mediaItem, index) => (
              <Col xs={12} sm={8} md={6} lg={4} key={mediaItem.id || index}>
                <div
                  style={{
                    border: '1px solid #f0f0f0',
                    borderRadius: '8px',
                    overflow: 'hidden',
                    aspectRatio: '1',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    backgroundColor: '#fafafa'
                  }}>
                  <Image
                    src={mediaItem.url}
                    alt={`${title} - Image ${index + 1}`}
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                    preview={{
                      mask: (
                        <div
                          style={{
                            background: 'rgba(0,0,0,0.5)',
                            color: 'white',
                            fontSize: '12px',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: '100%'
                          }}>
                          <EyeOutlined style={{ marginRight: '4px' }} />
                          Xem
                        </div>
                      )
                    }}
                  />
                </div>
              </Col>
            ))}
          </Row>
        </Card>
      )}

      {/* Additional Information */}
      {(notes || solution || feedback) && (
        <Card
          title={
            <Space>
              <InfoCircleOutlined style={{ color: '#13c2c2' }} />
              <BaseText>Thông tin bổ sung</BaseText>
            </Space>
          }
          size='small'>
          <Row gutter={[16, 16]}>
            {notes && (
              <Col span={24}>
                <BaseText color='textSecondary'>Ghi chú:</BaseText>
                <Paragraph style={{ marginTop: '8px', marginBottom: '0' }}>
                  {notes}
                </Paragraph>
              </Col>
            )}
            {solution && (
              <Col span={24}>
                <BaseText color='textSecondary'>Giải pháp:</BaseText>
                <Paragraph style={{ marginTop: '8px', marginBottom: '0' }}>
                  {solution}
                </Paragraph>
              </Col>
            )}
            {feedback && (
              <Col span={24}>
                <BaseText color='textSecondary'>
                  Phản hồi từ khách hàng:
                </BaseText>
                <Paragraph style={{ marginTop: '8px', marginBottom: '0' }}>
                  {feedback}
                </Paragraph>
                {rating > 0 && (
                  <div style={{ marginTop: '8px' }}>
                    <BaseText color='textSecondary'>Đánh giá: </BaseText>
                    <Space>
                      {[...Array(5)].map((_, index) => (
                        <StarOutlined
                          key={index}
                          style={{
                            color: index < rating ? '#faad14' : '#d9d9d9'
                          }}
                        />
                      ))}
                    </Space>
                  </div>
                )}
              </Col>
            )}
          </Row>
        </Card>
      )}
    </div>
  )

  return (
    <>
      <BaseButton
        type='primary'
        shape='circle'
        icon={<EyeOutlined />}
        tooltip='Xem chi tiết'
        onClick={openModal}
      />
      <BaseModal
        open={open}
        title='Chi tiết khiếu nại'
        description='Thông tin chi tiết khiếu nại'
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  )
}

export default DetailButton
