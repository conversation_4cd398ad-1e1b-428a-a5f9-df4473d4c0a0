import { SaveOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  Button,
  Select,
  Upload,
  message,
  DatePicker
} from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useState } from 'react'
import { toastService } from '~/services'
import type { UploadFile, UploadProps } from 'antd'
import useUploadMutiple from '~/hooks/uploadFile/useUploadMutiple'
import useUploadSingle from '~/hooks/uploadFile/useUploadSingle'
import {
  ICreateComplaintReq,
  EComplaintType,
  EComplaintPriority,
  ComplaintTypeLabels,
  ComplaintPriorityLabels
} from '~/dto/complaint.dto'
import dayjs from 'dayjs'

const { Option } = Select
const { TextArea } = Input

interface CreateComplaintModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateComplaintModal = ({
  open,
  onClose,
  onSuccess
}: CreateComplaintModalProps) => {
  const [form] = useForm()
  const [fileList, setFileList] = useState<UploadFile[]>([])

  const { mutateAsync: uploadSingle, isPending: isUploadingSingle } =
    useUploadSingle()

  const handleSave = async (values: any) => {
    if (!values) return

    const body: ICreateComplaintReq = {
      ...values,
      startDate: values.startDate.format('YYYY-MM-DD'),
      dueDate: values.dueDate.format('YYYY-MM-DD'),
      media: fileList.map((file) => file.url)
    }

    try {
      // TODO: Implement create complaint API call
      onClose()
      onSuccess?.()
      // Reset form and file list
      form.resetFields()
      setFileList([])
    } catch (error) {
      toastService.error('Tạo khiếu nại thất bại')
    }
  }

  // Handle image upload
  const handleUploadChange = (res: any) => {
    if (res.Location) {
      setFileList((curr) => {
        return [
          ...curr,
          {
            uid: res.Location,
            name: res.Location,
            status: 'done',
            url: res.Location
          }
        ]
      })
    }
  }

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith('image/')
    if (!isImage) {
      message.error('Chỉ được upload file hình ảnh!')
      return false
    }

    const isLt5M = file.size / 1024 / 1024 < 5
    if (!isLt5M) {
      message.error('Hình ảnh phải nhỏ hơn 5MB!')
      return false
    }

    return true
  }

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  )

  const modalContent = (
    <Form form={form} layout='vertical' onFinish={handleSave}>
      {/* Thông tin cơ bản */}
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label='Tiêu đề'
            name='title'
            rules={[{ required: true, message: 'Vui lòng nhập tiêu đề' }]}>
            <Input placeholder='Nhập tiêu đề khiếu nại' />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label='Loại khiếu nại'
            name='type'
            rules={[
              { required: true, message: 'Vui lòng chọn loại khiếu nại' }
            ]}>
            <Select placeholder='Chọn loại khiếu nại'>
              {Object.entries(ComplaintTypeLabels).map(([value, label]) => (
                <Option key={value} value={value}>
                  {label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label='Mô tả'
            name='description'
            rules={[{ required: true, message: 'Vui lòng nhập mô tả' }]}>
            <TextArea rows={3} placeholder='Nhập mô tả chi tiết khiếu nại' />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label='Mức độ ưu tiên'
            name='priority'
            rules={[
              { required: true, message: 'Vui lòng chọn mức độ ưu tiên' }
            ]}>
            <Select placeholder='Chọn mức độ ưu tiên'>
              {Object.entries(ComplaintPriorityLabels).map(([value, label]) => (
                <Option key={value} value={value}>
                  {label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label='Mã khách hàng'
            name='customerId'
            rules={[{ required: true, message: 'Vui lòng chọn khách hàng' }]}>
            <Select placeholder='Chọn khách hàng'>
              {/* TODO: Add customer options */}
              <Option value='1'>Công ty TNHH ABC</Option>
              <Option value='2'>Công ty TNHH XYZ</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      {/* Thông tin địa chỉ */}
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label='Địa chỉ khiếu nại'
            name='complaintAddress'
            rules={[
              { required: true, message: 'Vui lòng nhập địa chỉ khiếu nại' }
            ]}>
            <Input placeholder='Nhập địa chỉ khiếu nại' />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label='Địa chỉ'
            name='address'
            rules={[{ required: true, message: 'Vui lòng nhập địa chỉ' }]}>
            <Input placeholder='Nhập địa chỉ' />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item label='Tỉnh/Thành phố' name='provinceCode'>
            <Select placeholder='Chọn tỉnh/thành phố'>
              {/* TODO: Add province options */}
              <Option value='01'>Hồ Chí Minh</Option>
              <Option value='02'>Hà Nội</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label='Quận/Huyện' name='districtCode'>
            <Select placeholder='Chọn quận/huyện'>
              {/* TODO: Add district options */}
              <Option value='001'>Quận 1</Option>
              <Option value='002'>Quận 2</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item label='Phường/Xã' name='wardCode'>
            <Select placeholder='Chọn phường/xã'>
              {/* TODO: Add ward options */}
              <Option value='00001'>Phường 1</Option>
              <Option value='00002'>Phường 2</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      {/* Thông tin nhân viên */}
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label='NV theo dõi/giám sát'
            name='supervisorId'
            rules={[
              { required: true, message: 'Vui lòng chọn nhân viên giám sát' }
            ]}>
            <Select placeholder='Chọn nhân viên giám sát'>
              {/* TODO: Add supervisor options */}
              <Option value='1'>Nguyễn Văn A</Option>
              <Option value='2'>Trần Thị B</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label='NV được phân công'
            name='assignedStaffId'
            rules={[
              {
                required: true,
                message: 'Vui lòng chọn nhân viên được phân công'
              }
            ]}>
            <Select placeholder='Chọn nhân viên được phân công'>
              {/* TODO: Add assigned staff options */}
              <Option value='1'>Lê Văn C</Option>
              <Option value='2'>Phạm Thị D</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      {/* Thông tin thời gian */}
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label='Ngày bắt đầu'
            name='startDate'
            rules={[{ required: true, message: 'Vui lòng chọn ngày bắt đầu' }]}>
            <DatePicker
              style={{ width: '100%' }}
              placeholder='Chọn ngày bắt đầu'
              format='DD/MM/YYYY'
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label='Ngày hết hạn'
            name='dueDate'
            rules={[{ required: true, message: 'Vui lòng chọn ngày hết hạn' }]}>
            <DatePicker
              style={{ width: '100%' }}
              placeholder='Chọn ngày hết hạn'
              format='DD/MM/YYYY'
            />
          </Form.Item>
        </Col>
      </Row>

      {/* Hình ảnh đính kèm */}
      <Form.Item label='Hình ảnh đính kèm' name='media'>
        <Upload
          listType='picture-card'
          fileList={fileList}
          onChange={handleUploadChange}
          beforeUpload={beforeUpload}
          multiple
          accept='image/*'
          customRequest={async ({ file, onSuccess }) => {
            try {
              const formData = new FormData()
              formData.append('file', file as File)

              await uploadSingle(formData).then((res) => {
                handleUploadChange(res)
              })
            } catch (error) {}
          }}>
          {fileList.length >= 8 ? null : uploadButton}
        </Upload>
      </Form.Item>

      <div
        style={{
          textAlign: 'right',
          marginTop: 24,
          borderTop: '1px solid #f0f0f0',
          paddingTop: 16
        }}>
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          Hủy
        </Button>
        <Button
          type='primary'
          htmlType='submit'
          icon={<SaveOutlined />}
          loading={isUploadingSingle}>
          Tạo khiếu nại
        </Button>
      </div>
    </Form>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo khiếu nại mới'
      description='Thêm khiếu nại mới vào hệ thống'
      childrenBody={modalContent}
    />
  )
}

export default CreateComplaintModal
