import { But<PERSON>, Col, Collapse, Form, Input, Row, Select } from 'antd'
import { FC, useCallback } from 'react'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { IFilterComplaint as IFilterComplaintDTO } from '~/dto/complaint.dto'

interface IProps {
  onFilter: (values: IFilterComplaintDTO) => void
  onReset: () => void
  isLoading: boolean
}

const FilterProduct: FC<IProps> = ({ onFilter, onReset, isLoading }) => {
  const [form] = Form.useForm()

  const handleSubmit = useCallback(
    (values: IFilterComplaintDTO) => {
      onFilter(values)
    },
    [onFilter]
  )

  const handleReset = useCallback(() => {
    form.resetFields()
    onReset()
  }, [form, onReset])

  return (
    <Collapse>
      <Collapse.Panel header='Tìm kiếm' key='0'>
        <Form
          form={form}
          onFinish={handleSubmit}
          layout='vertical'
          className='bg-white p-4 rounded-lg'>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item name='title' label='Tiêu đề'>
                <Input placeholder='Nhập tiêu đề' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='type' label='Loại'>
                <Select placeholder='Chọn loại'>
                  <Select.Option value='TYPE_1'>Loại 1</Select.Option>
                  <Select.Option value='TYPE_2'>Loại 2</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='status' label='Trạng thái'>
                <Select placeholder='Chọn trạng thái'>
                  <Select.Option value='PENDING'>Đang chờ</Select.Option>
                  <Select.Option value='PROCESSING'>Đang xử lý</Select.Option>
                  <Select.Option value='COMPLETED'>Hoàn thành</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='customerCode' label='Mã khách hàng'>
                <Input placeholder='Nhập mã khách hàng' />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item name='provinceCode' label='Tỉnh/Thành phố'>
                <Select placeholder='Chọn tỉnh/thành phố'>
                  <Select.Option value='01'>Hồ Chí Minh</Select.Option>
                  <Select.Option value='02'>Hà Nội</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='districtCode' label='Quận/Huyện'>
                <Select placeholder='Chọn quận/huyện'>
                  <Select.Option value='001'>Quận 1</Select.Option>
                  <Select.Option value='002'>Quận 2</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='wardCode' label='Phường/Xã'>
                <Select placeholder='Chọn phường/xã'>
                  <Select.Option value='00001'>Phường 1</Select.Option>
                  <Select.Option value='00002'>Phường 2</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label=' '>
                <div
                  style={{
                    display: 'flex',
                    gap: 10
                  }}>
                  <Button
                    type='primary'
                    style={{ width: '50%' }}
                    htmlType='submit'
                    onClick={() => handleSubmit(form.getFieldsValue())}
                    loading={isLoading}>
                    <SearchOutlined />
                    Tìm kiếm
                  </Button>
                  <Button
                    type='default'
                    style={{ width: '50%' }}
                    htmlType='submit'
                    onClick={() => handleReset()}>
                    <ReloadOutlined />
                    Làm mới
                  </Button>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  )
}

export default FilterProduct
