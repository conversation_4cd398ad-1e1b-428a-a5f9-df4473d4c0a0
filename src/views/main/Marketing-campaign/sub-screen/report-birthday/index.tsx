import React, { useEffect, useState } from 'react'
import { Card, DatePicker, Space, Select, Typography } from 'antd'
import dayjs from 'dayjs'
import FullCalendar from '@fullcalendar/react'
import dayGridPlugin from '@fullcalendar/daygrid'
import interactionPlugin from '@fullcalendar/interaction'
import BaseView from '~/components/BaseView'

const { MonthPicker } = DatePicker
const { Option } = Select
const { Title } = Typography

// Mock danh sách sinh nhật
const dataExample = [
  { name: 'Nguy<PERSON>n <PERSON>ăn A', dob: '1990-06-10' },
  { name: 'Trần Thị B', dob: '1995-06-22' },
  { name: '<PERSON><PERSON> Văn <PERSON>', dob: '1988-07-05' },
  { name: '<PERSON>ạm Thị D', dob: '1992-07-18' },
  { name: 'Đỗ Văn E', dob: '1986-08-01' }
]

export const ReportBirthdayView = () => {
  const [selectedMonth, setSelectedMonth] = useState<dayjs.Dayjs>(dayjs())
  const [selectedCustomer, setSelectedCustomer] = useState<string>('all')
  const [eventsBirthday, setEventsBirthday] = useState([])

  const handleMonthChange = (value: dayjs.Dayjs | null) => {
    if (value) setSelectedMonth(value)
  }

  const handleChooseCustomer = (value: string) => {
    setSelectedCustomer(value)
  }

  const getBirthdayEvents = (month: number, year: number, customer: string) => {
    const filteredCustomers =
      customer === 'all'
        ? dataExample
        : dataExample.filter((c) => c.name === customer)

    return filteredCustomers
      .map((c) => {
        const birthDate = dayjs(c.dob)
        const eventDate = dayjs(`${year}-${month}-${birthDate.date()}`)
        return {
          title: `🎂 ${c.name}`,
          date: eventDate.format('YYYY-MM-DD')
        }
      })
      .filter((event) => dayjs(event.date).month() + 1 === month)
  }

  useEffect(() => {
    const month = selectedMonth.month() + 1
    const year = selectedMonth.year()
    setEventsBirthday(getBirthdayEvents(month, year, selectedCustomer))
  }, [selectedMonth, selectedCustomer])

  return (
    <BaseView>
      <Card
        title='Lịch sinh nhật khách hàng'
        extra={
          <Space>
            <span>Chọn khách hàng:</span>
            <Select
              style={{ width: 200 }}
              value={selectedCustomer}
              onChange={handleChooseCustomer}>
              <Option value='all'>--Tất cả--</Option>
              {dataExample.map((c) => (
                <Option key={c.name} value={c.name}>
                  {c.name}
                </Option>
              ))}
            </Select>
            <span>Chọn tháng:</span>
            <MonthPicker
              value={selectedMonth}
              onChange={handleMonthChange}
              format='MM/YYYY'
              placeholder='Chọn tháng'
            />
          </Space>
        }>
        <FullCalendar
          plugins={[dayGridPlugin, interactionPlugin]}
          initialView='dayGridMonth'
          initialDate={selectedMonth.format('YYYY-MM-DD')}
          events={eventsBirthday}
          height='auto'
          locale={'vi'}
          headerToolbar={{
            left: '',
            center: 'title',
            right: ''
          }}
        />
      </Card>
    </BaseView>
  )
}
