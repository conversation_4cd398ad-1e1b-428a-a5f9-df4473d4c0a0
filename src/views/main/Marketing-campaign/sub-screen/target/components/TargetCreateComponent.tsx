import { Form, Input, Radio, Button, Select } from 'antd'
import { useEffect } from 'react'
import BaseModal from '~/components/BaseModal'

interface CreateTargetModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const { Option } = Select

export const TargetCreateComponent = ({
  open,
  onClose,
  onSuccess
}: CreateTargetModalProps) => {
  const [form] = Form.useForm()

  // Hàm xử lý submit
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields()
      console.log('Submitted values:', values)
      // Gọi API hoặc xử lý logic tạo nhóm ở đây
      onSuccess?.()
      onClose()
    } catch (err) {
      console.log('Validation failed:', err)
    }
  }

  // Reset form mỗi khi mở modal
  useEffect(() => {
    if (open) {
      form.resetFields()
    }
  }, [open])

  const contentModal = (
    <Form form={form} layout='vertical'>
      <Form.Item
        label='Mã nhóm'
        name='groupCode'
        rules={[{ required: true, message: 'Vui lòng nhập mã nhóm' }]}>
        <Input placeholder='Nhập mã nhóm' />
      </Form.Item>

      <Form.Item
        label='Tên nhóm'
        name='groupName'
        rules={[{ required: true, message: 'Vui lòng nhập tên nhóm' }]}>
        <Input placeholder='Nhập tên nhóm' />
      </Form.Item>

      <Form.Item
        label='Nhóm khách hàng'
        name='customerGroup'
        rules={[{ required: true, message: 'Vui lòng chọn nhóm khách hàng' }]}>
        <Select placeholder='Chọn nhóm khách hàng'>
          <Option value='group1'>Nhóm 1</Option>
          <Option value='group2'>Nhóm 2</Option>
          <Option value='group3'>Nhóm 3</Option>
        </Select>
      </Form.Item>

      <Form.Item
        label='Trạng thái'
        name='status'
        initialValue='active'
        rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}>
        <Radio.Group>
          <Radio value='active'>Đang sử dụng</Radio>
          <Radio value='inactive'>Ngừng sử dụng</Radio>
        </Radio.Group>
      </Form.Item>

      <Form.Item>
        <Button type='primary' onClick={handleSubmit}>
          Tạo mới
        </Button>
        <Button style={{ marginLeft: 8 }} onClick={onClose}>
          Hủy
        </Button>
      </Form.Item>
    </Form>
  )

  return (
    <BaseModal
      open={open}
      title='Tạo mục tiêu mới'
      description='Thêm mục tiêu mới vào hệ thống'
      onClose={onClose}
      childrenBody={contentModal}
    />
  )
}
