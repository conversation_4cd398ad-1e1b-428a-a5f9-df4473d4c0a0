import { Tooltip, Tag, Typography, Col, Row } from 'antd'
import { useState } from 'react'
import { getColorPercent } from '~/common/utils/common.utils'
import BaseTable from '~/components/BaseTable'
import BaseView from '~/components/BaseView'
import {
  IFilterReportKpiMarketingEmployee,
  ReportKpiMarketingEmployee
} from '~/dto/report-kpi-marketing-employee.dto'
import FilterProduct from './components/FilterProduct'
const { Text } = Typography

export const MarketingReportKPIView = () => {
  //filter
  const [filter, setFilter] = useState<IFilterReportKpiMarketingEmployee>({
    pageIndex: 1,
    pageSize: 10
  })

  const columns: any[] = [
    {
      title: 'STT',
      key: 'stt',
      width: 60,
      align: 'center',
      render: (_: any, __: any, index: number) => index + 1
    },
    { title: 'Mã NV', dataIndex: 'code', key: 'code', align: 'center', width: 100 },
    { title: '<PERSON><PERSON><PERSON> viên', dataIndex: 'name', key: 'name', align: 'center', width: 200 },
    {
      title: (
        <Tooltip title='Số lượng chiến dịch marketing đã được lên kế hoạch và thực hiện (email, quảng cáo, sự kiện,...).'>
          <Text strong> Chiến dịch triển khai</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'campaignsTarget',
          key: 'campaignsTarget',
          align: 'center',
          width: 100
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'campaignsActual',
          key: 'campaignsActual',
          align: 'center',
          width: 100
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'campaignsRate',
          key: 'campaignsRate',
          align: 'center',
          width: 100,
          render: (v: number) => <Tag color={getColorPercent(v)}>{v}</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tổng số người nhìn thấy hoặc tương tác với chiến dịch (view, impression, reach,...).'>
          <Text strong> Lượt tiếp cận</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'reachTarget',
          key: 'reachTarget',
          align: 'center',
          width: 100
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'reachActual',
          key: 'reachActual',
          align: 'center',
          width: 100
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'reachRate',
          key: 'reachRate',
          align: 'center',
          width: 100,
          render: (v: number) => <Tag color={getColorPercent(v)}>{v}</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tỷ lệ người tiếp cận chuyển đổi thành hành động cụ thể (đăng ký, mua hàng, điền form,...).'>
          <Text strong> Tỷ lệ chuyển đổi</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'conversionRateTarget',
          key: 'conversionRateTarget',
          align: 'center',
          width: 100
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'conversionRateActual',
          key: 'conversionRateActual',
          align: 'center',
          width: 100
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'conversionRateRate',
          key: 'conversionRateRate',
          align: 'center',
          width: 100,
          render: (v: number) => <Tag color={getColorPercent(v)}>{v}</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tổng chi phí marketing chia cho số lượng lead thu về (đánh giá hiệu quả ngân sách).'>
          <Text strong> Chi phí/lead</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'costPerLeadTarget',
          key: 'costPerLeadTarget',
          align: 'center',
          width: 100
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'costPerLeadActual',
          key: 'costPerLeadActual',
          align: 'center',
          width: 100
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'costPerLeadRate',
          key: 'costPerLeadRate',
          align: 'center',
          width: 100,
          render: (v: number) => <Tag color={getColorPercent(v)}>{v}</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Số lượng khách hàng tiềm năng (lead) mà marketing chuyển giao cho sale (qualified lead).'>
          <Text strong> Số lead MKT</Text>
        </Tooltip>
      ),
      align: 'center',
      width: 150,
      children: [
        {
          title: 'Chỉ tiêu',
          dataIndex: 'leadsTarget',
          key: 'leadsTarget',
          align: 'center',
          width: 100
        },
        {
          title: 'Kết quả thực hiện',
          dataIndex: 'leadsActual',
          key: 'leadsActual',
          align: 'center',
          width: 100
        },
        {
          title: 'Tỷ lệ hoàn thành',
          dataIndex: 'leadsRate',
          key: 'leadsRate',
          align: 'center',
          width: 100,
          render: (v: number) => <Tag color={getColorPercent(v)}>{v}</Tag>
        }
      ]
    },
    {
      title: (
        <Tooltip title='Tổng điểm KPI = ∑ (Tỷ lệ hoàn thành × Trọng số) / 100'>
          <Text strong> Tổng điểm KPI</Text>
        </Tooltip>
      ),
      align: 'center',
      dataIndex: 'totalKpi',
      key: 'totalKpi',
      width: 120,
      render: (v: number) => <Tag color={getColorPercent(v)}>{v}</Tag>
    }
  ]

  const fakeData: ReportKpiMarketingEmployee[] = [
    {
      stt: 1,
      code: 'NV000001',
      name: 'Nguyễn Văn Y',
      campaignsTarget: 3,
      campaignsActual: 4,
      campaignsRate: '133%',
      reachTarget: 10,
      reachActual: 9,
      reachRate: '90%',
      conversionRateTarget: '5%',
      conversionRateActual: '4.5%',
      conversionRateRate: '90%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 85000,
      costPerLeadRate: '85%',
      leadsTarget: 100,
      leadsActual: 110,
      leadsRate: '110%',
      totalKpi: '102.85%'
    },
    {
      stt: 2,
      code: 'NV000002',
      name: 'Trần Thị K',
      campaignsTarget: 3,
      campaignsActual: 2,
      campaignsRate: '67%',
      reachTarget: 10,
      reachActual: 12,
      reachRate: '120%',
      conversionRateTarget: '5%',
      conversionRateActual: '3.5%',
      conversionRateRate: '70%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 105000,
      costPerLeadRate: '105%',
      leadsTarget: 100,
      leadsActual: 90,
      leadsRate: '90%',
      totalKpi: '88.65%'
    },
    {
      stt: 3,
      code: 'NV000003',
      name: 'Lê Hoàng L',
      campaignsTarget: 3,
      campaignsActual: 3,
      campaignsRate: '100%',
      reachTarget: 10,
      reachActual: 11,
      reachRate: '110%',
      conversionRateTarget: '5%',
      conversionRateActual: '6.2%',
      conversionRateRate: '124%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 78000,
      costPerLeadRate: '78%',
      leadsTarget: 100,
      leadsActual: 130,
      leadsRate: '130%',
      totalKpi: '110.70%'
    },
    {
      stt: 4,
      code: 'NV000004',
      name: 'Phạm Mai M',
      campaignsTarget: 3,
      campaignsActual: 1,
      campaignsRate: '33%',
      reachTarget: 10,
      reachActual: 6,
      reachRate: '60%',
      conversionRateTarget: '5%',
      conversionRateActual: '2.5%',
      conversionRateRate: '50%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 120000,
      costPerLeadRate: '120%',
      leadsTarget: 100,
      leadsActual: 50,
      leadsRate: '50%',
      totalKpi: '59.10%'
    },
    {
      stt: 5,
      code: 'NV000005',
      name: 'Hồ Thị N',
      campaignsTarget: 3,
      campaignsActual: 3,
      campaignsRate: '100%',
      reachTarget: 10,
      reachActual: 10,
      reachRate: '100%',
      conversionRateTarget: '5%',
      conversionRateActual: '5%',
      conversionRateRate: '100%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 95000,
      costPerLeadRate: '95%',
      leadsTarget: 100,
      leadsActual: 100,
      leadsRate: '100%',
      totalKpi: '100.00%'
    },
    // Manual dummy records
    {
      stt: 6,
      code: 'NV000006',
      name: 'Trịnh Văn A',
      campaignsTarget: 3,
      campaignsActual: 2,
      campaignsRate: '67%',
      reachTarget: 10,
      reachActual: 8,
      reachRate: '80%',
      conversionRateTarget: '5%',
      conversionRateActual: '4%',
      conversionRateRate: '80%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 110000,
      costPerLeadRate: '110%',
      leadsTarget: 100,
      leadsActual: 95,
      leadsRate: '95%',
      totalKpi: '84.50%'
    },
    {
      stt: 7,
      code: 'NV000007',
      name: 'Lê Thị B',
      campaignsTarget: 3,
      campaignsActual: 4,
      campaignsRate: '133%',
      reachTarget: 10,
      reachActual: 12,
      reachRate: '120%',
      conversionRateTarget: '5%',
      conversionRateActual: '6%',
      conversionRateRate: '120%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 90000,
      costPerLeadRate: '90%',
      leadsTarget: 100,
      leadsActual: 105,
      leadsRate: '105%',
      totalKpi: '108.30%'
    },
    {
      stt: 8,
      code: 'NV000008',
      name: 'Phan Đức C',
      campaignsTarget: 3,
      campaignsActual: 3,
      campaignsRate: '100%',
      reachTarget: 10,
      reachActual: 11,
      reachRate: '110%',
      conversionRateTarget: '5%',
      conversionRateActual: '5.5%',
      conversionRateRate: '110%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 98000,
      costPerLeadRate: '98%',
      leadsTarget: 100,
      leadsActual: 115,
      leadsRate: '115%',
      totalKpi: '106.85%'
    },
    {
      stt: 9,
      code: 'NV000009',
      name: 'Đỗ Lan D',
      campaignsTarget: 3,
      campaignsActual: 1,
      campaignsRate: '33%',
      reachTarget: 10,
      reachActual: 7,
      reachRate: '70%',
      conversionRateTarget: '5%',
      conversionRateActual: '3%',
      conversionRateRate: '60%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 125000,
      costPerLeadRate: '125%',
      leadsTarget: 100,
      leadsActual: 60,
      leadsRate: '60%',
      totalKpi: '60.15%'
    },
    {
      stt: 10,
      code: 'NV000010',
      name: 'Vũ Thị E',
      campaignsTarget: 3,
      campaignsActual: 2,
      campaignsRate: '67%',
      reachTarget: 10,
      reachActual: 9,
      reachRate: '90%',
      conversionRateTarget: '5%',
      conversionRateActual: '4%',
      conversionRateRate: '80%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 100000,
      costPerLeadRate: '100%',
      leadsTarget: 100,
      leadsActual: 85,
      leadsRate: '85%',
      totalKpi: '84.50%'
    },
    {
      stt: 11,
      code: 'NV000011',
      name: 'Ngô Minh F',
      campaignsTarget: 3,
      campaignsActual: 3,
      campaignsRate: '100%',
      reachTarget: 10,
      reachActual: 10,
      reachRate: '100%',
      conversionRateTarget: '5%',
      conversionRateActual: '5%',
      conversionRateRate: '100%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 92000,
      costPerLeadRate: '92%',
      leadsTarget: 100,
      leadsActual: 105,
      leadsRate: '105%',
      totalKpi: '99.70%'
    },
    {
      stt: 12,
      code: 'NV000012',
      name: 'Trần Khánh G',
      campaignsTarget: 3,
      campaignsActual: 4,
      campaignsRate: '133%',
      reachTarget: 10,
      reachActual: 13,
      reachRate: '130%',
      conversionRateTarget: '5%',
      conversionRateActual: '7%',
      conversionRateRate: '140%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 88000,
      costPerLeadRate: '88%',
      leadsTarget: 100,
      leadsActual: 120,
      leadsRate: '120%',
      totalKpi: '116.30%'
    },
    {
      stt: 13,
      code: 'NV000013',
      name: 'Lý Thanh H',
      campaignsTarget: 3,
      campaignsActual: 3,
      campaignsRate: '100%',
      reachTarget: 10,
      reachActual: 12,
      reachRate: '120%',
      conversionRateTarget: '5%',
      conversionRateActual: '6%',
      conversionRateRate: '120%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 97000,
      costPerLeadRate: '97%',
      leadsTarget: 100,
      leadsActual: 115,
      leadsRate: '115%',
      totalKpi: '106.45%'
    },
    {
      stt: 14,
      code: 'NV000014',
      name: 'Phạm Văn I',
      campaignsTarget: 3,
      campaignsActual: 2,
      campaignsRate: '67%',
      reachTarget: 10,
      reachActual: 8,
      reachRate: '80%',
      conversionRateTarget: '5%',
      conversionRateActual: '4.5%',
      conversionRateRate: '90%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 103000,
      costPerLeadRate: '103%',
      leadsTarget: 100,
      leadsActual: 95,
      leadsRate: '95%',
      totalKpi: '93.35%'
    },
    {
      stt: 15,
      code: 'NV000015',
      name: 'Văn Thị K',
      campaignsTarget: 3,
      campaignsActual: 3,
      campaignsRate: '100%',
      reachTarget: 10,
      reachActual: 11,
      reachRate: '110%',
      conversionRateTarget: '5%',
      conversionRateActual: '5.2%',
      conversionRateRate: '104%',
      costPerLeadTarget: 100000,
      costPerLeadActual: 96000,
      costPerLeadRate: '96%',
      leadsTarget: 100,
      leadsActual: 105,
      leadsRate: '105%',
      totalKpi: '101.28%'
    }
  ]

  const handleFilter = (values: IFilterReportKpiMarketingEmployee) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFilter({
      pageIndex: 1,
      pageSize: 10
    })
  }

  const handlePageChange = (page: number, pageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: page,
      pageSize
    })
  }

  return (
    <BaseView>
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={24}>
          <FilterProduct onFilter={handleFilter} onReset={handleReset} isLoading={false} />
        </Col>
      </Row>
      <BaseTable
        columns={columns}
        data={fakeData}
        total={fakeData.length}
        isLoading={false}
        scroll={{ x: 2000 }}
      />
    </BaseView>
  )
}
