import React, { useState } from 'react'
import { Table, Input, Button, Space, Tag, Select, Collapse, Row, Col, Form } from 'antd'
import { SearchOutlined, DownloadOutlined, ReloadOutlined } from '@ant-design/icons'
import type { ColumnsType } from 'antd/es/table'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'

interface ZNSRecord {
  key: string
  idZNS: string
  templateName: string
  campaignName: string
  customerCode: string
  customerName: string
  phone: string
  contactName: string
  status: string
  message: string
  sentAt: string
}

const mockData: ZNSRecord[] = [
  {
    key: '1',
    idZNS: '317128',
    templateName: 'Chăm sóc khách hàng',
    campaignName: 'Chăm sóc khách hàng chưa định danh',
    customerCode: '*********',
    customerName: 'Công ty TNHH ADCS',
    phone: '**********',
    contactName: '<PERSON>h <PERSON><PERSON>',
    status: 'Th<PERSON>t bại',
    message: '<PERSON><PERSON> account not found',
    sentAt: '18/06/2025 14:52'
  },
  {
    key: '2',
    idZNS: '317128',
    templateName: 'Chăm sóc khách hàng',
    campaignName: 'Chăm sóc khách hàng định kỳ',
    customerCode: '*********',
    customerName: 'Công ty Cổ phần ABIS',
    phone: '**********',
    contactName: 'Chị Thùy',
    status: 'Thành công',
    message: 'Success',
    sentAt: '18/06/2025 14:52'
  },
  {
    key: '3',
    idZNS: '317130',
    templateName: 'Thông báo bảo trì',
    campaignName: 'Thông báo hệ thống',
    customerCode: '*********',
    customerName: 'Công ty ABC',
    phone: '**********',
    contactName: 'Anh Bình',
    status: 'Thành công',
    message: 'Success',
    sentAt: '18/06/2025 15:00'
  },
  {
    key: '4',
    idZNS: '317131',
    templateName: 'Khuyến mãi hè',
    campaignName: 'Marketing 2025',
    customerCode: '*********',
    customerName: 'Công ty XYZ',
    phone: '**********',
    contactName: 'Chị Mai',
    status: 'Thất bại',
    message: 'Zalo account not found',
    sentAt: '18/06/2025 15:01'
  },
  {
    key: '5',
    idZNS: '317132',
    templateName: 'Cảm ơn khách hàng',
    campaignName: 'Kết thúc đơn hàng',
    customerCode: '*********',
    customerName: 'Công ty DEF',
    phone: '**********',
    contactName: 'Anh Hùng',
    status: 'Thành công',
    message: 'Success',
    sentAt: '18/06/2025 15:02'
  },
  {
    key: '6',
    idZNS: '317133',
    templateName: 'Nhắc gia hạn',
    campaignName: 'Reminder Premium',
    customerCode: '*********',
    customerName: 'Công ty KLM',
    phone: '**********',
    contactName: 'Chị Trâm',
    status: 'Thành công',
    message: 'Success',
    sentAt: '18/06/2025 15:03'
  },
  {
    key: '7',
    idZNS: '317134',
    templateName: 'Gửi mã xác thực',
    campaignName: 'Xác thực tài khoản',
    customerCode: '*********',
    customerName: 'Công ty STU',
    phone: '**********',
    contactName: 'Anh Quang',
    status: 'Thất bại',
    message: 'Blocked user',
    sentAt: '18/06/2025 15:04'
  },
  {
    key: '8',
    idZNS: '317135',
    templateName: 'Chăm sóc khách hàng',
    campaignName: 'CSKH 2025',
    customerCode: '651181889',
    customerName: 'Công ty GHI',
    phone: '0966442211',
    contactName: 'Anh Nam',
    status: 'Thành công',
    message: 'Success',
    sentAt: '18/06/2025 15:05'
  },
  {
    key: '9',
    idZNS: '317136',
    templateName: 'Thông báo đơn hàng',
    campaignName: 'Đơn hàng mới',
    customerCode: '651181890',
    customerName: 'Công ty LMN',
    phone: '0977554433',
    contactName: 'Chị Hà',
    status: 'Thành công',
    message: 'Success',
    sentAt: '18/06/2025 15:06'
  },
  {
    key: '10',
    idZNS: '317137',
    templateName: 'Cảnh báo đăng nhập',
    campaignName: 'Security Notice',
    customerCode: '651181891',
    customerName: 'Công ty PQR',
    phone: '0955332211',
    contactName: 'Anh Tài',
    status: 'Thất bại',
    message: 'Invalid phone',
    sentAt: '18/06/2025 15:07'
  }
]

const statusColorMap: Record<string, string> = {
  'Thành công': 'green',
  'Thất bại': 'red'
}

const { Option } = Select

export const MarketingReportZNSView = () => {
  const [customerName, setCustomerName] = useState('')
  const [phone, setPhone] = useState('')
  const [status, setStatus] = useState<string | undefined>(undefined)
  const [idZNS, setIdZNS] = useState('')

  const filteredData = mockData.filter((item) => {
    return (
      item.customerName.toLowerCase().includes(customerName.toLowerCase()) &&
      item.phone.includes(phone) &&
      (status ? item.status === status : true)
    )
  })

  const exportToExcel = () => {
    const worksheet = XLSX.utils.json_to_sheet(filteredData)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'ZNS_Report')
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    })
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
    })
    saveAs(blob, 'zns_report.xlsx')
  }

  const columns: ColumnsType<ZNSRecord> = [
    { title: 'STT', key: 'stt', width: 60, align: 'center', render: (_, __, index) => index + 1 },
    { title: 'ID ZNS', dataIndex: 'idZNS', key: 'idZNS' },
    { title: 'Tên mẫu ZNS', dataIndex: 'templateName', key: 'templateName' },
    { title: 'Tên chiến dịch', dataIndex: 'campaignName', key: 'campaignName' },
    { title: 'Mã KH', dataIndex: 'customerCode', key: 'customerCode' },
    { title: 'Tên KH', dataIndex: 'customerName', key: 'customerName' },
    { title: 'SĐT', dataIndex: 'phone', key: 'phone' },
    { title: 'Liên hệ', dataIndex: 'contactName', key: 'contactName' },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => <Tag color={statusColorMap[status] || 'default'}>{status}</Tag>
    },
    { title: 'Message', dataIndex: 'message', key: 'message' },
    { title: 'Thời gian gửi', dataIndex: 'sentAt', key: 'sentAt' }
  ]

  return (
    <BaseView>
      <Collapse style={{ marginBottom: 16 }}>
        <Collapse.Panel header='Tìm kiếm' key='0'>
          <Row gutter={16}>
            <Col span={6}>
              <Form.Item label='ID ZNS' name='idZNS'>
                <Input
                  placeholder='Nhập ID ZNS'
                  value={idZNS}
                  onChange={(e) => setIdZNS(e.target.value)}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='Tên khách hàng' name='customerName'>
                <Input
                  placeholder='Nhập tên khách hàng'
                  value={customerName}
                  onChange={(e) => setCustomerName(e.target.value)}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='Số điện thoại' name='phone'>
                <Input
                  placeholder='Nhập số điện thoại'
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='Trạng thái' name='status'>
                <Select
                  placeholder='Chọn trạng thái'
                  value={status}
                  onChange={(value) => setStatus(value)}>
                  <Option value=''>--Chọn trạng thái--</Option>
                  <Option value='Thành công'>Thành công</Option>
                  <Option value='Thất bại'>Thất bại</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24} style={{ marginTop: 10, justifyContent: 'center' }}>
            <Space>
              <Button type='primary' icon={<SearchOutlined />}>
                Tìm kiếm
              </Button>
              <Button icon={<ReloadOutlined />}>Làm mới</Button>
            </Space>
          </Row>
        </Collapse.Panel>
      </Collapse>
      <BaseTable
        rowKey='key'
        columns={columns}
        data={filteredData}
        total={filteredData.length}
        isLoading={false}
      />
    </BaseView>
  )
}
