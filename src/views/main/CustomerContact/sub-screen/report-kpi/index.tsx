import React from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>,
  Col,
  Collapse,
  Form,
  Row,
  Select,
  Space,
  Table,
  Tag,
  Tooltip,
  Typography
} from 'antd'
import type { ColumnsType } from 'antd/es/table'
import BaseView from '~/components/BaseView'
import BaseTable from '~/components/BaseTable'
import { DownloadOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { getColorPercent } from '~/common/utils/common.utils'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'

const { Text } = Typography

export interface SupportKPI {
  key: string
  name: string
  ticketTarget: number
  ticketResult: number
  ticketRate: number

  responseTarget: number
  responseResult: number
  responseRate: number

  csatTarget: number
  csatResult: number
  csatRate: number

  overdueTarget: string
  overdueResult: number
  overdueRate: number

  updateTarget: string
  updateResult: number
  updateRate: number
}

const columns: ColumnsType<SupportKPI> = [
  {
    title: 'STT',
    key: 'stt',
    width: 60,
    fixed: 'left',
    align: 'center',
    render: (_, __, index) => index + 1
  },
  {
    title: 'M<PERSON> Nhân viên',
    dataIndex: 'code',
    fixed: 'left',
    align: 'center',
    width: 120
  },
  {
    title: 'Nhân viên',
    dataIndex: 'name',
    fixed: 'left',
    align: 'center',
    width: 150
  },
  {
    title: (
      <Tooltip title='Số lượng yêu cầu, khiếu nại, hoặc phản hồi từ khách hàng đã được xử lý thành công.'>
        <Text strong>Ticket xử lý - Số lượng</Text>
      </Tooltip>
    ),
    children: [
      { title: 'Chỉ tiêu', dataIndex: 'ticketTarget', width: 70, align: 'center' },
      { title: 'Kết quả', dataIndex: 'ticketResult', width: 70, align: 'center' },
      {
        title: 'Tỷ lệ \n hoàn thành',
        dataIndex: 'ticketRate',
        width: 100,
        align: 'center',
        render: (text) => <Tag color={getColorPercent(text)}>{text}%</Tag>
      }
    ]
  },
  {
    title: (
      <Tooltip title='Số lượng nhiệm vụ hoàn thành trong tháng'>
        <Text strong>Hoàn thành nhiệm vụ</Text>
      </Tooltip>
    ),
    children: [
      { title: 'Chỉ tiêu', dataIndex: 'responseTarget', width: 70, align: 'center' },
      { title: 'Kết quả', dataIndex: 'responseResult', width: 70, align: 'center' },
      {
        title: 'Tỷ lệ hoàn thành',
        dataIndex: 'responseRate',
        width: 100,
        align: 'center',
        render: (text) => <Tag color={getColorPercent(text)}>{text}%</Tag>
      }
    ]
  },
  {
    title: (
      <Tooltip title='Điểm khảo sát sự hài lòng của khách hàng sau khi xử lý xong ticket (thường từ 1–5 hoặc %).'>
        <Text strong>Hài lòng KH (CSAT) - Điểm</Text>
      </Tooltip>
    ),
    children: [
      { title: 'Chỉ tiêu', dataIndex: 'csatTarget', width: 70, align: 'center' },
      { title: 'Kết quả', dataIndex: 'csatResult', width: 70, align: 'center' },
      {
        title: 'Tỷ lệ hoàn thành',
        dataIndex: 'csatRate',
        width: 100,
        align: 'center',
        render: (text) => <Tag color={getColorPercent(text)}>{text}%</Tag>
      }
    ]
  },
  {
    title: (
      <Tooltip title='Ticket được xử lý sau thời hạn cam kết (SLA), phản ánh sự chậm trễ.'>
        <Text strong>Hoàn thành ticket quá hạn</Text>
      </Tooltip>
    ),
    children: [
      { title: 'Chỉ tiêu', dataIndex: 'overdueTarget', width: 70, align: 'center' },
      { title: 'Kết quả', dataIndex: 'overdueResult', width: 70, align: 'center' },
      {
        title: 'Tỷ lệ hoàn thành',
        dataIndex: 'overdueRate',
        width: 100,
        align: 'center',
        render: (text) => <Tag color={getColorPercent(text, true)}>{text}%</Tag>
      }
    ]
  },
  {
    title: (
      <Tooltip title='Ticket được cập nhật trạng thái kịp thời trên hệ thống (ví dụ: Đang xử lý → Hoàn tất).'>
        <Text strong>Cập nhật trạng thái ticket đúng hạn</Text>
      </Tooltip>
    ),
    children: [
      { title: 'Chỉ tiêu', dataIndex: 'updateTarget', width: 70, align: 'center' },
      { title: 'Kết quả', dataIndex: 'updateResult', width: 70, align: 'center' },
      {
        title: 'Tỷ lệ hoàn thành',
        dataIndex: 'updateRate',
        width: 100,
        align: 'center',
        render: (text) => <Tag color={getColorPercent(text)}>{text}%</Tag>
      }
    ]
  },
  {
    title: 'Tổng điểm KPI',
    key: 'total',
    width: 70,
    align: 'center',
    fixed: 'right',
    render: (text, record) => {
      const total =
        record.ticketRate * 0.3 +
        record.responseRate * 0.15 +
        record.csatRate * 0.2 +
        record.overdueRate * 0.15 +
        record.updateRate * 0.2
      return (
        <Tag color={getColorPercent(total)}>
          <strong>{total.toFixed(2)}%</strong>
        </Tag>
      )
    }
  }
]

const data: SupportKPI[] = Array.from({ length: 10 }, (_, i) => ({
  key: `${i}`,
  name: `Nguyễn Văn Bùi`,
  code: `NV00000${i + 1}`,
  ticketTarget: 20,
  ticketResult: 18 + Math.floor(Math.random() * 10),
  ticketRate: 90 + Math.floor(Math.random() * 30),

  responseTarget: 100,
  responseResult: 30 + Math.floor(Math.random() * 30),
  responseRate: 30 + Math.floor(Math.random() * 30),

  csatTarget: 30,
  csatResult: 25 + Math.floor(Math.random() * 10),
  csatRate: 80 + Math.floor(Math.random() * 30),

  overdueTarget: '10',
  overdueResult: Math.floor(Math.random() * 21),
  overdueRate: 80 + Math.floor(Math.random() * 41),

  updateTarget: '95',
  updateResult: 60 + Math.floor(Math.random() * 40),
  updateRate: 60 + Math.floor(Math.random() * 50)
}))

const { Option } = Select

export const SupportReportKPIView = () => {
  const handleExport = () => {
    // XLSX
    const worksheet = XLSX.utils.json_to_sheet(data)
    const workbook = XLSX.utils.book_new()
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Support_KPI_Report')
    const excelBuffer = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    })
    const blob = new Blob([excelBuffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8'
    })
    saveAs(blob, 'support_kpi_report.xlsx')
  }

  return (
    <BaseView>
      <Card
        title='Báo cáo KPI chăm sóc khách hàng'
        extra={
          <Button type='primary' icon={<DownloadOutlined />} onClick={handleExport}>
            Xuất Excel
          </Button>
        }>
        {/* Bộ lọc filter theo tháng */}
        <Collapse>
          <Collapse.Panel header='Tìm kiếm' key='0'>
            <Row gutter={16}>
              <Col span={6}>
                <Form.Item label='Tháng' name='month'>
                  <Select onChange={(value) => console.log(value)} placeholder='Chọn tháng'>
                    <Option value=''>--Chọn tháng--</Option>
                    <Option value='1'>Tháng 1</Option>
                    <Option value='2'>Tháng 2</Option>
                    <Option value='3'>Tháng 3</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='Mã nhân viên' name='code'>
                  <Select onChange={(value) => console.log(value)} placeholder='Mã nhân viên'>
                    <Option value=''>--Chọn mã nhân viên--</Option>
                    <Option value='1'>NV000001</Option>
                    <Option value='2'>NV000002</Option>
                    <Option value='3'>NV000003</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='Tên nhân viên' name='name'>
                  <Select onChange={(value) => console.log(value)} placeholder='Tên nhân viên'>
                    <Option value=''>--Chọn tên nhân viên--</Option>
                    <Option value='1'>Nguyễn Văn A</Option>
                    <Option value='2'>Nguyễn Văn B</Option>
                    <Option value='3'>Nguyễn Văn C</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='Tổng điểm KPI' name='rate'>
                  <Select onChange={(value) => console.log(value)} placeholder='Tổng điểm KPI'>
                    <Option value=''>--Chọn tổng điểm KPI--</Option>
                    <Option value='1'>Từ 0% đến 50%</Option>
                    <Option value='2'>Từ 50% đến 100%</Option>
                    <Option value='3'>Trên 100%</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={24} style={{ marginTop: 10, justifyContent: 'center' }}>
              <Space>
                <Button type='primary' icon={<SearchOutlined />}>
                  Tìm kiếm
                </Button>
                <Button icon={<ReloadOutlined />}>
                  Làm mới
                </Button>
              </Space>
            </Row>
          </Collapse.Panel>
        </Collapse>
        <BaseTable
          style={{ marginTop: 16 }}
          bordered
          columns={columns}
          data={data}
          total={data.length}
          isLoading={false}
          scroll={{ x: 2000 }}
          pagination={false}
        />
      </Card>
    </BaseView>
  )
}
