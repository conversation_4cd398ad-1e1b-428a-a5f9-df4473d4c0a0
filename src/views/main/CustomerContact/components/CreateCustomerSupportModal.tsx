import { SaveOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Switch,
  Upload,
  message,
  DatePicker,
  Radio
} from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useEffect, useState } from 'react'
import { useCreateProduct } from '~/hooks/product/useCreateProduct'
import { CreateProductReq } from '~/dto/product.dto'
import { toastService } from '~/services'
import type { UploadFile } from 'antd'
import useUploadSingle from '~/hooks/uploadFile/useUploadSingle'
import { useModal } from '~/hooks/useModal'
import moment from 'moment'

const { Option } = Select
const { TextArea } = Input

interface CreateProductModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateCustomerSupportModal = ({
  open,
  onClose,
  onSuccess
}: CreateProductModalProps) => {
  const [form] = useForm()

  useEffect(() => {
    if (open) {
      form.setFieldsValue({
        id: null,
        title: null,
        description: null,
        type: null,
        status: null,
        visitLocation: null,
        customerCode: null,
        sapCode: null,
        address: null,
        supervisor: null,
        assignedEmployee: null,
        model: null,
        dueDate: null,
        actualDate: null,
        checkInDate: null,
        checkOutTime: null,
        messageStatus: null
      })
      // Set existing images to fileList
    }
  }, [open])

  const modalContent = (
    <div>
      <Form form={form} layout='vertical'>
        <Row gutter={16}>
          <Col span={16}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label='Phân loại chuyến thăm' name='type'>
                  <Select placeholder='-- Vui lòng chọn --' />
                </Form.Item>
              </Col>
             
            </Row>

            <Form.Item label='Tiêu đề' name='title'>
              <Input placeholder='Tự động cập nhật sau khi lưu' />
            </Form.Item>

            <Form.Item label='Mô tả' name='description'>
              <Input.TextArea placeholder='Nhập nội dung' rows={4} />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label='Khách hàng' name='customerCode'>
                  <Select placeholder='-- Vui lòng chọn --' />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label='NV kinh doanh' name='supervisor'>
                  <Input />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label='Địa chỉ' name='address'>
                  <Select placeholder='-- Vui lòng chọn --' />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label='Liên hệ' name='assignedEmployee'>
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='SĐT liên hệ' name='phone'>
                  <Input />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='Email' name='email'>
                  <Input />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={6}>
                <Form.Item label='Khu vực' name='area'>
                  <Select placeholder='-- Vui lòng chọn --' />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='Tỉnh/Thành phố' name='province'>
                  <Select placeholder='-- Vui lòng chọn --' />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='Quận/Huyện' name='district'>
                  <Select placeholder='-- Vui lòng chọn --' />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label='Phường/Xã' name='ward'>
                  <Select placeholder='-- Vui lòng chọn --' />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item label='Địa điểm ghé thăm' name='visitLocation'>
              <Input />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label='lat' name='lat'>
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label='lng' name='lng'>
                  <Input />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item label='Các khách hàng trong khu vực'>
              <Button>Xem thông tin</Button>
            </Form.Item>

            <Form.Item label='Yêu cầu checkin' name='requireCheckin'>
              <Radio.Group>
                <Radio value={true}>Có</Radio>
                <Radio value={false}>Không</Radio>
              </Radio.Group>
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label='Ngày dự kiến' name='dueDate'>
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label='Ngày TH thực tế' name='actualDate'>
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item label='Phân công cho' name='function'>
              <Radio.Group>
                <Radio value='Cá nhân'>Cá nhân</Radio>
                <Radio value='Nhóm'>Nhóm</Radio>
                <Radio value='Mở rộng'>Mở rộng</Radio>
              </Radio.Group>
            </Form.Item>

            <Form.Item label='NV được phân công' name='assignedEmployee'>
              <Select placeholder='-- Chọn người thực hiện --' />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Button type='primary' style={{ marginBottom: 8 }}>
              Lấy vị trí hiện tại
            </Button>
            <div style={{ border: '1px solid #d9d9d9', height: 480 }}>
              <iframe
                title='map'
                width='100%'
                height='100%'
                frameBorder='0'
                src='https://www.google.com/maps/embed/v1/place?q=Tân+Bình+Hồ+Chí+Minh&key=YOUR_API_KEY'
                allowFullScreen
              />
            </div>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24} style={{ textAlign: 'center' }}>
            <Button type='primary'>Lưu</Button>
          </Col>
        </Row>
      </Form>
    </div>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo mới thăm hỏi khách hàng'
      description='Tạo mới thăm hỏi khách hàng'
      childrenBody={modalContent}
    />
  )
}

export default CreateCustomerSupportModal
