import { SaveOutlined, PlusOutlined } from "@ant-design/icons";
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Switch,
  Upload,
  message,
} from "antd";
import BaseModal from "~/components/BaseModal";
import { useForm } from "antd/es/form/Form";
import { useState } from "react";
import { EProduct } from "~/common/enums/NSProduct";
import { useCreateProduct } from "~/hooks/product/useCreateProduct";
import { CreateProductReq } from "~/dto/product.dto";
import { toastService } from "~/services";
import type { UploadFile, UploadProps } from "antd";
import useUploadMutiple from "~/hooks/uploadFile/useUploadMutiple";
import useUploadSingle from "~/hooks/uploadFile/useUploadSingle";

const { Option } = Select;
const { TextArea } = Input;

interface CreateProductModalProps {
  open: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

const CreateProductModal = ({
  open,
  onClose,
  onSuccess,
}: CreateProductModalProps) => {
  const [form] = useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const { mutateAsync: createProduct, isPending } = useCreateProduct();

  const { mutateAsync: uploadSingle, isPending: isUploadingSingle } =
    useUploadSingle();

  const handleSave = async (values: CreateProductReq) => {
    if (!values) return;

    const body = {
      ...values,
      images: fileList.map((file) => file.url),
    };

    try {
      await createProduct(body);
      onClose();
      onSuccess?.();
      // Reset form and file list
      form.resetFields();
      setFileList([]);
    } catch (error) {
      toastService.error("Tạo product thất bại");
    }
  };

  // Handle image upload
  const handleUploadChange = (res: any) => {
    if (res.Location) {
      setFileList((curr) => {
        return [
          ...curr,
          {
            uid: res.Location,
            name: res.Location,
            status: "done",
            url: res.Location,
          },
        ];
      });
    }
  };

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith("image/");
    if (!isImage) {
      message.error("Chỉ được upload file hình ảnh!");
      return false;
    }

    const isLt5M = file.size / 1024 / 1024 < 5;
    if (!isLt5M) {
      message.error("Hình ảnh phải nhỏ hơn 5MB!");
      return false;
    }

    return true;
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  const modalContent = (
    <Form form={form} layout="vertical" onFinish={handleSave}>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="Tên sản phẩm"
            name="name"
            rules={[{ required: true, message: "Vui lòng nhập tên sản phẩm" }]}
          >
            <Input placeholder="Tên sản phẩm" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Tiêu đề"
            name="title"
            rules={[{ required: true, message: "Vui lòng nhập tiêu đề" }]}
          >
            <Input placeholder="Tiêu đề" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="Số lượng tối đa"
            name="defaultMaxUsers"
            rules={[
              { required: true, message: "Vui lòng nhập số lượng tối đa" },
              { type: "number", min: 1, message: "Phải lớn hơn 0" },
            ]}
          >
            <InputNumber
              style={{ width: "100%" }}
              placeholder="Số lượng tối đa"
              min={1}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="Mô tả" name="description">
            <TextArea rows={3} placeholder="Mô tả sản phẩm" />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label="Hình ảnh sản phẩm" name="images">
        <Upload
          listType="picture-card"
          fileList={fileList}
          onChange={handleUploadChange}
          beforeUpload={beforeUpload}
          multiple
          accept="image/*"
          customRequest={async ({ file, onSuccess }) => {
            // Simulate upload success for demo
            // In real app, you would upload to your server here
            try {
              const formData = new FormData();
              formData.append("file", file as File);

              await uploadSingle(formData).then((res) => {
                handleUploadChange(res);
              });
            } catch (error) {}
          }}
        >
          {fileList.length >= 8 ? null : uploadButton}
        </Upload>
      </Form.Item>

      <Row gutter={24}>
        <Col span={24}>
          <Form.Item label="Ghi chú" name="note">
            <TextArea rows={2} placeholder="Ghi chú" />
          </Form.Item>
        </Col>
      </Row>

      <div
        style={{
          textAlign: "right",
          marginTop: 24,
          borderTop: "1px solid #f0f0f0",
          paddingTop: 16,
        }}
      >
        <Button onClick={onClose} style={{ marginRight: 8 }}>
          Hủy
        </Button>
        <Button
          type="primary"
          htmlType="submit"
          icon={<SaveOutlined />}
          loading={isPending}
        >
          Tạo sản phẩm
        </Button>
      </div>
    </Form>
  );

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title="Tạo sản phẩm mới"
      description="Thêm sản phẩm mới vào hệ thống"
      childrenBody={modalContent}
    />
  );
};

export default CreateProductModal;
