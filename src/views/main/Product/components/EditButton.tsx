import { EditOutlined, SaveOutlined, UploadOutlined } from "@ant-design/icons";
import {
  Card,
  Tag,
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Upload,
  message,
} from "antd";
import BaseButton from "~/components/BaseButton";
import BaseText from "~/components/BaseText";
import { useModal } from "../../../../hooks/useModal";
import BaseModal from "~/components/BaseModal";
import { IProduct } from "~/dto/product.dto";
import { EProduct } from "~/common/enums/NSProduct";
import { useEffect, useState } from "react";
import { useForm } from "antd/es/form/Form";
import {
  useUpdateProduct,
  UseUpdateProductParams,
} from "~/hooks/product/useUpdateProduct";
import useUploadSingle from "~/hooks/uploadFile/useUploadSingle";
import type { UploadFile, UploadProps } from "antd";
import { toastService } from "~/services/@common";
import { useDetailProduct } from "~/hooks/product/useDetailProduct";

const { Option } = Select;
const { TextArea } = Input;

interface EditButtonProps {
  data: IProduct;
  onSuccess?: () => void;
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { id } = data;
  const { open, openModal, closeModal } = useModal();
  const [form] = useForm();
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const { mutateAsync: updateProduct, isPending } = useUpdateProduct();
  const { mutateAsync: uploadSingle, isPending: isUploadingSingle } =
    useUploadSingle();

  const { data: detailData, isLoading } = useDetailProduct(id);

  const {
    name,
    title,
    description,
    defaultMaxUsers,
    note,
    status,
    type,
    customerUsed,
    createdDate,
    updatedDate,
    version,
    media,
  } = detailData || {};

  useEffect(() => {
    if (open && detailData) {
      form.setFieldsValue({
        name,
        title,
        description,
        defaultMaxUsers: Number(defaultMaxUsers),
        note,
      });
      // Set existing images to fileList
      if (media && media.length > 0) {
        const existingFiles: UploadFile[] = media.map((media, index) => ({
          uid: media.id || `-${index}`,
          name: `image-${index + 1}`,
          status: "done",
          url: media.imageUrl,
        }));
        setFileList(existingFiles);
      } else {
        setFileList([]);
      }
    }
  }, [open, detailData, form]);

  if (!detailData) return null;

  const handleSave = async (values: UseUpdateProductParams) => {
    if (!data) return;
    const body = {
      ...values,
      id: detailData?.id,
      images: fileList.map((file) => file.url),
    };

    try {
      await updateProduct(body);
      closeModal();
      onSuccess && onSuccess();
      // Reset form and file list
      form.resetFields();
      setFileList([]);
    } catch (error) {
      toastService.error("Cập nhật product thất bại");
    }
  };

  // Handle image upload
  const handleUploadChange = (res: any) => {
    if (res.Location) {
      setFileList((curr) => {
        return [
          ...curr,
          {
            uid: res.Location,
            name: res.Location,
            status: "done",
            url: res.Location,
          },
        ];
      });
    }
  };

  const handleRemove = (file: UploadFile) => {
    setFileList((prev) => prev.filter((f) => f.uid !== file.uid));
  };

  const beforeUpload = (file: File) => {
    const isImage = file.type.startsWith("image/");
    if (!isImage) {
      message.error("Chỉ được upload file hình ảnh!");
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error("Hình ảnh phải nhỏ hơn 2MB!");
    }
    return isImage && isLt2M;
  };

  const uploadButton = (
    <div>
      <UploadOutlined />
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  // Header thông tin product
  const productHeader = (
    <Card style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={8}>
          <div>
            <BaseText color="textSecondary">Tên sản phẩm:</BaseText>
            <br />
            <BaseText weight="bold">{name || "N/A"}</BaseText>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color="textSecondary">Loại sản phẩm:</BaseText>
            <br />
            <Tag color={EProduct.EProductType[type]?.color}>
              {EProduct.EProductType[type]?.name}
            </Tag>
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color="textSecondary">Trạng thái:</BaseText>
            <br />
            <Tag color={EProduct.EProductStatus[status]?.color}>
              {EProduct.EProductStatus[status]?.name}
            </Tag>
          </div>
        </Col>
      </Row>
      <Row style={{ marginTop: 12 }}>
        <Col span={8}>
          <BaseText color="textSecondary">Khách hàng sử dụng:</BaseText>
          <br />
          <BaseText weight="bold">{customerUsed || 0}</BaseText>
        </Col>
        <Col span={8}>
          <BaseText color="textSecondary">Hình ảnh:</BaseText>
          <br />
          <BaseText weight="bold">{data.media?.length || 0} ảnh</BaseText>
        </Col>
      </Row>
    </Card>
  );

  const modalContent = (
    <div>
      {productHeader}

      <Form form={form} layout="vertical" onFinish={handleSave}>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Tên sản phẩm"
              name="name"
              rules={[
                { required: true, message: "Vui lòng nhập tên sản phẩm" },
              ]}
            >
              <Input placeholder="Product name" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Tiêu đề"
              name="title"
              rules={[{ required: true, message: "Vui lòng nhập tiêu đề" }]}
            >
              <Input placeholder="Product title" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              label="Số lượng tối đa"
              name="defaultMaxUsers"
              rules={[
                { required: true, message: "Vui lòng nhập số lượng tối đa" },
                { type: "number", min: 1, message: "Phải lớn hơn 0" },
              ]}
            >
              <InputNumber
                style={{ width: "100%" }}
                placeholder="Số lượng tối đa"
                min={1}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item label="Mô tả" name="description">
              <TextArea rows={3} placeholder="Mô tả sản phẩm" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item label="Hình ảnh sản phẩm" name="images">
          <Upload
            listType="picture-card"
            fileList={fileList}
            beforeUpload={beforeUpload}
            multiple
            onChange={handleUploadChange}
            onRemove={handleRemove}
            accept="image/*"
            customRequest={async ({ file, onSuccess }) => {
              // Simulate upload success for demo
              // In real app, you would upload to your server here
              try {
                const formData = new FormData();
                formData.append("file", file as File);

                await uploadSingle(formData).then((res) => {
                  handleUploadChange(res);
                });
              } catch (error) {
                console.log("======uploadSingle=====>>", error);
              }
            }}
          >
            {fileList.length >= 8 ? null : uploadButton}
          </Upload>
          <div style={{ color: "#666", fontSize: "12px", marginTop: "8px" }}>
            Tối đa 8 hình ảnh, mỗi file nhỏ hơn 5MB
          </div>
        </Form.Item>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item label="Ghi chú" name="note">
              <TextArea rows={2} placeholder="Ghi chú" />
            </Form.Item>
          </Col>
        </Row>

        <div
          style={{
            textAlign: "right",
            marginTop: 24,
            borderTop: "1px solid #f0f0f0",
            paddingTop: 16,
          }}
        >
          <Button onClick={closeModal} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SaveOutlined />}
            loading={isPending}
          >
            Cập nhật sản phẩm
          </Button>
        </div>
      </Form>
    </div>
  );

  return (
    <>
      <BaseButton
        icon={<EditOutlined />}
        onClick={openModal}
        type="primary"
        tooltip="Chỉnh sửa"
      />
      <BaseModal
        open={open}
        onClose={closeModal}
        title="Chỉnh sửa sản phẩm"
        description="Cập nhật thông tin sản phẩm"
        childrenBody={modalContent}
      />
    </>
  );
};

export default EditButton;
