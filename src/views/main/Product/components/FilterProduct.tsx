import { <PERSON><PERSON>, Col, Collapse, Form, Input, Row, Select } from "antd";
import { FC, useCallback } from "react";
import { ReloadOutlined, SearchOutlined } from "@ant-design/icons";
import { EProduct, NSProduct } from "~/common/enums/NSProduct";

interface IProps {
  onFilter: (values: IFilterProduct) => void;
  onReset: () => void;
  isLoading: boolean;
}
interface IFilterProduct {
  productName: string;
  version: string;
  status: string;
}

const FilterProduct: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading } = props;
  const [form] = Form.useForm();

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue());
  }, [form, onFilter]);

  const handleReset = useCallback(() => {
    form.resetFields();
    onReset();
  }, [form, onReset]);

  return (
    <Collapse>
      <Collapse.Panel header="Tìm kiếm" key="0">
        <Form form={form} layout="vertical">
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item
                initialValue={null}
                label="Tên sản phẩm"
                name="productName"
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item initialValue={null} label="Phiên bản" name="version">
                <Input />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item initialValue={null} label="Trạng thái" name="status">
                <Select>
                  {Object.values(EProduct.EProductStatus).map(
                    (status: any, index: number) => (
                      <Select.Option key={index} value={status.code}>
                        {status.name}
                      </Select.Option>
                    )
                  )}
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label=" ">
                <div
                  style={{
                    display: "flex",
                    gap: 10,
                  }}
                >
                  <Button
                    type="primary"
                    style={{ width: "50%" }}
                    htmlType="submit"
                    onClick={handleFilter}
                    loading={isLoading}
                  >
                    <SearchOutlined />
                    Tìm kiếm
                  </Button>
                  <Button
                    type="default"
                    style={{ width: "50%" }}
                    htmlType="submit"
                    onClick={handleReset}
                  >
                    <ReloadOutlined />
                    Làm mới
                  </Button>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  );
};

export default FilterProduct;
