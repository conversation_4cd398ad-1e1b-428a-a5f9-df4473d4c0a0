import { useState, FC } from "react";
import { Col, Row, Tag } from "antd";
import type { ColumnsType } from "antd/es/table";
import BaseView from "~/components/BaseView";
import { DeleteOutlined } from "@ant-design/icons";
import { toastService } from "~/services";
import { IProduct } from "~/dto/product.dto";
import BaseButton from "~/components/BaseButton";
import DetailButton from "./components/DetailButton";
import { useListProduct } from "~/hooks/product/useListProduct";
import BaseTable from "~/components/BaseTable";
import EditButton from "./components/EditButton";
import { EProduct } from "~/common/enums/NSProduct";
import FilterProduct from "./components/FilterProduct";
import { useDeleteProduct } from "~/hooks/product/useDeleteProduct";

interface IFilterProduct {
  productName: string;
  version: string;
  status: string;
  pageIndex: number;
  pageSize: number;
}

type IProps = {};

export const ListProductView: FC<IProps> = (props: IProps) => {
  const [filter, setFilter] = useState<IFilterProduct>({
    productName: "",
    version: "",
    status: "",
    pageIndex: 1,
    pageSize: 10,
  });

  const { data, total, isLoading } = useListProduct(filter);

  const { mutateAsync: deleteProduct, isPending: isDeletePending } =
    useDeleteProduct();

  const handleFilter = (values: IFilterProduct) => {
    setFilter(values);
  };

  const handleReset = () => {
    setFilter({
      productName: "",
      version: "",
      status: "",
      pageIndex: 1,
      pageSize: 10,
    });
  };

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize,
    });
  };

  const handleDelete = async (item: IProduct) => {
    try {
      await deleteProduct({
        id: item.id,
      });
    } catch (error) {
      toastService.handleError(error);
    }
  };

  const columns: ColumnsType<IProduct> = [
    {
      title: "Tên sản phẩm",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Phiên bản",
      dataIndex: "version",
      key: "version",
      width: 100,
    },
    {
      title: "Số KH sử dụng",
      dataIndex: "customerUsed",
      key: "customerUsed",
      width: 150,
    },
    {
      title: "Trạng thái",
      dataIndex: "statusName",
      key: "statusName",
      render: (value: string, record: IProduct) => {
        return (
          <Tag
            color={EProduct.EProductStatus[record.status]?.color}
            style={{ fontSize: "14px", padding: "4px 12px" }}
          >
            {record.status?.toUpperCase() || "UNKNOWN"}
          </Tag>
        );
      },
    },
    {
      title: "Tác vụ",
      key: "action",
      width: 200,
      render: (value: any, record: any, index: number) => {
        return (
          <>
            <DetailButton data={value} />
            <EditButton data={value} />

            <BaseButton
              danger
              type="primary"
              shape="circle"
              icon={<DeleteOutlined />}
              tooltip="Delete"
              onClick={() => handleDelete(record)}
            />
          </>
        );
      },
    },
  ];

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24}>
          <FilterProduct
            onFilter={handleFilter}
            onReset={handleReset}
            isLoading={isLoading}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={data}
            total={total}
            isLoading={isLoading}
            onPageChange={handlePageChange}
          />
        </Col>
      </Row>
    </BaseView>
  );
};
