import { FC } from 'react'
import { useNavigate } from 'react-router-dom'
import { Row, Col, Typography } from 'antd'
import { ScheduleOutlined, ProjectOutlined } from '@ant-design/icons'
import BaseText from '~/components/BaseText'
import { useThemeStore } from '~/stores/themeStore'
import { COLORS } from '~/common/constants'

const { Text } = Typography

type HeaderProps = {}

const iconCircleStyle: React.CSSProperties = {
  width: 64,
  height: 64,
  borderRadius: '50%',
  background: '#3a465e',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  margin: '0 auto 12px auto'
}

const boxStyle: React.CSSProperties = {
  background: '#f7f9fc',
  borderRadius: 12,
  padding: '15px 0',
  textAlign: 'center',
  cursor: 'pointer',
  boxShadow: '0 2px 8px rgba(58, 70, 94, 0.10)',
  transition: 'box-shadow 0.2s, transform 0.2s',
  border: `1px solid #e3e8f0`,
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'center',
  justifyContent: 'center'
}

const Header: FC<HeaderProps> = () => {
  const navigate = useNavigate()
  const { themeStyle, changeTheme } = useThemeStore()
  const newTheme = themeStyle === 'dark' ? 'light' : 'dark'

  return (
    <div style={{ marginBottom: 24 }}>
      <BaseText
        style={{
          fontWeight: 700,
          marginBottom: 24,
          display: 'block',
          letterSpacing: 0.5,
          textTransform: 'uppercase'
        }}>
        Nhóm chức năng
      </BaseText>
      <Row gutter={[24, 24]}>
        <Col xs={24} sm={12}>
          <div
            style={boxStyle}
            onClick={() => navigate('/customer-management/list-customer')}
            tabIndex={0}
            role='button'
            aria-label='Đi tới Khách Hàng'>
            <div style={iconCircleStyle}>
              <ScheduleOutlined style={{ fontSize: 32, color: '#fff' }} />
            </div>
            <span>Khách Hàng</span>
          </div>
        </Col>
        <Col xs={24} sm={12}>
          <div
            style={boxStyle}
            onClick={() => navigate('/complaint-management')}
            tabIndex={0}
            role='button'
            aria-label='Đi tới Xử lý khiếu nại'>
            <div style={iconCircleStyle}>
              <ProjectOutlined style={{ fontSize: 32, color: '#fff' }} />
            </div>
            <span>Xử lý khiếu nại</span>
          </div>
        </Col>
      </Row>
    </div>
  )
}

export default Header
