import { FC } from 'react'
import { Collapse, Form, Row, Col, Button, Select } from 'antd'
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons'

interface IFilterBySalesRepReport {
  salesRep?: string
  department?: string
  pageIndex: number
  pageSize: number
}

type IProps = {
  onFilter: (values: IFilterBySalesRepReport) => void
  onReset: () => void
  isLoading?: boolean
}

export const FilterBySalesRepReport: FC<IProps> = ({
  onFilter,
  onReset,
  isLoading
}) => {
  const [form] = Form.useForm()

  const handleSubmit = (values: IFilterBySalesRepReport) => {
    onFilter({
      ...values,
      pageIndex: 1,
      pageSize: 10
    })
  }

  const handleReset = () => {
    form.resetFields()
    onReset()
  }

  // Mock sales representative options - replace with actual API data
  const salesRepOptions = [
    { label: 'Nguyễn Văn A', value: 'sales_rep_1' },
    { label: 'Trần Thị B', value: 'sales_rep_2' },
    { label: 'Lê Văn C', value: 'sales_rep_3' },
    { label: 'Phạm Thị D', value: 'sales_rep_4' },
    { label: 'Hoàng Văn E', value: 'sales_rep_5' }
  ]

  // Mock department options - replace with actual API data
  const departmentOptions = [
    { label: 'Kinh doanh', value: 'sales' },
    { label: 'Marketing', value: 'marketing' },
    { label: 'Chăm sóc khách hàng', value: 'customer_service' },
    { label: 'Phát triển kinh doanh', value: 'business_development' },
    { label: 'Quản lý khách hàng', value: 'account_management' }
  ]

  return (
    <Collapse>
      <Collapse.Panel header='Tìm kiếm' key='0'>
        <Form
          form={form}
          onFinish={handleSubmit}
          layout='vertical'
          className='filter-form'>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name='salesRep'
                label='Nhân viên kinh doanh'
                rules={[{ required: false }]}>
                <Select
                  placeholder='Chọn nhân viên kinh doanh'
                  allowClear
                  options={salesRepOptions}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name='department'
                label='Phòng ban'
                rules={[{ required: false }]}>
                <Select
                  placeholder='Chọn phòng ban'
                  allowClear
                  options={departmentOptions}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item label=' '>
                <div
                  style={{
                    display: 'flex',
                    gap: 10,
                    justifyContent: 'flex-start'
                  }}>
                  <Button
                    type='primary'
                    htmlType='submit'
                    onClick={() => handleSubmit(form.getFieldsValue())}
                    loading={isLoading}>
                    <SearchOutlined />
                    Tìm kiếm
                  </Button>
                  <Button
                    type='default'
                    htmlType='button'
                    onClick={() => handleReset()}>
                    <ReloadOutlined />
                    Làm mới
                  </Button>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  )
}
