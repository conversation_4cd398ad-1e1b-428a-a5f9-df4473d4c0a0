import { Row, Col, Typography } from 'antd'
import { UserOutlined, IdcardOutlined, PhoneOutlined } from '@ant-design/icons'
import BaseText from '~/components/BaseText'
import { COLORS } from '~/common/constants'

const { Text } = Typography

const cardData = [
  {
    label: 'KHÁCH HÀNG',
    value: '325,826',
    description: 'Tổng số khách hàng trên toàn hệ thống',
    color: '#2563eb',
    icon: <UserOutlined style={{ fontSize: 32, color: COLORS.WHITE }} />
  },
  {
    label: 'LIÊN HỆ',
    value: '76,544',
    description: 'Tổng số thông tin liên hệ',
    color: '#14b8a6',
    icon: <IdcardOutlined style={{ fontSize: 32, color: COLORS.WHITE }} />
  },
  {
    label: 'CÔNG VIỆC',
    value: '21,267',
    description: 'Tổng số công việc đang chờ xử lý',
    color: '#8b5cf6',
    icon: <PhoneOutlined style={{ fontSize: 32, color: COLORS.WHITE }} />
  }
]

const InfomationBroad = () => {
  return (
    <div style={{ marginBottom: 24 }}>
      <BaseText
        style={{
          fontWeight: 700,
          marginBottom: 20,
          display: 'block',
          letterSpacing: 0.5,
          textTransform: 'uppercase'
        }}>
        Bảng thông tin
      </BaseText>
      <Row gutter={[16, 16]}>
        {cardData.map((card) => (
          <Col xs={24} sm={12} md={8} key={card.label}>
            <div
              style={{
                background: card.color,
                borderRadius: 8,
                padding: '24px 20px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                cursor: 'pointer',
                minHeight: 110,
                boxShadow: '0 2px 8px rgba(0,0,0,0.04)',
                transition: 'box-shadow 0.2s, transform 0.2s'
              }}>
              <Col style={{ display: 'flex', flexDirection: 'column' }}>
                <Text
                  style={{
                    color: COLORS.WHITE,
                    fontWeight: 600,
                    textTransform: 'uppercase'
                  }}>
                  {card.label}
                </Text>
                <Text
                  style={{
                    color: COLORS.WHITE,
                    fontWeight: 600,
                    fontSize: 24,
                    lineHeight: '32px'
                  }}>
                  {card.value}
                </Text>
                <Text
                  style={{ color: COLORS.WHITE, fontSize: 12, marginTop: 4 }}>
                  {card.description}
                </Text>
              </Col>
              <div>{card.icon}</div>
            </div>
          </Col>
        ))}
      </Row>
    </div>
  )
}

export default InfomationBroad
