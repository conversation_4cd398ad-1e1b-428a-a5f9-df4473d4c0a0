import { FC } from 'react'
import {
  Collapse,
  Form,
  Input,
  Row,
  Col,
  Button,
  Select,
  InputNumber
} from 'antd'
import { SearchOutlined, ReloadOutlined } from '@ant-design/icons'
import { IFilterRevenueReport } from '~/dto/report.dto'

type IProps = {
  onFilter: (values: IFilterRevenueReport) => void
  onReset: () => void
  isLoading?: boolean
}

export const FilterRevenueReport: FC<IProps> = ({
  onFilter,
  onReset,
  isLoading
}) => {
  const [form] = Form.useForm()

  const handleSubmit = (values: IFilterRevenueReport) => {
    onFilter({
      ...values,
      pageIndex: 1,
      pageSize: 10
    })
  }

  const handleReset = () => {
    form.resetFields()
    onReset()
  }

  // Mock company options - replace with actual API data
  const companyOptions = [
    { label: 'Công ty TNHH ABC', value: 'company_1' },
    { label: 'Công ty Cổ phần XYZ', value: 'company_2' },
    { label: '<PERSON><PERSON>h nghiệp Tư nhân DEF', value: 'company_3' },
    { label: 'Công ty TNHH GHI', value: 'company_4' },
    { label: 'Công ty Cổ phần JKL', value: 'company_5' }
  ]

  // Display rows options
  const displayRowsOptions = [
    { label: '10 dòng', value: 10 },
    { label: '20 dòng', value: 20 },
    { label: '50 dòng', value: 50 },
    { label: '100 dòng', value: 100 }
  ]

  return (
    <Collapse>
      <Collapse.Panel header='Tìm kiếm' key='0'>
        <Form
          form={form}
          onFinish={handleSubmit}
          layout='vertical'
          className='filter-form'>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name='company'
                label='Công ty'
                rules={[{ required: false }]}>
                <Select
                  placeholder='Chọn công ty'
                  allowClear
                  options={companyOptions}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name='customerCode'
                label='Mã khách hàng'
                rules={[{ required: false }]}>
                <Input placeholder='Nhập mã khách hàng' />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name='phone'
                label='Điện thoại'
                rules={[{ required: false }]}>
                <Input placeholder='Nhập số điện thoại' />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name='creditLimitFrom'
                label='Hạn mức từ'
                rules={[{ required: false }]}>
                <InputNumber
                  placeholder='Nhập hạn mức từ'
                  style={{ width: '100%' }}
                  formatter={(value) =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name='creditLimitTo'
                label='Hạn mức đến'
                rules={[{ required: false }]}>
                <InputNumber
                  placeholder='Nhập hạn mức đến'
                  style={{ width: '100%' }}
                  formatter={(value) =>
                    `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value!.replace(/\$\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name='pageSize'
                label='Số dòng hiển thị'
                rules={[{ required: false }]}>
                <Select
                  placeholder='Chọn số dòng hiển thị'
                  allowClear
                  options={displayRowsOptions}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label=' '>
                <div
                  style={{
                    display: 'flex',
                    gap: 10
                  }}>
                  <Button
                    type='primary'
                    style={{ width: '50%' }}
                    htmlType='submit'
                    onClick={() => handleSubmit(form.getFieldsValue())}
                    loading={isLoading}>
                    <SearchOutlined />
                    Tìm kiếm
                  </Button>
                  <Button
                    type='default'
                    style={{ width: '50%' }}
                    htmlType='submit'
                    onClick={() => handleReset()}>
                    <ReloadOutlined />
                    Làm mới
                  </Button>
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Collapse.Panel>
    </Collapse>
  )
}
