import { Card, Row, Col, Select, Table, Tag, Space } from 'antd'
import { Bar, Doughnut } from 'react-chartjs-2'
import {
  Chart,
  BarElement,
  CategoryScale,
  LinearScale,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js'
import { getColorPercent, showValueOnBar } from '~/common/utils/common.utils'
import { title } from 'process'

Chart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend, ArcElement)
Chart.register(showValueOnBar)

const csKPI = {
  labels: [
    'Số Ticket xử lý',
    '<PERSON><PERSON> lượng nhiệm vụ hoàn thành',
    '<PERSON><PERSON><PERSON> độ hài lòng khách hàng',
    'Ticket Quá hạn',
    'Tỷ lệ cập nhật trạng thái'
  ],
  datasets: [
    {
      label: '% Tháng trước',
      backgroundColor: '#FFB823',
      data: [100, 6, 90, 5, 95]
    },
    {
      label: '% Tháng hiện tại',
      backgroundColor: '#708A58',
      data: [51, 80, 80, 80, 18]
    }
  ]
}

const options = {
  plugins: {
    legend: {
      position: 'top' as const
    }
  },
  responsive: true,
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const { Option } = Select

export const EmployeeSCKPIChart = () => {
  const columns = [
    {
      title: 'KPI',
      dataIndex: 'kpi',
      key: 'kpi'
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Đơn vị đo',
      dataIndex: 'unit',
      key: 'unit'
    },
    {
      title: 'Mục tiêu',
      dataIndex: 'target',
      key: 'target'
    },
    {
      title: 'Kết quả đạt được',
      dataIndex: 'result',
      key: 'result'
    },
    {
      title: 'Tỷ lệ hoàn thành',
      dataIndex: 'completionRate',
      key: 'completionRate',
      render: (text: number) => (
        <Tag color={getColorPercent(text)}>
          <strong>{text}%</strong>
        </Tag>
      )
    },
    {
      title: 'Tổng KPI',
      dataIndex: 'total',
      key: 'total',
      render: (_text: any, _row: any, index: number) => {
        if (index === 0) {
          return {
            children: (
              <Tag color={getColorPercent(61)}>
                <strong>61%</strong>
              </Tag>
            ),
            props: {
              rowSpan: 5 // Số hàng bạn muốn merge (ở đây là 5 dòng)
            }
          }
        }

        return {
          children: null,
          props: {
            rowSpan: 0
          }
        }
      }
    }
  ]

  const data = [
    {
      key: '1',
      kpi: 'Số ticket xử lý',
      description: 'Số lượng yêu cầu / khiếu nại xử lý thành công',
      unit: 'ticket',
      target: 100,
      result: 51,
      completionRate: '51'
    },
    {
      key: '2',
      kpi: 'Số lượng nhiệm vụ hoàn thành',
      description: 'Số lượng nhiệm vụ hoàn thành',
      unit: 'ticket',
      target: 50,
      result: 40,
      completionRate: '80'
    },
    {
      key: '3',
      kpi: 'Mức độ hài lòng khách hàng',
      description: 'Khảo sát sau xử lý (CSAT), điểm trung bình trên thang điểm 5',
      unit: 'điểm',
      target: 5,
      result: 4,
      completionRate: '80'
    },
    {
      key: '4',
      kpi: 'Ticket quá hạn',
      description: 'Số lượng ticket xử lý trễ hạn',
      unit: 'ticket',
      target: 5,
      result: 4,
      completionRate: '80'
    },
    {
      key: '5',
      kpi: 'Cập nhật trạng thái ticket',
      description: 'Ghi nhận trạng thái ticket kịp thời',
      unit: 'ticket',
      target: 85,
      result: 15,
      completionRate: '18'
    }
  ]
  return (
    <Card title='KPI nhóm nhân viên Chăm sóc khách hàng' size='small' style={{ marginTop: 24 }}>
      <Space>
        <Select style={{ width: 200, marginBottom: 16 }} placeholder='Chọn năm'>
          <Option value=''>--Chọn năm--</Option>
          <Option value='2021'>2021</Option>
          <Option value='2022'>2022</Option>
          <Option value='2023'>2023</Option>
        </Select>
        <Select style={{ width: 200, marginBottom: 16 }} placeholder='Chọn quý'>
          <Option value=''>--Chọn quý--</Option>
          <Option value='1'>Quý 1</Option>
          <Option value='2'>Quý 2</Option>
          <Option value='3'>Quý 3</Option>
          <Option value='3'>Quý 4</Option>
        </Select>
        <Select style={{ width: 200, marginBottom: 16 }} placeholder='Chọn tháng trước'>
          <Option value=''>--Chọn tháng--</Option>
          <Option value='1'>Tháng 1</Option>
          <Option value='2'>Tháng 2</Option>
          <Option value='3'>Tháng 3</Option>
          <Option value='3'>Tháng 4</Option>
          <Option value='3'>Tháng 5</Option>
        </Select>
      </Space>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <div
            style={{
              width: '100%',
              height: '430px',
              padding: '20px',
              backgroundColor: '#fff',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}>
            <Bar data={csKPI} options={options} />
          </div>
        </Col>
        <Col xs={24} lg={12}>
          <Table columns={columns} dataSource={data} pagination={false} size='small' bordered />
        </Col>
      </Row>
    </Card>
  )
}
