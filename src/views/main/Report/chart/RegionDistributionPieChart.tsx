import React from 'react'
import { Pie } from 'react-chartjs-2'
import { Chart as ChartJS, ArcElement, Tooltip, Legend, Title } from 'chart.js'

ChartJS.register(ArcElement, Tooltip, Legend, Title)

interface RegionDistributionPieChartProps {
  data?: {
    labels: string[]
    datasets: {
      data: number[]
      backgroundColor: string[]
      borderColor: string[]
      borderWidth: number
    }[]
  }
}

const RegionDistributionPieChart: React.FC<RegionDistributionPieChartProps> = ({
  data
}) => {
  // Dữ liệu mẫu - có thể thay thế bằng data từ props
  const defaultData = {
    labels: [
      '<PERSON>hu vực Đ<PERSON> Nam Bộ',
      '<PERSON>hu vực <PERSON>',
      '<PERSON>hu vực <PERSON>',
      'Nước ngoài'
    ],
    datasets: [
      {
        data: [45, 30, 15, 10],
        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
        borderColor: ['#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0'],
        borderWidth: 2
      }
    ]
  }

  const chartData = data || defaultData

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          padding: 20,
          usePointStyle: true,
          font: {
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: 'Khách hàng theo khu vực',
        font: {
          size: 16,
          weight: 'bold' as const
        },
        padding: {
          top: 10,
          bottom: 20
        }
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            const label = context.label || ''
            const value = context.parsed
            const total = context.dataset.data.reduce(
              (a: number, b: number) => a + b,
              0
            )
            const percentage = ((value / total) * 100).toFixed(1)
            return `${label}: ${value} (${percentage}%)`
          }
        }
      }
    }
  }

  return (
    <div
      style={{
        width: '100%',
        height: '450px',
        padding: '20px',
        backgroundColor: '#fff',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
      <Pie data={chartData} options={options} />
    </div>
  )
}

export default RegionDistributionPieChart
