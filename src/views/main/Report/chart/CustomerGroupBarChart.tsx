import React from 'react'
import { Bar } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

interface CustomerGroupBarChartProps {
  data?: {
    labels: string[]
    datasets: {
      label: string
      data: number[]
      backgroundColor: string[]
      borderColor: string[]
      borderWidth: number
    }[]
  }
}

const CustomerGroupBarChart: React.FC<CustomerGroupBarChartProps> = ({
  data
}) => {
  // Dữ liệu mẫu - có thể thay thế bằng data từ props
  const defaultData = {
    labels: [
      'Nội địa',
      '<PERSON>ất khẩu',
      '<PERSON><PERSON> hai'
    ],
    datasets: [
      {
        label: 'Số lượng',
        data: [150, 120, 80, 95, 60],
        backgroundColor: [
          'rgba(255, 99, 132, 0.8)',
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 206, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
          'rgba(153, 102, 255, 0.8)'
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)'
        ],
        borderWidth: 2
      }
    ]
  }

  const chartData = data || defaultData

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: 'Khách hàng theo thị trường',
        font: {
          size: 16,
          weight: 'bold' as const
        },
        padding: {
          top: 10,
          bottom: 20
        }
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            return `${context.dataset.label}: ${context.parsed.y}`
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Số lượng khách hàng',
          font: {
            size: 12,
            weight: 'bold' as const
          }
        },
        ticks: {
          stepSize: 20
        }
      },
      x: {
        title: {
          display: true,
          text: 'Thị trường',
          font: {
            size: 12,
            weight: 'bold' as const
          }
        },
        ticks: {
          maxRotation: 45,
          minRotation: 0
        }
      }
    }
  }

  return (
    <div
      style={{
        width: '100%',
        height: '450px',
        padding: '20px',
        backgroundColor: '#fff',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
      <Bar data={chartData} options={options} />
    </div>
  )
}

export default CustomerGroupBarChart
