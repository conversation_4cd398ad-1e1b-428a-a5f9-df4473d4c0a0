import React from 'react'
import { Bar } from 'react-chartjs-2'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js'

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

interface TopProvincesBarChartProps {
  data?: {
    labels: string[]
    datasets: {
      label: string
      data: number[]
      backgroundColor: string[]
      borderColor: string[]
      borderWidth: number
    }[]
  }
}

const TopProvincesBarChart: React.FC<TopProvincesBarChartProps> = ({
  data
}) => {
  // Dữ liệu mẫu - có thể thay thế bằng data từ props
  const defaultData = {
    labels: [
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON> Nẵng',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON>',
      '<PERSON><PERSON><PERSON><PERSON>',
      '<PERSON><PERSON>',
      '<PERSON><PERSON>'
    ],
    datasets: [
      {
        label: '<PERSON><PERSON> lượng kh<PERSON>ch hàng',
        data: [320, 280, 250, 220, 180, 160, 140, 120, 100, 90],
        backgroundColor: [
          'rgba(255, 99, 132, 0.8)',
          'rgba(54, 162, 235, 0.8)',
          'rgba(255, 206, 86, 0.8)',
          'rgba(75, 192, 192, 0.8)',
          'rgba(153, 102, 255, 0.8)',
          'rgba(255, 159, 64, 0.8)',
          'rgba(199, 199, 199, 0.8)',
          'rgba(83, 102, 255, 0.8)',
          'rgba(78, 252, 3, 0.8)',
          'rgba(252, 3, 244, 0.8)'
        ],
        borderColor: [
          'rgba(255, 99, 132, 1)',
          'rgba(54, 162, 235, 1)',
          'rgba(255, 206, 86, 1)',
          'rgba(75, 192, 192, 1)',
          'rgba(153, 102, 255, 1)',
          'rgba(255, 159, 64, 1)',
          'rgba(199, 199, 199, 1)',
          'rgba(83, 102, 255, 1)',
          'rgba(78, 252, 3, 1)',
          'rgba(252, 3, 244, 1)'
        ],
        borderWidth: 2
      }
    ]
  }

  const chartData = data || defaultData

  const options = {
    indexAxis: 'y' as const, // Đây là key để tạo horizontal bar chart
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          padding: 20,
          font: {
            size: 12
          }
        }
      },
      title: {
        display: true,
        text: 'Top 10 Tỉnh Thành',
        font: {
          size: 16,
          weight: 'bold' as const
        },
        padding: {
          top: 10,
          bottom: 20
        }
      },
      tooltip: {
        callbacks: {
          label: function (context: any) {
            return `${context.dataset.label}: ${context.parsed.x}`
          }
        }
      }
    },
    scales: {
      x: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Số lượng khách hàng',
          font: {
            size: 12,
            weight: 'bold' as const
          }
        },
        ticks: {
          stepSize: 50
        }
      },
      y: {
        title: {
          display: true,
          text: 'Tỉnh/Thành',
          font: {
            size: 12,
            weight: 'bold' as const
          }
        }
      }
    }
  }

  return (
    <div
      style={{
        width: '100%',
        height: '450px',
        padding: '20px',
        backgroundColor: '#fff',
        borderRadius: '8px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
      <Bar data={chartData} options={options} />
    </div>
  )
}

export default TopProvincesBarChart
