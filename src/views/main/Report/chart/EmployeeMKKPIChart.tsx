import { Card, Row, Col, Select, Table, Tag, Space } from 'antd'
import { Bar, Doughnut } from 'react-chartjs-2'
import {
  Chart,
  BarElement,
  CategoryScale,
  LinearScale,
  Tooltip,
  Legend,
  ArcElement
} from 'chart.js'
import { getColorPercent, showValueOnBar } from '~/common/utils/common.utils'

Chart.register(BarElement, CategoryScale, LinearScale, Tooltip, Legend, ArcElement)
Chart.register(showValueOnBar)

const csKPI = {
  labels: [
    'Chiến dịch triển khai',
    '<PERSON><PERSON><PERSON>t tiếp cận',
    'Tỷ lệ chuyển đổi',
    'Chi phí/lead',
    'Số lead MKT tạo ra'
  ],
  datasets: [
    {
      label: '% Tháng trước',
      backgroundColor: '#648DB3',
      data: [100, 6, 90, 5, 95]
    },
    {
      label: '% Tháng hiện tại',
      backgroundColor: '#FF3F33',
      data: [105, 67, 103, 104, 50]
    }
  ]
}

const options = {
  plugins: {
    legend: {
      position: 'top' as const
    }
  },
  responsive: true,
  scales: {
    y: {
      beginAtZero: true
    }
  }
}

const { Option } = Select

export const EmployeeMKTKPIChart = () => {
  const columns = [
    {
      title: 'KPI',
      dataIndex: 'kpi',
      key: 'kpi'
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: 'Đơn vị đo',
      dataIndex: 'unit',
      key: 'unit'
    },
    {
      title: 'Mục tiêu',
      dataIndex: 'target',
      key: 'target'
    },
    {
      title: 'Kết quả đạt được',
      dataIndex: 'result',
      key: 'result'
    },
    {
      title: 'Tỷ lệ hoàn thành',
      dataIndex: 'completion',
      key: 'completion',
      render: (text: string) => {
        const value = parseInt(text)
        let color = 'default'
        if (value >= 100) color = 'green'
        else if (value >= 70) color = 'orange'
        else color = 'red'

        return <Tag color={color}>{text}%</Tag>
      }
    },
    {
      title: 'Tổng KPI',
      dataIndex: 'total',
      key: 'total',
      render: (_text: any, _row: any, index: number) => {
        if (index === 0) {
          return {
            children: (
              <Tag color={getColorPercent(61)}>
                <strong>61%</strong>
              </Tag>
            ),
            props: {
              rowSpan: 5 // Số hàng bạn muốn merge (ở đây là 5 dòng)
            }
          }
        }

        return {
          children: null,
          props: {
            rowSpan: 0
          }
        }
      }
    }
  ]
  const data = [
    {
      key: '1',
      kpi: 'Chiến dịch triển khai',
      description:
        'Số lượng chiến dịch marketing đã được lên kế hoạch và thực hiện (email, quảng cáo, sự kiện,...)',
      unit: 'Chiến dịch',
      target: 20,
      result: 21,
      completion: '105'
    },
    {
      key: '2',
      kpi: 'Lượt tiếp cận',
      description:
        'Tổng số người nhìn thấy hoặc tương tác với chiến dịch (view, impression, reach...)',
      unit: 'Lượt',
      target: 60,
      result: 40,
      completion: '67'
    },
    {
      key: '3',
      kpi: 'Chuyển đổi',
      description:
        'Người tiếp cận chuyển đổi thành hành động cụ thể (đăng ký, mua hàng, điền form...)',
      unit: 'Người',
      target: 30,
      result: 31,
      completion: '103'
    },
    {
      key: '4',
      kpi: 'Số bài viết',
      description: 'Số lượng bài viết trên các nền tảng',
      unit: 'Bài viết',
      target: 50,
      result: 52,
      completion: '104'
    },
    {
      key: '5',
      kpi: 'Số lead MKT tạo ra',
      description:
        'Số lượng khách hàng tiềm năng (lead) mà marketing chuyển giao cho sale (qualified lead).',
      unit: 'Lead',
      target: 20,
      result: 10,
      completion: '50'
    }
  ]
  return (
    <Card title='KPI nhóm nhân viên Marketing' size='small' style={{ marginTop: 24 }}>
      <Space>
        <Select style={{ width: 200, marginBottom: 16 }} placeholder='Chọn năm'>
          <Option value=''>--Chọn năm--</Option>
          <Option value='2021'>2021</Option>
          <Option value='2022'>2022</Option>
          <Option value='2023'>2023</Option>
        </Select>
        <Select style={{ width: 200, marginBottom: 16 }} placeholder='Chọn quý'>
          <Option value=''>--Chọn quý--</Option>
          <Option value='1'>Quý 1</Option>
          <Option value='2'>Quý 2</Option>
          <Option value='3'>Quý 3</Option>
          <Option value='3'>Quý 4</Option>
        </Select>
        <Select style={{ width: 200, marginBottom: 16 }} placeholder='Chọn tháng trước'>
          <Option value=''>--Chọn tháng--</Option>
          <Option value='1'>Tháng 1</Option>
          <Option value='2'>Tháng 2</Option>
          <Option value='3'>Tháng 3</Option>
          <Option value='3'>Tháng 4</Option>
          <Option value='3'>Tháng 5</Option>
        </Select>
      </Space>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <div
            style={{
              width: '100%',
              height: '450px',
              padding: '20px',
              backgroundColor: '#fff',
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}>
            <Bar data={csKPI} options={options} />
          </div>
        </Col>
        <Col xs={24} lg={12}>
          <Table size='small' columns={columns} dataSource={data} pagination={false} bordered />
        </Col>
      </Row>
    </Card>
  )
}
