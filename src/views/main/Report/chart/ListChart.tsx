import React from 'react'
import { Row, Col } from 'antd'
import CustomerGroupPieChart from './CustomerGroupPieChart'
import CustomerGroupBarChart from './CustomerGroupBarChart'
import TopProvincesBarChart from './TopProvincesBarChart'
import RegionDistributionPie<PERSON>hart from './RegionDistributionPieChart'
import RegionCustomerBarChart from './RegionCustomerBarChart'
import ProductPreferenceAreaChart from './ProductPreferenceAreaChart'
import ComplaintGroupBarChart from './ComplaintGroupBarChart'
import CommonIssueBarChart from './CommonIssueBarChart'

const ListChart: React.FC = () => {
  return (
    <div style={{ marginBottom: 24 }}>
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={12}>
          <CustomerGroupPieChart />
        </Col>
        <Col xs={24} lg={12}>
          <CustomerGroupBarChart />
        </Col>
        <Col xs={24} lg={12}>
          <TopProvincesBarChart />
        </Col>
        <Col xs={24} lg={12}>
          <RegionDistributionPieChart />
        </Col>
        <Col xs={24} lg={12}>
          <RegionCustomerBarChart />
        </Col>
        <Col xs={24} lg={12}>
          <ProductPreferenceAreaChart />
        </Col>
        <Col xs={24} lg={12}>
          <ComplaintGroupBarChart />
        </Col>
        <Col xs={24} lg={12}>
          <CommonIssueBarChart />
        </Col>
      </Row>
    </div>
  )
}

export default ListChart
