import { FC, useState, useEffect } from 'react'
import { ColumnsType } from 'antd/es/table'
import { Card, Row, Col, Statistic } from 'antd'
import {
  UserOutlined,
  ShoppingCartOutlined,
  DollarOutlined
} from '@ant-design/icons'

import BaseView from '~/components/BaseView'
import BaseText from '~/components/BaseText'
import BaseTable from '~/components/BaseTable'
import { FilterRevenueReport } from '../component/FilterRevenueReport'
import { IFilterRevenueReport } from '~/dto/report.dto'

// Interface for customer revenue data
interface ICustomerRevenue {
  id: string
  customerCode: string
  customerName: string
  taxCode: string
  phone: string
  address: string
  totalOrders: number
  totalRevenue: number
}

// Mock data for demonstration
const mockData: ICustomerRevenue[] = [
  {
    id: '1',
    customerCode: 'KH001',
    customerName: 'Công ty TNHH ABC',
    taxCode: '0123456789',
    phone: '0901234567',
    address: '123 Đường ABC, Quận 1, TP.HCM',
    totalOrders: 15,
    totalRevenue: 25000000
  },
  {
    id: '2',
    customerCode: 'KH002',
    customerName: 'Công ty Cổ phần XYZ',
    taxCode: '0987654321',
    phone: '0909876543',
    address: '456 Đường XYZ, Quận 3, TP.HCM',
    totalOrders: 8,
    totalRevenue: 18000000
  },
  {
    id: '3',
    customerCode: 'KH003',
    customerName: 'Doanh nghiệp Tư nhân DEF',
    taxCode: '0111222333',
    phone: '0901112223',
    address: '789 Đường DEF, Quận 7, TP.HCM',
    totalOrders: 22,
    totalRevenue: 35000000
  },
  {
    id: '4',
    customerCode: 'KH004',
    customerName: 'Công ty TNHH GHI',
    taxCode: '0444555666',
    phone: '0904445556',
    address: '321 Đường GHI, Quận 2, TP.HCM',
    totalOrders: 12,
    totalRevenue: 22000000
  },
  {
    id: '5',
    customerCode: 'KH005',
    customerName: 'Công ty Cổ phần JKL',
    taxCode: '0777888999',
    phone: '0907778889',
    address: '654 Đường JKL, Quận 5, TP.HCM',
    totalOrders: 18,
    totalRevenue: 28000000
  }
]

type IProps = {}

// Doanh số khách hàng
export const CustomerRevenueReport: FC<IProps> = (props: IProps) => {
  const [filter, setFilter] = useState<IFilterRevenueReport>({
    company: '',
    customerCode: '',
    creditLimitFrom: undefined,
    creditLimitTo: undefined,
    phone: '',
    pageIndex: 1,
    pageSize: 10
  })

  const handlePageChange = (pageIndex: number, pageSize: number) => {
    // Handle pagination here
    console.log('Page changed:', pageIndex, pageSize)
  }

  const handleFilter = (values: IFilterRevenueReport) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFilter({
      company: '',
      customerCode: '',
      creditLimitFrom: undefined,
      creditLimitTo: undefined,
      phone: '',
      pageIndex: 1,
      pageSize: 10
    })
  }

  const columns: ColumnsType<ICustomerRevenue> = [
    {
      title: 'STT',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: 'center',
      render: (_, __, index) => index + 1
    },
    {
      //change title color
      title: (
        <BaseText style={{ color: 'green', fontWeight: 'bold' }}>
          <DollarOutlined /> Tổng doanh số
        </BaseText>
      ),
      dataIndex: 'totalRevenue',
      key: 'totalRevenue',
      width: 150,
      align: 'right',
      render: (value) => (
        <BaseText style={{ color: 'green', fontWeight: 500 }}>
          {value.toLocaleString('vi-VN')} ₫
        </BaseText>
      )
    },
    {
      title: 'Mã khách hàng',
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 120,
      align: 'center'
    },
    {
      title: 'Tên khách hàng',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 200,
      render: (text) => <BaseText style={{ fontWeight: 500 }}>{text}</BaseText>
    },
    {
      title: 'Mã số thuế',
      dataIndex: 'taxCode',
      key: 'taxCode',
      width: 130,
      align: 'center'
    },
    {
      title: 'Điện thoại',
      dataIndex: 'phone',
      key: 'phone',
      width: 120,
      align: 'center'
    },
    {
      title: 'Địa chỉ',
      dataIndex: 'address',
      key: 'address',
      width: 250,
      ellipsis: true
    },
    {
      title: 'Tổng đơn hàng',
      dataIndex: 'totalOrders',
      key: 'totalOrders',
      width: 120,
      align: 'center'
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24}>
          <FilterRevenueReport
            onFilter={handleFilter}
            onReset={handleReset}
            isLoading={false}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable<ICustomerRevenue>
            columns={columns}
            data={mockData}
            total={mockData.length}
            isLoading={false}
            defaultPageSize={10}
            onPageChange={handlePageChange}
            rowKey='id'
            scroll={{ x: 1200 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}
