import React, { useState } from 'react'
import { Card, Table, Tag, Typography, Space, Button, Select, Input, Col, Row } from 'antd'
import { EditOutlined, EyeOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import BaseTable from '~/components/BaseTable'
import { useNavigate } from 'react-router-dom'
import BaseView from '~/components/BaseView'

const { Title } = Typography
const { Option } = Select

interface QuotationTemplate {
  key: string
  serviceType: string
  templateName: string
  templateCode: string
  applicableUnits: string[]
  effectiveDate: string
  createdAt: string
  status: string
  updatedAt: string
}

const mockData: QuotationTemplate[] = [
  {
    key: '1',
    serviceType: 'Vận chuyển đường bộ',
    templateName: 'Báo giá nội thành HN',
    templateCode: 'TPL-HN-01',
    applicableUnits: ['Công ty A', 'Chi nhánh HCM'],
    effectiveDate: '2025-06-01',
    createdAt: '2025-05-20',
    updatedAt: '2025-06-01',
    status: 'Active'
  },
  {
    key: '2',
    serviceType: '<PERSON>ận chuyển đường biển',
    templateName: 'Báo giá quốc tế',
    templateCode: 'TPL-SEA-INT',
    applicableUnits: ['Công ty B'],
    effectiveDate: '2025-06-15',
    createdAt: '2025-05-25',
    updatedAt: '2025-06-12',
    status: 'Active'
  },
  {
    key: '3',
    serviceType: 'Kho bãi',
    templateName: 'Báo giá lưu kho HCM',
    templateCode: 'TPL-WH-HCM',
    applicableUnits: ['Kho HCM'],
    effectiveDate: '2025-05-01',
    createdAt: '2025-04-10',
    updatedAt: '2025-05-01',
    status: 'InActive'
  },
  {
    key: '4',
    serviceType: 'Vận chuyển đường hàng không',
    templateName: 'Báo giá nhanh QG',
    templateCode: 'TPL-AIR-QG',
    applicableUnits: ['Chi nhánh Đà Nẵng'],
    effectiveDate: '2025-06-10',
    createdAt: '2025-05-28',
    updatedAt: '2025-06-05',
    status: 'Active'
  },
  {
    key: '5',
    serviceType: 'Vận chuyển đường bộ',
    templateName: 'Báo giá tỉnh miền Bắc',
    templateCode: 'TPL-BAC-01',
    applicableUnits: ['Công ty A'],
    effectiveDate: '2025-06-01',
    createdAt: '2025-05-15',
    updatedAt: '2025-06-01',
    status: 'Active'
  },
  {
    key: '6',
    serviceType: 'Vận chuyển đường biển',
    templateName: 'Báo giá nội địa biển',
    templateCode: 'TPL-SEA-VN',
    applicableUnits: ['Chi nhánh Hải Phòng'],
    effectiveDate: '2025-07-01',
    createdAt: '2025-06-01',
    updatedAt: '2025-06-15',
    status: 'InActive'
  },
  {
    key: '7',
    serviceType: 'Kho bãi',
    templateName: 'Báo giá lưu kho HN',
    templateCode: 'TPL-WH-HN',
    applicableUnits: ['Kho Hà Nội'],
    effectiveDate: '2025-05-20',
    createdAt: '2025-04-28',
    updatedAt: '2025-05-20',
    status: 'Active'
  },
  {
    key: '8',
    serviceType: 'Vận chuyển đường hàng không',
    templateName: 'Báo giá hàng nhẹ',
    templateCode: 'TPL-AIR-LIGHT',
    applicableUnits: ['Công ty C'],
    effectiveDate: '2025-06-25',
    createdAt: '2025-06-01',
    updatedAt: '2025-06-10',
    status: 'InActive'
  },
  {
    key: '9',
    serviceType: 'Vận chuyển đường bộ',
    templateName: 'Báo giá khu vực miền Trung',
    templateCode: 'TPL-MTRUNG',
    applicableUnits: ['Chi nhánh Đà Nẵng', 'Chi nhánh Huế'],
    effectiveDate: '2025-06-10',
    createdAt: '2025-05-20',
    updatedAt: '2025-06-05',
    status: 'Active'
  },
  {
    key: '10',
    serviceType: 'Kho bãi',
    templateName: 'Báo giá lưu kho tổng hợp',
    templateCode: 'TPL-WH-GEN',
    applicableUnits: ['Kho HCM', 'Kho HN'],
    effectiveDate: '2025-06-20',
    createdAt: '2025-06-01',
    updatedAt: '2025-06-16',
    status: 'InActive'
  }
]

export const QuotationView = () => {
  const navigate = useNavigate()
  const [searchCode, setSearchCode] = useState('')
  const [searchName, setSearchName] = useState('')
  const [status, setStatus] = useState('')

  const generateStatusColor = (status: string) => {
    switch (status) {
      case 'Active':
        return 'green'
      case 'InActive':
        return 'red'
      default:
        return 'default'
    }
  }

  const handleEdit = (record: QuotationTemplate) => {
    navigate(`edit?id=${record.key}`)
  }

  const handleViewDetail = (record: QuotationTemplate) => {
    navigate(`detail?id=${record.key}`)
  }

  const columns = [
    {
      title: 'Loại dịch vụ',
      dataIndex: 'serviceType',
      key: 'serviceType'
    },
    {
      title: 'Tên Template',
      dataIndex: 'templateName',
      key: 'templateName'
    },
    {
      title: 'Mã Template',
      dataIndex: 'templateCode',
      key: 'templateCode'
    },
    {
      title: 'Đơn vị áp dụng',
      dataIndex: 'applicableUnits',
      key: 'applicableUnits',
      render: (units: string[]) => (
        <Space wrap>
          {units.map((unit, index) => (
            <Tag color='blue' key={index}>
              {unit}
            </Tag>
          ))}
        </Space>
      )
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => <Tag color={generateStatusColor(status)}>{status}</Tag>
    },
    {
      title: 'Ngày áp dụng',
      dataIndex: 'effectiveDate',
      key: 'effectiveDate'
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt'
    },
    {
      title: 'Cập nhật',
      dataIndex: 'updatedAt',
      key: 'updatedAt'
    },
    {
      title: 'Thao tác',
      key: 'actions',
      render: (record: QuotationTemplate) => (
        <Space>
          <Button type='primary' icon={<EyeOutlined />} onClick={() => handleViewDetail(record)}>
            Xem
          </Button>
          <Button icon={<EditOutlined />} onClick={() => handleEdit(record)}>
            Sửa
          </Button>
        </Space>
      )
    }
  ]

  return (
    <BaseView>
      <Card
        title='Quản lý Template Báo giá'
        style={{ width: '100%', height: '85vh', overflow: 'auto' }}>
        <Row style={{ marginBottom: 16 }} gutter={24}>
          <Col span={6}>
            <div>Mã template: </div>
            <Input
              placeholder='Nhập mã template'
              value={searchCode}
              onChange={(e) => setSearchCode(e.target.value)}
            />
          </Col>
          <Col span={6}>
            <div>Tên template: </div>
            <Input
              placeholder='Nhập tên template'
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
            />
          </Col>
          <Col span={6}>
            <div>Trạng thái: </div>
            <Select
              style={{ width: '100%' }}
              placeholder='Chọn trạng thái'
              allowClear
              value={status}
              onChange={setStatus}>
              <Option value=''>--Chọn trạng thái--</Option>
              <Option value='active'>Active</Option>
              <Option value='inactive'>Inactive</Option>
            </Select>
          </Col>
          <Col span={24} style={{ marginTop: 10, textAlign: 'center' }}>
            <Button type='primary' icon={<SearchOutlined />} style={{ marginRight: 10 }}>
              Tìm Kiếm
            </Button>
            <Button type='default' icon={<ReloadOutlined />}>
              Làm mới
            </Button>
          </Col>
        </Row>
        <BaseTable
          data={mockData}
          total={mockData.length}
          isLoading={false}
          columns={columns}
          bordered
          scroll={{ x: 'max-content' }}
        />
      </Card>
    </BaseView>
  )
}
