import React, { useState } from 'react'
import { Table, Tag, Button, Space, Card, Select, Input, Col, Row } from 'antd'
import type { ColumnsType } from 'antd/es/table'
import { DeleteOutlined, EyeOutlined, ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import BaseTable from '~/components/BaseTable'
import BaseView from '~/components/BaseView'
import DetailButton from './components/DetailButton'
import EditButton from './components/EditButton'

export interface Invoice {
  key: string
  code: string
  customerName: string
  // Đơn vị phát hành
  issuingUnit: 'MISA' | 'VNPT Invoice'
  serviceType: 'Logistic' | 'Khác'
  status: 'active' | 'inactive'
  createdAt: string
  createdBy: string
}

const mockData: Invoice[] = [
  {
    key: '1',
    code: 'TEMP-001',
    issuingUnit: 'MISA',
    customerName: 'Công ty ABC',
    serviceType: 'Logistic',
    status: 'active',
    createdAt: '2025-06-10',
    createdBy: '<PERSON>uy<PERSON><PERSON>n <PERSON>'
  },
  {
    key: '2',
    code: 'TEMP-002',
    issuingUnit: 'VNPT Invoice',
    customerName: 'Công ty XYZ',
    serviceType: 'Khác',
    status: 'inactive',
    createdAt: '2025-06-11',
    createdBy: 'Trần Thị B'
  },
  {
    key: '3',
    code: 'TEMP-003',
    issuingUnit: 'MISA',
    customerName: 'Công ty Mặt Trời',
    serviceType: 'Logistic',
    status: 'active',
    createdAt: '2025-06-09',
    createdBy: 'Phạm Văn C'
  },
  {
    key: '4',
    code: 'TEMP-004',
    issuingUnit: 'VNPT Invoice',
    customerName: 'Công ty Sao Mai',
    serviceType: 'Khác',
    status: 'active',
    createdAt: '2025-06-08',
    createdBy: 'Lê Thị D'
  },
  {
    key: '5',
    code: 'TEMP-005',
    issuingUnit: 'MISA',
    customerName: 'Công ty Thủy Tinh',
    serviceType: 'Logistic',
    status: 'inactive',
    createdAt: '2025-06-07',
    createdBy: 'Ngô Văn E'
  },
  {
    key: '6',
    code: 'TEMP-006',
    issuingUnit: 'VNPT Invoice',
    customerName: 'Công ty Đại Dương',
    serviceType: 'Khác',
    status: 'inactive',
    createdAt: '2025-06-06',
    createdBy: 'Vũ Thị F'
  },
  {
    key: '7',
    code: 'TEMP-007',
    issuingUnit: 'MISA',
    customerName: 'Công ty Xanh',
    serviceType: 'Logistic',
    status: 'active',
    createdAt: '2025-06-05',
    createdBy: 'Trịnh Văn G'
  },
  {
    key: '8',
    code: 'TEMP-008',
    issuingUnit: 'VNPT Invoice',
    customerName: 'Công ty Đỏ',
    serviceType: 'Khác',
    status: 'inactive',
    createdAt: '2025-06-04',
    createdBy: 'Đặng Thị H'
  },
  {
    key: '9',
    code: 'TEMP-009',
    issuingUnit: 'MISA',
    customerName: 'Công ty Vàng',
    serviceType: 'Logistic',
    status: 'active',
    createdAt: '2025-06-03',
    createdBy: 'Bùi Văn I'
  },
  {
    key: '10',
    code: 'TEMP-010',
    issuingUnit: 'VNPT Invoice',
    customerName: 'Công ty Bạc',
    serviceType: 'Khác',
    status: 'active',
    createdAt: '2025-06-02',
    createdBy: 'Hoàng Thị K'
  }
]

const { Option } = Select

export const InvoiceView = () => {
  const [searchCode, setSearchCode] = useState('')
  const [searchName, setSearchName] = useState('')
  const [status, setStatus] = useState('')

  const columns: ColumnsType<Invoice> = [
    {
      title: 'STT',
      dataIndex: 'index',
      key: 'index',
      render: (_text, _record, index) => index + 1
    },
    {
      title: 'Mã hóa đơn',
      dataIndex: 'code',
      key: 'code'
    },
    {
      title: 'Đơn vị phát hành',
      dataIndex: 'issuingUnit',
      key: 'issuingUnit',
      render: (unit: Invoice['issuingUnit']) => {
        const color = unit === 'MISA' ? 'blue' : 'orange'
        return <Tag color={color}>{unit}</Tag>
      }
    },
    {
      title: 'Tên khách hàng',
      dataIndex: 'customerName',
      key: 'customerName'
    },
    {
      title: 'Dịch vụ',
      dataIndex: 'serviceType',
      key: 'serviceType',
      render: (type: Invoice['serviceType']) => {
        const color = type === 'Logistic' ? 'blue' : 'purple'
        return <Tag color={color}>{type}</Tag>
      }
    },
    {
      title: 'Trạng thái',
      dataIndex: 'status',
      key: 'status',
      render: (status: Invoice['status']) => {
        const color = status === 'active' ? 'green' : 'red'
        const label = status === 'active' ? 'Đang hoạt động' : 'Ngưng hoạt động'

        return <Tag color={color}>{label}</Tag>
      }
    },
    {
      title: 'Ngày tạo',
      dataIndex: 'createdAt',
      key: 'createdAt'
    },
    {
      title: 'Người tạo',
      dataIndex: 'createdBy',
      key: 'createdBy'
    },
    {
      title: 'Tác vụ',
      key: 'action',
      render: (_, record) => (
        <Space>
          <DetailButton data={record} />
          <EditButton data={record} />

          <Button
            icon={<DeleteOutlined />}
            danger
            type='primary'
            onClick={() => console.log('Xóa', record.code)}></Button>
        </Space>
      )
    }
  ]

  return (
    <BaseView>
      <Card title='Quản lý mẫu hóa đơn'>
        {/* Bộ lọc */}
        <Row style={{ marginBottom: 16 }} gutter={24}>
          <Col span={6}>
            <div>Mã hóa đơn: </div>
            <Input
              placeholder='Nhập mã hóa đơn'
              value={searchCode}
              onChange={(e) => setSearchCode(e.target.value)}
            />
          </Col>
          <Col span={6}>
            <div>Tên hóa đơn: </div>
            <Input
              placeholder='Nhập tên hóa đơn'
              value={searchName}
              onChange={(e) => setSearchName(e.target.value)}
            />
          </Col>
          <Col span={6}>
            <div>Trạng thái: </div>
            <Select
              style={{ width: '100%' }}
              placeholder='Chọn trạng thái'
              allowClear
              value={status}
              onChange={setStatus}>
              <Option value=''>--Chọn trạng thái--</Option>
              <Option value='active'>Active</Option>
              <Option value='inactive'>Inactive</Option>
            </Select>
          </Col>
          <Col span={24} style={{ marginTop: 10, textAlign: 'center' }}>
            <Button type='primary' icon={<SearchOutlined />} style={{ marginRight: 10 }}>
              Tìm Kiếm
            </Button>
            <Button type='default' icon={<ReloadOutlined />}>
              Làm mới
            </Button>
          </Col>
        </Row>
        <BaseTable
          columns={columns}
          data={mockData}
          total={mockData.length}
          isLoading={false}
          rowKey='key'
        />
      </Card>
    </BaseView>
  )
}
