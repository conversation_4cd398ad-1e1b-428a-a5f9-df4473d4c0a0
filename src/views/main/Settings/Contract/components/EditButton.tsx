import { EditOutlined, SaveOutlined } from '@ant-design/icons'
import {
  Card,
  Row,
  Col,
  Form,
  Input,
  Button,
  Select,
  Table,
  Typography
} from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'

import { useEffect, useState } from 'react'
import { useModal } from '~/hooks/useModal'
import { ISettingContract } from '~/dto/setting-contract.dto'
const { Text } = Typography
const { Option } = Select

interface EditButtonProps {
  data: ISettingContract
  onSuccess?: () => void
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { open, openModal, closeModal } = useModal()
  const [form] = useForm()

  const [detailData, setDetailData] = useState<ISettingContract>(data)

  useEffect(() => {
    let newTerms = detailData.terms.filter((term) => term.name !== '')
    console.log(newTerms)
    setDetailData({ ...detailData, terms: newTerms })
  }, [open])

  useEffect(() => {
    if (open && data) {
      form.setFieldsValue({
        ...detailData
      })
    }
  }, [open, detailData, form])

  const columns: any[] = [
    {
      title: 'Tên điều khoản',
      dataIndex: 'name',
      key: 'name',
      width: '20%',
      render: (value: string, record: any, index: number) => (
        <Input.TextArea
          value={value}
          onChange={(e) => {
            const newTerms = [...detailData.terms]
            newTerms[index].name = e.target.value
            setDetailData({ ...detailData, terms: newTerms })
          }}
        />
      )
    },
    {
      title: 'Nội dung',
      dataIndex: 'content',
      key: 'content',
      render: (value: string, record: any, index: number) => (
        <Input.TextArea
          value={value}
          onChange={(e) => {
            const newTerms = [...detailData.terms]
            newTerms[index].content = e.target.value
            setDetailData({ ...detailData, terms: newTerms })
          }}
        />
      )
    }
  ]

  const onAddClick = () => {
    setDetailData({
      ...detailData,
      terms: [
        ...detailData.terms,
        { code: '' + detailData.terms.length + 1, name: '', content: '' }
      ]
    })
  }

  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Form form={form} layout='vertical'>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='Tên' name='name'>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Mô tả' name='description'>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Trạng thái' name='status'>
                <Select>
                  <Option value='Hoạt động'>Hoạt động</Option>
                  <Option value='Không hoạt động'>Không hoạt động</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={24} style={{ marginBottom: 16 }}>
              <Text strong>Điều khoản:</Text>
              <Table
                columns={columns}
                dataSource={detailData.terms}
                rowKey={(record, index) => index}
                pagination={false}
                bordered
                style={{ marginTop: 8, marginBottom: 8 }}
              />

              <Button type='primary' onClick={onAddClick}>
                Thêm điều khoản
              </Button>
            </Col>

            <Col span={24}>
              <Form.Item name='baseOn'>
                <Text strong>Căn cứ ký kết:</Text>
                <Input.TextArea rows={4} placeholder='Căn cứ vào ...' />
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <div
          style={{
            textAlign: 'center',
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}>
          <Button onClick={closeModal} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Cập nhật
          </Button>
        </div>
      </Card>
    </div>
  )

  return (
    <>
      <BaseButton
        icon={<EditOutlined />}
        onClick={openModal}
        type='primary'
        tooltip='Chỉnh sửa'
      />
      <BaseModal
        open={open}
        onClose={closeModal}
        title='Chỉnh sửa hợp đồng'
        description='Cập nhật thông tin hợp đồng'
        childrenBody={modalContent}
      />
    </>
  )
}

export default EditButton
