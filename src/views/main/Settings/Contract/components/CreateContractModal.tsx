import { SaveOutlined } from '@ant-design/icons'
import { Row, Col, Form, Input, Button, Select, Card, Table, Typography } from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useEffect, useState } from 'react'
import { useCreateProduct } from '~/hooks/product/useCreateProduct'
import { ISettingContract } from '~/dto/setting-contract.dto'
import CreateProductModal from '../../Department/components/CreateDepartmentModal'

const { Option } = Select
const { Text } = Typography

interface CreateProductModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateContractModal = ({ open, onClose, onSuccess }: CreateProductModalProps) => {
  const [form] = useForm()

  const [detailData, setDetailData] = useState<ISettingContract>({
    id: null,
    code: null,
    name: null,
    terms: [{ code: null, name: null, content: null }],
    baseOn: null,
    description: null,
    status: null,
    createdAt: null,
    updatedAt: null
  })
  useEffect(() => {
    form.setFieldsValue(detailData)
  }, [open, detailData, form])

  const columns: any[] = [
    {
      title: 'Tên điều khoản',
      dataIndex: 'name',
      key: 'name',
      width: '20%',
      render: (value: string, record: any, index: number) => (
        <Input.TextArea
          placeholder='Nhập tên điều khoản'
          value={value}
          onChange={(e) => {
            const newTerms = [...detailData.terms]
            newTerms[index].name = e.target.value
            setDetailData({ ...detailData, terms: newTerms })
          }}
        />
      )
    },
    {
      title: 'Nội dung',
      dataIndex: 'content',
      key: 'content',
      render: (value: string, record: any, index: number) => (
        <Input.TextArea
          placeholder='Nhập nội dung điều khoản'
          value={value}
          onChange={(e) => {
            const newTerms = [...detailData.terms]
            newTerms[index].content = e.target.value
            setDetailData({ ...detailData, terms: newTerms })
          }}
        />
      )
    }
  ]

  const onAddClick = () => {
    setDetailData({
      ...detailData,
      terms: [
        ...detailData.terms,
        { code: '' + detailData.terms.length + 1, name: '', content: '' }
      ]
    })
  }

  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Form form={form} layout='vertical'>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='Tên' name='name'>
                <Input placeholder='Nhập tên hợp đồng' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Mô tả' name='description'>
                <Input placeholder='Nhập mô tả hợp đồng' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Trạng thái' name='status'>
                <Select placeholder='Chọn trạng thái' allowClear>
                  <Option value='Hoạt động'>Hoạt động</Option>
                  <Option value='Không hoạt động'>Không hoạt động</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={24} style={{ marginBottom: 16 }}>
              <Text strong>Điều khoản:</Text>
              <Table
                columns={columns}
                dataSource={detailData.terms}
                rowKey={(record, index) => index}
                pagination={false}
                bordered
                style={{ marginTop: 8 }}
              />

              <Button type='primary' onClick={onAddClick}>
                Thêm điều khoản
              </Button>
            </Col>
            <Col span={24}>
              <Form.Item name='baseOn'>
                <Text strong>Căn cứ ký kết:</Text>
                <Input.TextArea rows={4} placeholder='Căn cứ vào ...' />
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <div
          style={{
            textAlign: 'center',
            marginTop: 24,
            borderTop: '1px solid #f0f0f0',
            paddingTop: 16
          }}>
          <Button onClick={onClose} style={{ marginRight: 8 }}>
            Hủy
          </Button>
          <Button type='primary' htmlType='submit' icon={<SaveOutlined />}>
            Thêm mới
          </Button>
        </div>
      </Card>
    </div>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo cấu hình hợp đồng '
      description='Thêm mới cấu hình hợp đồng'
      childrenBody={modalContent}
    />
  )
}

export default CreateContractModal
