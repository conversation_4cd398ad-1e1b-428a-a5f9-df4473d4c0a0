import { SaveOutlined, PlusOutlined } from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Switch,
  Upload,
  message,
  Card
} from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useEffect, useState } from 'react'
import { EProduct } from '~/common/enums/NSProduct'
import { useCreateProduct } from '~/hooks/product/useCreateProduct'
import { CreateProductReq } from '~/dto/product.dto'
import { toastService } from '~/services'
import type { UploadFile, UploadProps } from 'antd'
import useUploadMutiple from '~/hooks/uploadFile/useUploadMutiple'
import useUploadSingle from '~/hooks/uploadFile/useUploadSingle'
import { useModal } from '~/hooks/useModal'

const { Option } = Select
const { TextArea } = Input

interface CreateProductModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateDepartmentModal = ({
  open,
  onClose,
  onSuccess
}: CreateProductModalProps) => {
  const [form] = useForm()
  useEffect(() => {
    form.setFieldsValue({
      id: null,
      name: null,
      description: null,
      status: null
    })
  }, [open])

  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Form form={form} layout='vertical'>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='Mã phòng ban' name='id'>
                <Input placeholder='Nhập mã phòng ban' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Tên phòng ban' name='name'>
                <Input placeholder='Nhập tên phòng ban' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Mô tả' name='description'>
                <Input placeholder='Nhập mô tả' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Trạng thái' name='status'>
                <Select placeholder='Nhập trạng thái'>
                  <Select.Option value='Hoạt động'>Hoạt động</Select.Option>
                  <Select.Option value='Không hoạt động'>
                    Không hoạt động
                  </Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row
            style={{
              textAlign: 'center',
              borderTop: '1px solid #f0f0f0',
              display: 'flex',
              justifyContent: 'center',
              padding: '16px 0'
            }}>
            <Button onClick={onClose} style={{ marginRight: 8 }}>
              Hủy
            </Button>
            <Button type='primary' icon={<SaveOutlined />}>
              Thêm mới
            </Button>
          </Row>
        </Form>
      </Card>
    </div>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo sản phẩm mới'
      description='Thêm sản phẩm mới vào hệ thống'
      childrenBody={modalContent}
    />
  )
}

export default CreateDepartmentModal
