import { Button, Col, Collapse, Form, Input, Row, Select } from 'antd'
import { FC, useCallback } from 'react'
import { ReloadOutlined, SearchOutlined } from '@ant-design/icons'
import { IDepartmentFilter } from '~/dto/department.dto'
import { IPermission, IPermissionFilter } from '~/dto/permission.dto'

interface IProps {
  onFilter: (values: IPermissionFilter) => void
  onReset: () => void
  isLoading: boolean
}

const FilterProduct: FC<IProps> = (props: IProps) => {
  const { onFilter, onReset, isLoading } = props
  const [form] = Form.useForm()

  const handleFilter = useCallback(() => {
    onFilter(form.getFieldsValue())
  }, [form, onFilter])

  const handleReset = useCallback(() => {
    form.resetFields()
    onReset()
  }, [form, onReset])

  return (
    <Collapse>
      <Collapse.Panel header='Tìm kiếm' key='0'>
        <Form form={form} layout='vertical'>
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item name='id' label='Mã quyền'>
                <Input placeholder='Nhập mã quyền' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item name='name' label='Tên quyền'>
                <Input placeholder='Nhập tên quyền' />
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <Row gutter={24} style={{ marginTop: 10 }}>
          <Form.Item style={{ width: '100%' }}>
            <div
              style={{
                display: 'flex',
                gap: 10,
                justifyContent: 'center'
              }}>
              <Button
                type='primary'
                style={{ width: '15%' }}
                htmlType='submit'
                onClick={handleFilter}
                loading={isLoading}>
                <SearchOutlined />
                Tìm kiếm
              </Button>
              <Button
                type='default'
                style={{ width: '15%' }}
                htmlType='submit'
                onClick={handleReset}>
                <ReloadOutlined />
                Làm mới
              </Button>
            </div>
          </Form.Item>
        </Row>
      </Collapse.Panel>
    </Collapse>
  )
}

export default FilterProduct
