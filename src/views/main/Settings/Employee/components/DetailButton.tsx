import { EyeOutlined } from '@ant-design/icons'
import { Card, Typography, Row, Col, Divider, Steps } from 'antd'
import BaseButton from '~/components/BaseButton'
import BaseModal from '~/components/BaseModal'
import { formatDateCustom } from '~/common/helper/helper'
import { IMission } from '~/dto/missions.dto'
import { useEffect, useState } from 'react'
import { IEmployee } from '~/dto/employee.dto'
import { useModal } from '~/hooks/useModal'

const { Title, Paragraph, Text } = Typography

interface DetailButtonProps {
  data: IEmployee
}

const DetailButton = ({ data }: DetailButtonProps) => {
  const { open, openModal, closeModal } = useModal()

  const modalContent = (
    <div>
      {/* Product Overview Card */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Row gutter={16}>
          <Col span={24}>
            <Row>
              <Col span={8} style={{ marginBottom: 16 }}>
                <Text strong>Họ và tên:</Text>
                <br />
                <Text>{data.name}</Text>
              </Col>

              <Col span={8} style={{ marginBottom: 16 }}>
                <Text strong>Giới tính:</Text>
                <br />
                <Text>{data.gender}</Text>
              </Col>

              <Col span={8} style={{ marginBottom: 16 }}>
                <Text strong>Ngày sinh:</Text>
                <br />
                <Text>{formatDateCustom(data.dateOfBirth, 'DD/MM/YYYY')}</Text>
              </Col>

              <Col span={8} style={{ marginBottom: 16 }}>
                <Text strong>Địa chỉ:</Text>
                <br />
                <Text>{data.address}</Text>
              </Col>

              <Col span={8} style={{ marginBottom: 16 }}>
                <Text strong>Phòng ban:</Text>
                <br />
                <Text>{data.department}</Text>
              </Col>

              <Col span={8} style={{ marginBottom: 16 }}>
                <Text strong>Chức vụ:</Text>
                <br />
                <Text>{data.position}</Text>
              </Col>

              <Col span={8} style={{ marginBottom: 16 }}>
                <Text strong>Trạng thái:</Text>
                <br />
                <Text>{data.status}</Text>
              </Col>

              <Col span={8} style={{ marginBottom: 16 }}>
                <Text strong>Ghi chú:</Text>
                <br />
                <Text>{data.note}</Text>
              </Col>
            </Row>
          </Col>
        </Row>
      </Card>
    </div>
  )

  return (
    <>
      <BaseButton icon={<EyeOutlined />} onClick={openModal} type='primary' />
      <BaseModal
        open={open}
        title='Chi tiết nhân viên'
        description='Thông tin chi tiết nhân viên'
        onClose={closeModal}
        childrenBody={modalContent}
      />
    </>
  )
}

export default DetailButton
