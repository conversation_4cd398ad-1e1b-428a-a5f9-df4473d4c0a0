import { SaveOutlined, PlusOutlined, EditOutlined } from '@ant-design/icons'
import {
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Switch,
  Upload,
  message,
  Card,
  DatePicker,
  Radio
} from 'antd'
import BaseModal from '~/components/BaseModal'
import { useForm } from 'antd/es/form/Form'
import { useEffect, useState } from 'react'
import { EProduct } from '~/common/enums/NSProduct'
import { useCreateProduct } from '~/hooks/product/useCreateProduct'
import { CreateProductReq } from '~/dto/product.dto'
import { toastService } from '~/services'
import type { UploadFile, UploadProps } from 'antd'
import useUploadMutiple from '~/hooks/uploadFile/useUploadMutiple'
import useUploadSingle from '~/hooks/uploadFile/useUploadSingle'
import moment from 'moment'
import { BaseText, BaseButton } from '~/components'
import { IMission } from '~/dto'
import { useModal } from '~/hooks/useModal'

const { Option } = Select
const { TextArea } = Input

interface CreateProductModalProps {
  open: boolean
  onClose: () => void
  onSuccess?: () => void
}

const CreateSupportMissionsModal = ({
  open,
  onClose,
  onSuccess
}: CreateProductModalProps) => {
  const [form] = useForm()
  useUploadSingle()

  useEffect(() => {
    form.setFieldsValue({
      id: null,
      title: null,
      description: null,
      type: null,
      status: null,
      displayLocation: null,
      customerCode: null,
      sapCode: null,
      customerName: null,
      address: null,
      supervisor: null,
      assignedEmployee: null,
      assignType: null,
      level: null,
      startDate: null,
      dueDate: null,
      endDate: null,
      checkInDate: null,
      checkOutTime: null
    })
  }, [open])

  // if (!detailData) return null;

  // Handle image upload

  // Header thông tin product

  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card style={{ marginBottom: '16px' }} size='small'>
        <Form layout='vertical' form={form}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label='Loại' name='type' rules={[{ required: true }]}>
                <Select placeholder='Chọn loại'>
                  <Option key={1}>Vừa tạo | To do</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item
                label='Trạng thái'
                name='status'
                rules={[{ required: true }]}>
                <Select defaultValue='todo' disabled>
                  <Select.Option key={'NEW'}>Mới tạo</Select.Option>
                  <Select.Option key={'PROCESSING'}>Đang xử lý</Select.Option>
                  <Select.Option key={'COMPLETED'}>Hoàn thành</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item
                label='Tiêu đề'
                name='title'
                rules={[{ required: true }]}>
                <Input placeholder='Tự động cập nhật sau khi lưu' />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Khách hàng' name='customerName'>
                <Input disabled />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='NV theo dõi/giám sát' name='supervisor'>
                <Input disabled />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Địa chỉ' name='address'>
                <Select placeholder='-- Vui lòng chọn --' />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Liên hệ' name='contact'>
                <Input disabled />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='SĐT liên hệ' name='phone'>
                <Input />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Email' name='email'>
                <Input disabled />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Ngày tiếp nhận' name='checkInDate'>
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Ngày bắt đầu' name='startDate'>
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='NV được phân công' name='assignedEmployee'>
                <Select placeholder='-- Chọn người thực hiện --' />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label='Phân công cho' name='assignType'>
                <Radio.Group>
                  <Radio value={'SINGLE'}>Cá nhân</Radio>
                  <Radio value={'GROUP'}>Nhóm</Radio>
                  <Radio value={'EXTENDED'}>Mở rộng</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label='Mô tả' name='description'>
                <TextArea rows={4} placeholder='Nhập nội dung' />
              </Form.Item>
            </Col>

            <Col span={24} style={{ textAlign: 'center' }}>
              <Button type='primary' style={{ marginRight: 8 }}>
                Lưu
              </Button>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  )

  return (
    <BaseModal
      open={open}
      onClose={onClose}
      title='Tạo nhiệm vụ mới'
      description='Thêm nhiệm vụ mới vào hệ thống'
      childrenBody={modalContent}
    />
  )
}

export default CreateSupportMissionsModal
