import { EditOutlined, SaveOutlined, UploadOutlined } from "@ant-design/icons";
import {
  Card,
  Tag,
  Row,
  Col,
  Form,
  Input,
  InputNumber,
  Button,
  Select,
  Upload,
  message,
  DatePicker,
  Radio,
  TimePicker,
} from "antd";
import BaseButton from "~/components/BaseButton";
import BaseText from "~/components/BaseText";
import { useModal } from "../../../../hooks/useModal";
import BaseModal from "~/components/BaseModal";
import { IProduct } from "~/dto/product.dto";
import { EProduct } from "~/common/enums/NSProduct";
import { useEffect, useState } from "react";
import { useForm } from "antd/es/form/Form";
import {
  useUpdateProduct,
  UseUpdateProductParams,
} from "~/hooks/product/useUpdateProduct";
import useUploadSingle from "~/hooks/uploadFile/useUploadSingle";
import type { UploadFile, UploadProps } from "antd";
import { toastService } from "~/services/@common";
import { useDetailProduct } from "~/hooks/product/useDetailProduct";
import { IMission } from "~/dto/missions.dto";
import moment from "moment";

const { Option } = Select;
const { TextArea } = Input;

interface EditButtonProps {
  data: IMission;
  onSuccess?: () => void;
}

const EditButton = ({ data, onSuccess }: EditButtonProps) => {
  const { open, openModal, closeModal } = useModal();
  const [form] = useForm();
  useUploadSingle();

  const detailData: IMission = data
  useEffect(() => {
    if (open && detailData) {
      form.setFieldsValue({
        ...detailData,
        checkInDate: moment(data.checkInDate),
        startDate: moment(data.startDate),
        dueDate: moment(data.dueDate),
        endDate: moment(data.endDate)
      });
      console.log(form.getFieldsValue(), detailData, data)
    }
  }, [open, detailData, form]);

  // if (!detailData) return null;


  // Handle image upload






  // Header thông tin product
  const productHeader = (
    <Card style={{ marginBottom: 16 }}>
      <Row gutter={16}>
        <Col span={8}>
          <div>
            <BaseText color="textSecondary">Tên sản phẩm:</BaseText>
            <br />
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color="textSecondary">Loại sản phẩm:</BaseText>
            <br />
          </div>
        </Col>
        <Col span={8}>
          <div>
            <BaseText color="textSecondary">Trạng thái:</BaseText>
            <br />
          </div>
        </Col>
      </Row>
      <Row style={{ marginTop: 12 }}>
        <Col span={8}>
          <BaseText color="textSecondary">Khách hàng sử dụng:</BaseText>
          <br />
        </Col>
        <Col span={8}>
          <BaseText color="textSecondary">Hình ảnh:</BaseText>
          <br />
        </Col>
      </Row>
    </Card>
  );

  const modalContent = (
    <div>
      {/* {productHeader} */}
      <Card
        style={{ marginBottom: "16px" }}
        size="small"
      >
        <Form layout="vertical" form={form}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item label="Loại" name="type" rules={[{ required: true }]}>
                <Select placeholder="Chọn loại" >
                  <Option key={1}>Vừa tạo | To do</Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Trạng thái" name="status" rules={[{ required: true }]}>
                <Select defaultValue="todo" disabled>
                  <Select.Option key={'NEW'}>Mới tạo</Select.Option>
                  <Select.Option key={'PROCESSING'}>Đang xử lý</Select.Option>
                  <Select.Option key={'COMPLETED'}>Hoàn thành</Select.Option>
                </Select>
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Tiêu đề" name="title" rules={[{ required: true }]}>
                <Input placeholder="Tự động cập nhật sau khi lưu" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Khách hàng" name="customerName">
                <Input disabled />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="NV theo dõi/giám sát" name="supervisor">
                <Input disabled />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Địa chỉ" name="address">
                <Select placeholder="-- Vui lòng chọn --" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Liên hệ" name="contact">
                <Input disabled />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="SĐT liên hệ" name="phone">
                <Input />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Email" name="email">
                <Input disabled />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Ngày tiếp nhận" name="checkInDate">
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Ngày bắt đầu" name="startDate">
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="NV được phân công" name="assignedEmployee">
                <Select placeholder="-- Chọn người thực hiện --" />
              </Form.Item>
            </Col>

            <Col span={8}>
              <Form.Item label="Phân công cho" name="assignType">
                <Radio.Group>
                  <Radio value={'SINGLE'}>Cá nhân</Radio>
                  <Radio value={'GROUP'}>Nhóm</Radio>
                  <Radio value={'EXTENDED'}>Mở rộng</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label="Mô tả" name="description">
                <TextArea rows={4} placeholder="Nhập nội dung" />
              </Form.Item>
            </Col>

            <Col span={24} style={{ textAlign: 'right' }}>
              <Button type="primary" style={{ marginRight: 8 }}>
                Lưu
              </Button>
            </Col>
          </Row>
        </Form>
      </Card>
    </div>
  );

  return (
    <>
      <BaseButton
        icon={<EditOutlined />}
        onClick={openModal}
        type="primary"
        tooltip="Chỉnh sửa"
      />
      <BaseModal
        open={open}
        onClose={closeModal}
        title="Chỉnh sửa nhiệm vụ"
        description="Cập nhật thông tin nhiệm vụ"
        childrenBody={modalContent}
      />
    </>
  );
};

export default EditButton;
