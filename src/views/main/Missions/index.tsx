// Marketing-campaign components

import { Row, Col } from 'antd'
import dayjs from 'dayjs'
import BaseTable from '~/components/BaseTable'
import BaseView from '~/components/BaseView'
import FilterProduct from './components/FilterProduct'
import { IComplaint } from '~/dto/complaint.dto'
import { toastService } from '~/services'
import { useState } from 'react'
import { IMission, IMissionFilter } from '~/dto/missions.dto'
import { DeleteOutlined } from '@ant-design/icons'
import { BaseButton } from '~/components'
import DetailButton from './components/DetailButton'
import EditButton from './components/EditButton'
import moment from 'moment'

export const SupportMissionsView = () => {
  const [filter, setFilter] = useState<IMissionFilter>({
    id: null,
    title: '',
    description: '',
    type: '',
    status: '',
    displayLocation: '',
    customerCode: '',
    sapCode: '',
    customerName: '',
    address: '',
    supervisor: '',
    assignedEmployee: '',
    level: '',
    startDate: '',
    dueDate: '',
    endDate: '',
    checkInDate: '',
    pageIndex: 0,
    pageSize: 10
  })

  const handleFilter = (values: IMissionFilter) => {
    setFilter(values)
  }

  const handleReset = () => {
    setFilter({
      id: null,
      title: '',
      description: '',
      type: '',
      status: '',
      displayLocation: '',
      customerCode: '',
      sapCode: '',
      customerName: '',
      address: '',
      supervisor: '',
      assignedEmployee: '',
      level: '',
      startDate: '',
      dueDate: '',
      endDate: '',
      checkInDate: '',
      pageIndex: 0,
      pageSize: 10
    })
  }

  const handlePageChange = (newPageIndex: number, newPageSize: number) => {
    setFilter({
      ...filter,
      pageIndex: newPageIndex,
      pageSize: newPageSize
    })
  }

  const handleDelete = async (item: IComplaint) => {
    try {
      // TODO: Implement delete functionality
      toastService.success('Xóa thành công')
    } catch (error) {
      toastService.handleError(error)
    }
  }

  const fakeData: IMission[] = [
    {
      id: 1,
      title: 'Kiểm tra',
      description: 'Mô tả A',
      type: '1',
      status: 'NEW',
      displayLocation: 'Hà Nội',
      customerCode: 'CUST0001',
      sapCode: 'SAP0001',
      customerName: 'Khách hàng 1',
      address: '63 Đường ABC, Quận 7',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Phạm Thị D',
      level: 'Cao',
      startDate: '2025-06-28',
      dueDate: '2025-06-19',
      endDate: '2025-07-09',
      checkInDate: '2025-06-22',
      checkOutTime: '2025-06-13',
      assignType: 'SINGLE'
    },
    {
      id: 2,
      title: 'Kiểm tra',
      description: 'Mô tả A',
      type: 'Loại 1',
      status: 'PROCESSING',
      displayLocation: 'Hà Nội',
      customerCode: 'CUST0002',
      sapCode: 'SAP0002',
      customerName: 'Khách hàng 2',
      address: '79 Đường ABC, Quận 9',
      supervisor: 'Lê Văn C',
      assignedEmployee: 'Lê Văn C',
      level: 'Thấp',
      startDate: '2025-05-17',
      dueDate: '2025-06-23',
      endDate: '2025-05-19',
      checkInDate: '2025-07-16 19:59',
      checkOutTime: '2025-07-15 18:54',
      assignType: 'GROUP'
    },
    {
      id: 3,
      title: 'Tư vấn',
      description: 'Mô tả D',
      type: 'Loại 3',
      status: 'COMPLETED',
      displayLocation: 'TP.HCM',
      customerCode: 'CUST0003',
      sapCode: 'SAP0003',
      customerName: 'Khách hàng 3',
      address: '30 Đường ABC, Quận 11',
      supervisor: 'Lê Văn C',
      assignedEmployee: 'Phạm Thị D',
      level: 'Trung bình',
      startDate: '2025-06-30',
      dueDate: '2025-07-04',
      endDate: '2025-05-21',
      checkInDate: '2025-05-23 09:16',
      checkOutTime: '2025-05-27 22:50',
      assignType: 'SINGLE'
    },
    {
      id: 4,
      title: 'Kiểm tra',
      description: 'Mô tả C',
      type: 'Loại 2',
      status: 'NEW',
      displayLocation: 'Đà Nẵng',
      customerCode: 'CUST0004',
      sapCode: 'SAP0004',
      customerName: 'Khách hàng 4',
      address: '44 Đường ABC, Quận 9',
      supervisor: 'Phạm Thị D',
      assignedEmployee: 'Nguyễn Văn A',
      level: 'Cao',
      startDate: '2025-06-14',
      dueDate: '2025-06-21',
      endDate: '2025-07-12',
      checkInDate: '2025-06-08 03:05',
      checkOutTime: '2025-06-02 04:50',
      assignType: 'SINGLE'
    },
    {
      id: 5,
      title: 'Kiểm tra',
      description: 'Mô tả C',
      type: 'Loại 1',
      status: 'COMPLETED',
      displayLocation: 'Hà Nội',
      customerCode: 'CUST0005',
      sapCode: 'SAP0005',
      customerName: 'Khách hàng 5',
      address: '92 Đường ABC, Quận 8',
      supervisor: 'Lê Văn C',
      assignedEmployee: 'Phạm Thị D',
      level: 'Trung bình',
      startDate: '2025-06-28',
      dueDate: '2025-06-12',
      endDate: '2025-05-21',
      checkInDate: '2025-06-20 19:27',
      checkOutTime: '2025-07-07 17:35',
      assignType: 'SINGLE'
    },
    {
      id: 6,
      title: 'Lắp đặt',
      description: 'Mô tả C',
      type: 'Loại 1',
      status: 'COMPLETED',
      displayLocation: 'Hà Nội',
      customerCode: 'CUST0006',
      sapCode: 'SAP0006',
      customerName: 'Khách hàng 6',
      address: '69 Đường ABC, Quận 10',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Trần Thị B',
      level: 'Thấp',
      startDate: '2025-06-11',
      dueDate: '2025-06-10',
      endDate: '2025-05-31',
      checkInDate: '2025-06-05 04:56',
      checkOutTime: '2025-07-12 08:16',
      assignType: 'SINGLE'
    },
    {
      id: 7,
      title: 'Tư vấn',
      description: 'Mô tả D',
      type: 'Loại 3',
      status: 'PROCESSING',
      displayLocation: 'Hà Nội',
      customerCode: 'CUST0007',
      sapCode: 'SAP0007',
      customerName: 'Khách hàng 7',
      address: '64 Đường ABC, Quận 3',
      supervisor: 'Lê Văn C',
      assignedEmployee: 'Nguyễn Văn A',
      level: 'Thấp',
      startDate: '2025-06-05',
      dueDate: '2025-06-11',
      endDate: '2025-06-01',
      checkInDate: '2025-07-12 20:00',
      checkOutTime: '2025-06-12 01:27',
      assignType: 'SINGLE'
    },
    {
      id: 8,
      title: 'Lắp đặt',
      description: 'Mô tả A',
      type: 'Loại 3',
      status: 'PROCESSING',
      displayLocation: 'Cần Thơ',
      customerCode: 'CUST0008',
      sapCode: 'SAP0008',
      customerName: 'Khách hàng 8',
      address: '21 Đường ABC, Quận 5',
      supervisor: 'Trần Thị B',
      assignedEmployee: 'Nguyễn Văn A',
      level: 'Trung bình',
      startDate: '2025-07-01',
      dueDate: '2025-06-26',
      endDate: '2025-06-25',
      checkInDate: '2025-06-07 01:41',
      checkOutTime: '2025-07-04 16:01',
      assignType: 'SINGLE'
    },
    {
      id: 9,
      title: 'Tư vấn',
      description: 'Mô tả C',
      type: 'Loại 3',
      status: 'COMPLETED',
      displayLocation: 'Cần Thơ',
      customerCode: 'CUST0009',
      sapCode: 'SAP0009',
      customerName: 'Khách hàng 9',
      address: '8 Đường ABC, Quận 6',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Nguyễn Văn A',
      level: 'Cao',
      startDate: '2025-07-02',
      dueDate: '2025-06-29',
      endDate: '2025-06-12',
      checkInDate: '2025-05-18 16:47',
      checkOutTime: '2025-05-27 15:38',
      assignType: 'SINGLE'
    },
    {
      id: 10,
      title: 'Tư vấn',
      description: 'Mô tả B',
      type: 'Loại 3',
      status: 'NEW',
      displayLocation: 'Cần Thơ',
      customerCode: 'CUST0010',
      sapCode: 'SAP0010',
      customerName: 'Khách hàng 10',
      address: '81 Đường ABC, Quận 6',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Trần Thị B',
      level: 'Thấp',
      startDate: '2025-05-22',
      dueDate: '2025-07-10',
      endDate: '2025-07-12',
      checkInDate: '2025-06-04 21:19',
      checkOutTime: '2025-05-27 13:21',
      assignType: 'SINGLE'
    },
    {
      id: 11,
      title: 'Khảo sát',
      description: 'Mô tả D',
      type: 'Loại 1',
      status: 'NEW',
      displayLocation: 'Cần Thơ',
      customerCode: 'CUST0011',
      sapCode: 'SAP0011',
      customerName: 'Khách hàng 11',
      address: '1 Đường ABC, Quận 3',
      supervisor: 'Lê Văn C',
      assignedEmployee: 'Lê Văn C',
      level: 'Thấp',
      startDate: '2025-05-28',
      dueDate: '2025-06-04',
      endDate: '2025-05-27',
      checkInDate: '2025-07-08 06:51',
      checkOutTime: '2025-05-23 19:38',
      assignType: 'SINGLE'
    },
    {
      id: 12,
      title: 'Bảo trì',
      description: 'Mô tả D',
      type: 'Loại 3',
      status: 'PROCESSING',
      displayLocation: 'TP.HCM',
      customerCode: 'CUST0012',
      sapCode: 'SAP0012',
      customerName: 'Khách hàng 12',
      address: '99 Đường ABC, Quận 1',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Phạm Thị D',
      level: 'Thấp',
      startDate: '2025-05-25',
      dueDate: '2025-06-23',
      endDate: '2025-06-30',
      checkInDate: '2025-05-21 06:15',
      checkOutTime: '2025-06-15 22:27',
      assignType: 'SINGLE'
    },
    {
      id: 13,
      title: 'Bảo trì',
      description: 'Mô tả A',
      type: 'Loại 1',
      status: 'PROCESSING',
      displayLocation: 'Đà Nẵng',
      customerCode: 'CUST0013',
      sapCode: 'SAP0013',
      customerName: 'Khách hàng 13',
      address: '7 Đường ABC, Quận 2',
      supervisor: 'Lê Văn C',
      assignedEmployee: 'Lê Văn C',
      level: 'Trung bình',
      startDate: '2025-06-12',
      dueDate: '2025-06-13',
      endDate: '2025-06-25',
      checkInDate: '2025-06-01 00:57',
      checkOutTime: '2025-06-26 11:23',
      assignType: 'SINGLE'
    },
    {
      id: 14,
      title: 'Khảo sát',
      description: 'Mô tả C',
      type: 'Loại 3',
      status: 'PROCESSING',
      displayLocation: 'Đà Nẵng',
      customerCode: 'CUST0014',
      sapCode: 'SAP0014',
      customerName: 'Khách hàng 14',
      address: '93 Đường ABC, Quận 7',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Trần Thị B',
      level: 'Cao',
      startDate: '2025-07-08',
      dueDate: '2025-07-10',
      endDate: '2025-07-15',
      checkInDate: '2025-06-09 17:31',
      checkOutTime: '2025-06-25 16:48',
      assignType: 'SINGLE'
    },
    {
      id: 15,
      title: 'Lắp đặt',
      description: 'Mô tả A',
      type: 'Loại 2',
      status: 'PROCESSING',
      displayLocation: 'TP.HCM',
      customerCode: 'CUST0015',
      sapCode: 'SAP0015',
      customerName: 'Khách hàng 15',
      address: '33 Đường ABC, Quận 12',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Phạm Thị D',
      level: 'Thấp',
      startDate: '2025-06-24',
      dueDate: '2025-06-22',
      endDate: '2025-06-02',
      checkInDate: '2025-06-02 07:27',
      checkOutTime: '2025-07-11 21:02',
      assignType: 'SINGLE'
    },
    {
      id: 16,
      title: 'Lắp đặt',
      description: 'Mô tả C',
      type: 'Loại 1',
      status: 'COMPLETED',
      displayLocation: 'Hà Nội',
      customerCode: 'CUST0016',
      sapCode: 'SAP0016',
      customerName: 'Khách hàng 16',
      address: '76 Đường ABC, Quận 12',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Lê Văn C',
      level: 'Thấp',
      startDate: '2025-05-29',
      dueDate: '2025-06-15',
      endDate: '2025-06-24',
      checkInDate: '2025-05-23 18:52',
      checkOutTime: '2025-07-03 00:26',
      assignType: 'SINGLE'
    },
    {
      id: 17,
      title: 'Kiểm tra',
      description: 'Mô tả A',
      type: 'Loại 3',
      status: 'COMPLETED',
      displayLocation: 'Cần Thơ',
      customerCode: 'CUST0017',
      sapCode: 'SAP0017',
      customerName: 'Khách hàng 17',
      address: '65 Đường ABC, Quận 7',
      supervisor: 'Trần Thị B',
      assignedEmployee: 'Lê Văn C',
      level: 'Trung bình',
      startDate: '2025-05-18',
      dueDate: '2025-05-19',
      endDate: '2025-06-05',
      checkInDate: '2025-07-10 01:14',
      checkOutTime: '2025-06-23 13:22',
      assignType: 'SINGLE'
    },
    {
      id: 18,
      title: 'Khảo sát',
      description: 'Mô tả C',
      type: 'Loại 1',
      status: 'COMPLETED',
      displayLocation: 'Cần Thơ',
      customerCode: 'CUST0018',
      sapCode: 'SAP0018',
      customerName: 'Khách hàng 18',
      address: '32 Đường ABC, Quận 9',
      supervisor: 'Lê Văn C',
      assignedEmployee: 'Trần Thị B',
      level: 'Cao',
      startDate: '2025-06-25',
      dueDate: '2025-05-21',
      endDate: '2025-07-04',
      checkInDate: '2025-07-15 03:53',
      checkOutTime: '2025-05-25 06:25',
      assignType: 'SINGLE'
    },
    {
      id: 19,
      title: 'Lắp đặt',
      description: 'Mô tả A',
      type: 'Loại 1',
      status: 'NEW',
      displayLocation: 'Cần Thơ',
      customerCode: 'CUST0019',
      sapCode: 'SAP0019',
      customerName: 'Khách hàng 19',
      address: '33 Đường ABC, Quận 6',
      supervisor: 'Nguyễn Văn A',
      assignedEmployee: 'Nguyễn Văn A',
      level: 'Trung bình',
      startDate: '2025-07-10',
      dueDate: '2025-05-19',
      endDate: '2025-06-18',
      checkInDate: '2025-06-26 12:51',
      checkOutTime: '2025-06-14 20:08',
      assignType: 'SINGLE'
    },
    {
      id: 20,
      title: 'Bảo trì',
      description: 'Mô tả A',
      type: 'Loại 3',
      status: 'NEW',
      displayLocation: 'TP.HCM',
      customerCode: 'CUST0020',
      sapCode: 'SAP0020',
      customerName: 'Khách hàng 20',
      address: '14 Đường ABC, Quận 9',
      supervisor: 'Trần Thị B',
      assignedEmployee: 'Trần Thị B',
      level: 'Cao',
      startDate: '2025-07-10',
      dueDate: '2025-07-15',
      endDate: '2025-06-26',
      checkInDate: '2025-07-12 11:33',
      checkOutTime: '2025-06-09 08:28',
      assignType: 'SINGLE'
    }

    // 👉 và tiếp tục tới id: 20
  ]

  const columns: any = [
    { title: 'ID', dataIndex: 'id', key: 'id', width: 80 },
    { title: 'Tiêu đề', dataIndex: 'title', key: 'title', width: 150 },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 200
    },
    { title: 'Loại', dataIndex: 'type', key: 'type', width: 100 },
    { title: 'Trạng thái', dataIndex: 'status', key: 'status', width: 120 },
    {
      title: 'Địa điểm trưng bày',
      dataIndex: 'displayLocation',
      key: 'displayLocation',
      width: 160
    },
    {
      title: 'Mã khách hàng',
      dataIndex: 'customerCode',
      key: 'customerCode',
      width: 130
    },
    { title: 'Mã SAP', dataIndex: 'sapCode', key: 'sapCode', width: 120 },
    {
      title: 'Khách hàng',
      dataIndex: 'customerName',
      key: 'customerName',
      width: 150
    },
    { title: 'Địa chỉ', dataIndex: 'address', key: 'address', width: 200 },
    {
      title: 'NV theo dõi/giám sát',
      dataIndex: 'supervisor',
      key: 'supervisor',
      width: 160
    },
    {
      title: 'NV được phân công',
      dataIndex: 'assignedEmployee',
      key: 'assignedEmployee',
      width: 160
    },
    { title: 'Mức độ', dataIndex: 'level', key: 'level', width: 100 },
    {
      title: 'Ngày bắt đầu',
      dataIndex: 'startDate',
      key: 'startDate',
      width: 130,
      render: (val) => dayjs(val).format('DD/MM/YYYY')
    },
    {
      title: 'Ngày đến hạn',
      dataIndex: 'dueDate',
      key: 'dueDate',
      width: 130,
      render: (val) => dayjs(val).format('DD/MM/YYYY')
    },
    {
      title: 'Ngày kết thúc',
      dataIndex: 'endDate',
      key: 'endDate',
      width: 130,
      render: (val) => dayjs(val).format('DD/MM/YYYY')
    },
    {
      title: 'Thời gian check in',
      dataIndex: 'checkInDate',
      key: 'checkInDate',
      width: 150,
      render: (val) => moment(val).format('DD/MM/YYYY')
    },
    {
      title: 'Thời gian check out',
      dataIndex: 'checkOutTime',
      key: 'checkOutTime',
      width: 150,
      render: (val) => dayjs(val).format('DD/MM/YYYY HH:mm')
    },
    {
      title: 'Chức năng',
      key: 'action',
      width: 150,
      fixed: 'right',
      render: (_, record: any) => (
        <>
          <DetailButton data={record} />
          <EditButton data={record} />
          <BaseButton
            danger
            type='primary'
            shape='circle'
            icon={<DeleteOutlined />}
            tooltip='Delete'
            onClick={() => handleDelete(record)}
          />
        </>
      )
    }
  ]

  return (
    <BaseView>
      <Row gutter={16}>
        <Col span={24}>
          <FilterProduct
            onFilter={handleFilter}
            onReset={handleReset}
            isLoading={false}
          />
        </Col>
      </Row>
      <Row gutter={16}>
        <Col span={24} style={{ marginTop: 16 }}>
          <BaseTable
            columns={columns}
            data={fakeData}
            total={0}
            isLoading={false}
            onPageChange={handlePageChange}
            scroll={{ x: 3000 }}
          />
        </Col>
      </Row>
    </BaseView>
  )
}
