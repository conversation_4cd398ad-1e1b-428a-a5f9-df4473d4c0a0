import { theme } from "antd";
import { ConfigProviderProps, ThemeConfig } from "antd/es/config-provider";
import {
  createContext,
  Dispatch,
  SetStateAction,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from "react";
import { COLORS } from "~/common/constants";

type IThemeMode = "default" | "compact";
type IThemeStyle = "light" | "dark";

type SetStateType<T> = Dispatch<SetStateAction<T>>;

// type SetStateType<T = any> = (value: T) => void;

interface IThemeStore {
  mode: IThemeMode;
  themeStyle: IThemeStyle;
  setThemeStyle: SetStateType<IThemeStyle>;
  themConfig: ThemeConfig;
  changeTheme: (themeOption: {
    mode?: "default" | "compact";
    themeStyle?: "light" | "dark";
  }) => void;
}

const ThemeStoreContext = createContext<IThemeStore | undefined>(undefined);

const colorPrimary = COLORS.PRIMARY_RGB;

const ThemeStoreProvider = ({ children }: { children: React.ReactNode }) => {
  const { token } = theme.useToken();
  const [mode, setMode] = useState<IThemeMode>("default");
  const [themeStyle, setThemeStyle] = useState<IThemeStyle>("light");
  const [themConfig, setThemConfig] = useState<ThemeConfig>({});

  useEffect(() => {
    const algorithm = [];
    if (themeStyle === "dark") {
      algorithm.push(theme.darkAlgorithm);
    } else {
      algorithm.push(theme.defaultAlgorithm);
    }
    if (mode === "compact") {
      algorithm.push(theme.compactAlgorithm);
    }

    setThemConfig({
      algorithm,
      token: {
        colorPrimary,
      },
      components: {
        Layout: {
          colorBgHeader: theme.defaultConfig.token.colorBgBase,
        },
      },
    });
  }, [mode, themeStyle, token]);

  const changeTheme = useCallback(
    (themeOption: {
      mode?: "default" | "compact";
      themeStyle?: "light" | "dark";
    }) => {
      if (!themeOption || Object.keys(themeOption).length === 0) {
        return;
      }
      const { mode = undefined, themeStyle = undefined } = themeOption;
      if (!mode && !themeStyle) {
        return;
      }
      if (mode) {
        setMode(mode);
      }
      if (themeStyle) {
        setThemeStyle(themeStyle);
      }
    },
    []
  );

  const values = useMemo(
    () => ({
      themeStyle,
      setThemeStyle,
      mode,
      themConfig,
      changeTheme,
    }),
    [changeTheme, mode, themConfig, themeStyle]
  );

  return (
    <ThemeStoreContext.Provider value={values}>
      {children}
    </ThemeStoreContext.Provider>
  );
};

const useThemeStore = () => {
  const context = useContext(ThemeStoreContext);
  if (context === undefined) {
    throw new Error(
      "useThemeStore hook must be used with a ThemeStoreContext component"
    );
  }
  return context;
};

export { ThemeStoreProvider, useThemeStore };
