import {
  IMission,
  ICustomerVisit,
  IActivitySegment
} from '../../dto/mission.dto'

export const mockMissions: IMission[] = [
  {
    id: '1',
    activityId: 'ACT001',
    title: '<PERSON><PERSON> vấn sản phẩm mới',
    description: '<PERSON><PERSON> vấn khách hàng về sản phẩm mới ra mắt',
    type: '<PERSON><PERSON> vấn',
    status: '<PERSON><PERSON><PERSON> thành',
    creator: '<PERSON>uy<PERSON>n Văn A',
    createdAt: '2024-01-15',
    customerCode: 'KH001',
    customerName: 'Công ty ABC',
    email: '<EMAIL>',
    phone: '0901234567'
  },
  {
    id: '2',
    activityId: 'ACT002',
    title: '<PERSON><PERSON><PERSON><PERSON> sát thị trường',
    description: '<PERSON>h<PERSON>o sát nhu cầu khách hàng tại khu vực miền <PERSON>',
    type: 'Kh<PERSON>o sát',
    status: '<PERSON><PERSON> thực hiện',
    creator: 'Trầ<PERSON>',
    createdAt: '2024-01-20',
    customerCode: 'KH002',
    customerName: 'Công ty XYZ',
    email: '<EMAIL>',
    phone: '0912345678'
  },
  {
    id: '3',
    activityId: 'ACT003',
    title: 'Đào tạo nhân viên',
    description: 'Đào tạo nhân viên về quy trình mới',
    type: 'Đào tạo',
    status: 'Chờ xử lý',
    creator: 'Lê Văn C',
    createdAt: '2024-01-25',
    customerCode: 'KH003',
    customerName: 'Công ty DEF',
    email: '<EMAIL>',
    phone: '0923456789'
  },
  {
    id: '4',
    activityId: 'ACT004',
    title: 'Bảo trì hệ thống',
    description: 'Bảo trì định kỳ hệ thống IT',
    type: 'Bảo trì',
    status: 'Hoàn thành',
    creator: 'Phạm Thị D',
    createdAt: '2024-02-01',
    customerCode: 'KH004',
    customerName: 'Công ty GHI',
    email: '<EMAIL>',
    phone: '0934567890'
  },
  {
    id: '5',
    activityId: 'ACT005',
    title: 'Triển khai dự án',
    description: 'Triển khai dự án CRM mới',
    type: 'Triển khai',
    status: 'Đang thực hiện',
    creator: 'Hoàng Văn E',
    createdAt: '2024-02-05',
    customerCode: 'KH005',
    customerName: 'Công ty JKL',
    email: '<EMAIL>',
    phone: '0945678901'
  },
  {
    id: '6',
    activityId: 'ACT006',
    title: 'Hỗ trợ kỹ thuật',
    description: 'Hỗ trợ khắc phục sự cố hệ thống',
    type: 'Hỗ trợ',
    status: 'Hoàn thành',
    creator: 'Vũ Thị F',
    createdAt: '2024-02-10',
    customerCode: 'KH006',
    customerName: 'Công ty MNO',
    email: '<EMAIL>',
    phone: '0956789012'
  },
  {
    id: '7',
    activityId: 'ACT007',
    title: 'Đánh giá hiệu suất',
    description: 'Đánh giá hiệu suất hệ thống hàng tháng',
    type: 'Đánh giá',
    status: 'Chờ xử lý',
    creator: 'Đặng Văn G',
    createdAt: '2024-02-15',
    customerCode: 'KH007',
    customerName: 'Công ty PQR',
    email: '<EMAIL>',
    phone: '0967890123'
  },
  {
    id: '8',
    activityId: 'ACT008',
    title: 'Cập nhật phần mềm',
    description: 'Cập nhật phiên bản mới của phần mềm',
    type: 'Cập nhật',
    status: 'Đang thực hiện',
    creator: 'Bùi Thị H',
    createdAt: '2024-02-20',
    customerCode: 'KH008',
    customerName: 'Công ty STU',
    email: '<EMAIL>',
    phone: '0978901234'
  }
]

export const mockCustomerVisits: ICustomerVisit[] = [
  {
    id: '1',
    activityId: 'VISIT001',
    title: 'Thăm hỏi Chăm sóc và phát triển',
    description: 'Thăm hỏi và tư vấn Chăm sóc và phát triển tại TP.HCM',
    type: 'Thăm hỏi',
    status: 'Hoàn thành',
    creator: 'Nguyễn Văn A',
    createdAt: '2024-01-10',
    customerCode: 'KH001',
    customerName: 'Công ty ABC',
    email: '<EMAIL>',
    phone: '0901234567'
  },
  {
    id: '2',
    activityId: 'VISIT002',
    title: 'Khảo sát nhu cầu',
    description: 'Khảo sát nhu cầu khách hàng tại Hà Nội',
    type: 'Khảo sát',
    status: 'Đang thực hiện',
    creator: 'Trần Thị B',
    createdAt: '2024-01-18',
    customerCode: 'KH002',
    customerName: 'Công ty XYZ',
    email: '<EMAIL>',
    phone: '0912345678'
  },
  {
    id: '3',
    activityId: 'VISIT003',
    title: 'Giới thiệu sản phẩm',
    description: 'Giới thiệu sản phẩm mới cho Tiềm năng',
    type: 'Giới thiệu',
    status: 'Chờ xử lý',
    creator: 'Lê Văn C',
    createdAt: '2024-01-25',
    customerCode: 'KH003',
    customerName: 'Công ty DEF',
    email: '<EMAIL>',
    phone: '0923456789'
  },
  {
    id: '4',
    activityId: 'VISIT004',
    title: 'Hỗ trợ sau bán hàng',
    description: 'Hỗ trợ khách hàng sau khi mua sản phẩm',
    type: 'Hỗ trợ',
    status: 'Hoàn thành',
    creator: 'Phạm Thị D',
    createdAt: '2024-02-02',
    customerCode: 'KH004',
    customerName: 'Công ty GHI',
    email: '<EMAIL>',
    phone: '0934567890'
  },
  {
    id: '5',
    activityId: 'VISIT005',
    title: 'Đánh giá mức độ hài lòng',
    description: 'Đánh giá mức độ hài lòng của khách hàng',
    type: 'Đánh giá',
    status: 'Đang thực hiện',
    creator: 'Hoàng Văn E',
    createdAt: '2024-02-08',
    customerCode: 'KH005',
    customerName: 'Công ty JKL',
    email: '<EMAIL>',
    phone: '0945678901'
  },
  {
    id: '6',
    activityId: 'VISIT006',
    title: 'Thăm hỏi định kỳ',
    description: 'Thăm hỏi định kỳ khách hàng thân thiết',
    type: 'Thăm hỏi',
    status: 'Hoàn thành',
    creator: 'Vũ Thị F',
    createdAt: '2024-02-12',
    customerCode: 'KH006',
    customerName: 'Công ty MNO',
    email: '<EMAIL>',
    phone: '0956789012'
  },
  {
    id: '7',
    activityId: 'VISIT007',
    title: 'Tư vấn nâng cấp',
    description: 'Tư vấn nâng cấp dịch vụ cho khách hàng',
    type: 'Tư vấn',
    status: 'Chờ xử lý',
    creator: 'Đặng Văn G',
    createdAt: '2024-02-18',
    customerCode: 'KH007',
    customerName: 'Công ty PQR',
    email: '<EMAIL>',
    phone: '0967890123'
  },
  {
    id: '8',
    activityId: 'VISIT008',
    title: 'Khắc phục sự cố',
    description: 'Khắc phục sự cố hệ thống cho khách hàng',
    type: 'Khắc phục',
    status: 'Đang thực hiện',
    creator: 'Bùi Thị H',
    createdAt: '2024-02-22',
    customerCode: 'KH008',
    customerName: 'Công ty STU',
    email: '<EMAIL>',
    phone: '0978901234'
  }
]

export const mockActivitySegments: IActivitySegment[] = [
  { month: 'T1/2024', count: 45, segment: 'Chăm sóc và phát triển' },
  { month: 'T1/2024', count: 32, segment: 'Cơ hội' },
  { month: 'T1/2024', count: 28, segment: 'Tiềm năng' },
  { month: 'T2/2024', count: 52, segment: 'Chăm sóc và phát triển' },
  { month: 'T2/2024', count: 38, segment: 'Cơ hội' },
  { month: 'T2/2024', count: 35, segment: 'Tiềm năng' },
  { month: 'T3/2024', count: 48, segment: 'Chăm sóc và phát triển' },
  { month: 'T3/2024', count: 41, segment: 'Cơ hội' },
  { month: 'T3/2024', count: 30, segment: 'Tiềm năng' },
  { month: 'T4/2024', count: 55, segment: 'Chăm sóc và phát triển' },
  { month: 'T4/2024', count: 44, segment: 'Cơ hội' },
  { month: 'T4/2024', count: 37, segment: 'Tiềm năng' },
  { month: 'T5/2024', count: 50, segment: 'Chăm sóc và phát triển' },
  { month: 'T5/2024', count: 39, segment: 'Cơ hội' },
  { month: 'T5/2024', count: 33, segment: 'Tiềm năng' },
  { month: 'T6/2024', count: 58, segment: 'Chăm sóc và phát triển' },
  { month: 'T6/2024', count: 46, segment: 'Cơ hội' },
  { month: 'T6/2024', count: 40, segment: 'Tiềm năng' }
]
