import React, { useMemo } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  ChartOptions
} from 'chart.js'
import { Bar } from 'react-chartjs-2'
import { IActivitySegment } from '../../dto/mission.dto'

ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend)

interface ActivityChartProps {
  data: IActivitySegment[]
  title?: string
  height?: number
}

const ActivityChart: React.FC<ActivityChartProps> = ({
  data,
  title = 'Số lượng hoạt động theo tháng',
  height = 400
}) => {
  // Xử lý dữ liệu để tạo biểu đồ
  const chartData = useMemo(() => {
    const months = [...new Set(data.map((item) => item.month))].sort()
    const segments = [...new Set(data.map((item) => item.segment))]

    // <PERSON><PERSON><PERSON><PERSON> đẹp hơn cho từng phân khúc
    const colorPalette = [
      { bg: 'rgba(54, 162, 235, 0.8)', border: 'rgba(54, 162, 235, 1)' },
      { bg: 'rgba(255, 99, 132, 0.8)', border: 'rgba(255, 99, 132, 1)' },
      { bg: 'rgba(75, 192, 192, 0.8)', border: 'rgba(75, 192, 192, 1)' },
      { bg: 'rgba(255, 206, 86, 0.8)', border: 'rgba(255, 206, 86, 1)' },
      { bg: 'rgba(153, 102, 255, 0.8)', border: 'rgba(153, 102, 255, 1)' },
      { bg: 'rgba(255, 159, 64, 0.8)', border: 'rgba(255, 159, 64, 1)' },
      { bg: 'rgba(199, 199, 199, 0.8)', border: 'rgba(199, 199, 199, 1)' },
      { bg: 'rgba(83, 102, 255, 0.8)', border: 'rgba(83, 102, 255, 1)' }
    ]

    return {
      labels: months,
      datasets: segments.map((segment, index) => {
        const color = colorPalette[index % colorPalette.length]
        return {
          label: segment,
          data: months.map((month) => {
            const item = data.find(
              (d) => d.month === month && d.segment === segment
            )
            return item ? item.count : 0
          }),
          backgroundColor: color.bg,
          borderColor: color.border,
          borderWidth: 2,
          borderRadius: 4,
          borderSkipped: false,
          hoverBackgroundColor: color.border,
          hoverBorderColor: color.border,
          hoverBorderWidth: 3
        }
      })
    }
  }, [data])

  const options: ChartOptions<'bar'> = useMemo(
    () => ({
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top' as const,
          labels: {
            usePointStyle: true,
            padding: 20,
            font: {
              size: 12,
              weight: 'normal'
            }
          }
        },
        title: {
          display: true,
          text: title,
          font: {
            size: 18,
            weight: 'bold'
          },
          padding: {
            top: 10,
            bottom: 20
          },
          color: '#333'
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: '#fff',
          bodyColor: '#fff',
          borderColor: 'rgba(255, 255, 255, 0.1)',
          borderWidth: 1,
          cornerRadius: 8,
          displayColors: true,
          callbacks: {
            title: (context) => `Tháng: ${context[0].label}`,
            label: (context) =>
              `${context.dataset.label}: ${context.parsed.y} hoạt động`
          }
        }
      },
      scales: {
        y: {
          beginAtZero: true,
          title: {
            display: true,
            text: 'Số lượng hoạt động',
            font: {
              size: 14,
              weight: 'normal'
            },
            color: '#666'
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)',
            drawBorder: false
          },
          ticks: {
            font: {
              size: 12
            },
            color: '#666'
          }
        },
        x: {
          title: {
            display: true,
            text: 'Tháng',
            font: {
              size: 14,
              weight: 'normal'
            },
            color: '#666'
          },
          grid: {
            display: false
          },
          ticks: {
            font: {
              size: 12
            },
            color: '#666'
          }
        }
      },
      interaction: {
        intersect: false,
        mode: 'index' as const
      },
      animation: {
        duration: 1000,
        easing: 'easeInOutQuart'
      }
    }),
    [title]
  )

  return (
    <div
      style={{
        backgroundColor: 'white',
        borderRadius: '12px',
        border: '1px solid rgba(0,0,0,0.05)',
        height: `${height}px`,
        width: '100%'
      }}>
      <Bar data={chartData} options={options} />
    </div>
  )
}

export default ActivityChart
