import React from 'react'
import { Button, Tooltip } from 'antd'
import type { ButtonType } from 'antd/es/button'

interface BaseButtonProps {
  icon?: React.ReactNode
  tooltip?: string
  onClick: () => void
  type?: ButtonType
  danger?: boolean
  children?: React.ReactNode
  shape?: 'circle' | 'round'
  htmlType?: 'submit' | 'reset' | 'button'
  loading?: boolean
}

const BaseButton: React.FC<BaseButtonProps> = ({
  icon,
  tooltip = '',
  onClick,
  type = 'text',
  danger = false,
  children,
  loading
}) => {
  const BtnComponent = (
    <Button
      type={type}
      icon={icon}
      onClick={onClick}
      danger={danger}
      style={{ padding: '4px', marginRight: '4px' }}
      loading={loading}>
      {!icon && children}
    </Button>
  )

  return tooltip ? (
    <Tooltip title={tooltip}>{BtnComponent}</Tooltip>
  ) : (
    BtnComponent
  )
}

export default BaseButton
